<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单聊天测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 2px solid #e2e8f0;
        }
        
        .test-section {
            margin-bottom: 24px;
            padding: 16px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: #f8fafc;
        }
        
        .test-button {
            display: inline-block;
            padding: 12px 24px;
            background: #3b82f6;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 8px 8px 8px 0;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            background: #2563eb;
            transform: translateY(-2px);
        }
        
        .status {
            padding: 6px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 8px;
        }
        
        .status.success { background: #dcfce7; color: #166534; }
        .status.error { background: #fef2f2; color: #dc2626; }
        .status.pending { background: #fef3c7; color: #d97706; }
        
        .api-result {
            background: #1e293b;
            color: #e2e8f0;
            padding: 16px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            margin: 12px 0;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .url-box {
            background: #f3f4f6;
            padding: 12px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
            margin: 8px 0;
            border-left: 4px solid #3b82f6;
        }
        
        .step-list {
            list-style: none;
            padding: 0;
            margin: 16px 0;
        }
        
        .step-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
            position: relative;
            padding-left: 32px;
        }
        
        .step-list li:before {
            content: counter(step-counter);
            counter-increment: step-counter;
            position: absolute;
            left: 0;
            top: 8px;
            background: #3b82f6;
            color: white;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        
        .step-list {
            counter-reset: step-counter;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 简单聊天页面测试</h1>
            <p>快速测试统一聊天页面的加载和功能</p>
        </div>
        
        <div class="test-section">
            <h3>🎯 测试目标</h3>
            <p>验证统一聊天页面是否能正常加载和显示内容</p>
            <div class="url-box">
                目标URL: http://*************:3000/utilities/daily/unified-chat?agent_id=e55f5e84-6d8b-4265-8e55-728bdb0d2455
            </div>
        </div>
        
        <div class="test-section">
            <h3>🚀 快速测试</h3>
            <a href="http://*************:3000/utilities/daily/unified-chat?agent_id=e55f5e84-6d8b-4265-8e55-728bdb0d2455" 
               class="test-button" target="_blank" onclick="trackClick('main-test')">
                打开统一聊天页面
            </a>
            <span id="main-test-status" class="status pending">待测试</span>
            
            <br><br>
            
            <a href="http://*************:3000/utilities/daily/unified-chat?agent_id=test-agent&demo=1" 
               class="test-button" target="_blank" onclick="trackClick('demo-test')">
                演示模式测试
            </a>
            <span id="demo-test-status" class="status pending">待测试</span>
        </div>
        
        <div class="test-section">
            <h3>🔍 API验证</h3>
            <button onclick="testAPI()" class="test-button">测试后端API</button>
            <span id="api-status" class="status pending">未测试</span>
            <div id="api-result" class="api-result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>📋 测试步骤</h3>
            <ol class="step-list">
                <li>点击"测试后端API"按钮，确认API正常响应</li>
                <li>点击"打开统一聊天页面"，观察页面是否正常加载</li>
                <li>检查页面右上角是否显示调试信息</li>
                <li>打开浏览器开发者工具，查看控制台错误</li>
                <li>检查网络请求是否成功</li>
                <li>如果页面空白，尝试刷新页面</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h3>🔧 常见问题解决</h3>
            <ul>
                <li><strong>页面完全空白</strong> - 检查JavaScript错误，可能是组件加载失败</li>
                <li><strong>显示加载状态但不消失</strong> - API请求可能失败或超时</li>
                <li><strong>显示错误信息</strong> - 检查错误内容，通常是数据获取失败</li>
                <li><strong>网络请求404</strong> - 检查API路径是否正确</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>📊 预期结果</h3>
            <ul>
                <li>✅ 页面正常加载，显示聊天界面</li>
                <li>✅ 右上角显示调试信息（Agent ID、组件名等）</li>
                <li>✅ 左侧显示智能体信息</li>
                <li>✅ 中间显示聊天区域</li>
                <li>✅ 底部显示输入框</li>
                <li>✅ 无JavaScript错误</li>
            </ul>
        </div>
    </div>
    
    <script>
        function trackClick(testType) {
            const statusElement = document.getElementById(testType + '-status');
            statusElement.textContent = '已点击';
            statusElement.className = 'status pending';
            
            // 3秒后提示用户检查结果
            setTimeout(() => {
                const result = confirm('页面是否正常加载？\n点击"确定"表示成功，"取消"表示失败');
                if (result) {
                    statusElement.textContent = '✅ 成功';
                    statusElement.className = 'status success';
                } else {
                    statusElement.textContent = '❌ 失败';
                    statusElement.className = 'status error';
                }
            }, 3000);
        }
        
        async function testAPI() {
            const statusElement = document.getElementById('api-status');
            const resultElement = document.getElementById('api-result');
            
            statusElement.textContent = '测试中...';
            statusElement.className = 'status pending';
            resultElement.style.display = 'block';
            resultElement.textContent = '正在测试API...\n';
            
            try {
                // 测试智能体详情API
                resultElement.textContent += '测试智能体详情API...\n';
                const response = await fetch('http://*************:8000/api/v1/agents/e55f5e84-6d8b-4265-8e55-728bdb0d2455');
                const data = await response.json();
                
                if (response.ok) {
                    resultElement.textContent += `✅ 智能体详情API正常 (${response.status})\n`;
                    resultElement.textContent += `   名称: ${data.name || 'N/A'}\n`;
                    resultElement.textContent += `   类型: ${data.agent_type || 'N/A'}\n`;
                    resultElement.textContent += `   描述: ${(data.description || 'N/A').substring(0, 50)}...\n`;
                } else {
                    resultElement.textContent += `❌ 智能体详情API失败 (${response.status})\n`;
                    resultElement.textContent += `   错误: ${JSON.stringify(data)}\n`;
                }
                
                // 测试可用模型API
                resultElement.textContent += '\n测试可用模型API...\n';
                const modelsResponse = await fetch('http://*************:8000/api/v1/agents/available-models');
                const modelsData = await modelsResponse.json();
                
                if (modelsResponse.ok && modelsData.success) {
                    resultElement.textContent += `✅ 可用模型API正常 (${modelsResponse.status})\n`;
                    resultElement.textContent += `   模型数量: ${modelsData.models?.length || 0}\n`;
                } else {
                    resultElement.textContent += `❌ 可用模型API失败 (${modelsResponse.status})\n`;
                }
                
                statusElement.textContent = '✅ API正常';
                statusElement.className = 'status success';
                
            } catch (error) {
                resultElement.textContent += `❌ API测试失败: ${error.message}\n`;
                statusElement.textContent = '❌ API异常';
                statusElement.className = 'status error';
            }
        }
        
        // 页面加载完成后的提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 简单聊天测试页面已加载');
            console.log('请按照测试步骤进行验证');
        });
    </script>
</body>
</html>
