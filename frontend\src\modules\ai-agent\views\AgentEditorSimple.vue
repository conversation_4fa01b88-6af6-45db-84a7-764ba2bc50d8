<template>
  <div class="agent-editor-simple">
    <!-- 简化的头部 -->
    <div class="editor-header">
      <button @click="goBack" class="back-btn">
        ← 返回市场
      </button>
      <h1>{{ isEditing ? '编辑智能体' : '创建智能体' }}</h1>
    </div>

    <!-- 简化的表单 -->
    <div class="editor-content">
      <form @submit.prevent="handleSubmit" class="agent-form">
        <!-- 基本信息 -->
        <div class="form-section">
          <h3>基本信息</h3>
          
          <div class="form-group">
            <label>智能体名称 *</label>
            <input 
              v-model="formData.name" 
              type="text" 
              placeholder="给你的智能体起个名字"
              required
              class="form-input"
            />
          </div>
          
          <div class="form-group">
            <label>智能体类型 *</label>
            <div class="type-grid">
              <div 
                v-for="type in agentTypes" 
                :key="type.id"
                :class="['type-card', { selected: formData.agent_type === type.id }]"
                @click="selectType(type.id)"
              >
                <div class="type-icon">{{ type.icon }}</div>
                <div class="type-name">{{ type.name }}</div>
                <div class="type-category">{{ type.category }}</div>
              </div>
            </div>
          </div>
          
          <div class="form-group">
            <label>描述 *</label>
            <textarea 
              v-model="formData.description" 
              placeholder="描述你的智能体能做什么..."
              required
              class="form-textarea"
              rows="3"
            ></textarea>
          </div>
        </div>

        <!-- 高级设置（可选） -->
        <div class="form-section">
          <h3>高级设置 <span class="optional">(可选)</span></h3>
          
          <div class="form-group">
            <label>系统提示词</label>
            <textarea 
              v-model="formData.system_prompt" 
              placeholder="自定义系统提示词，留空使用默认设置..."
              class="form-textarea"
              rows="4"
            ></textarea>
          </div>
          
          <!-- 语言学习特殊设置 -->
          <div v-if="isLanguageLearning" class="form-group">
            <label>教学语言</label>
            <select v-model="formData.learning_language" class="form-select">
              <option value="">选择语言</option>
              <option value="english">英语</option>
              <option value="japanese">日语</option>
              <option value="french">法语</option>
              <option value="german">德语</option>
              <option value="spanish">西班牙语</option>
            </select>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <button type="button" @click="goBack" class="btn-secondary">
            取消
          </button>
          <button type="submit" :disabled="!isFormValid || saving" class="btn-primary">
            {{ saving ? '保存中...' : (isEditing ? '更新智能体' : '创建智能体') }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import { getAllAgentTypes, getAgentTypeConfig } from '@/config/agentTypes.js'
import agentService from '../services/agentService.js'

export default {
  name: 'AgentEditorSimple',
  
  setup() {
    const router = useRouter()
    const route = useRoute()
    
    // 状态
    const saving = ref(false)
    const agentTypes = ref([])
    const formData = ref({
      name: '',
      agent_type: '',
      description: '',
      system_prompt: '',
      learning_language: ''
    })
    
    // 计算属性
    const isEditing = computed(() => !!route.query.id)
    
    const isLanguageLearning = computed(() => {
      return formData.value.agent_type === 'language-learning' || 
             formData.value.agent_type === 'language_learning'
    })
    
    const isFormValid = computed(() => {
      return formData.value.name.trim() && 
             formData.value.agent_type && 
             formData.value.description.trim()
    })
    
    // 方法
    const loadAgentTypes = () => {
      try {
        agentTypes.value = getAllAgentTypes()
        console.log('加载智能体类型:', agentTypes.value.length)
      } catch (error) {
        console.error('加载智能体类型失败:', error)
        message.error('加载智能体类型失败')
      }
    }
    
    const selectType = (typeId) => {
      formData.value.agent_type = typeId
      
      // 根据类型设置默认描述
      const typeConfig = getAgentTypeConfig(typeId)
      if (typeConfig && !formData.value.description) {
        formData.value.description = typeConfig.description
      }
      
      console.log('选择智能体类型:', typeId)
    }
    
    const loadAgent = async () => {
      if (!isEditing.value) return

      try {
        const agentId = route.query.id
        console.log('加载智能体:', agentId)

        // 如果是本地智能体，从localStorage加载
        if (agentId.startsWith('local-')) {
          const localAgents = JSON.parse(localStorage.getItem('local_agents') || '[]')
          const agent = localAgents.find(a => a.id === agentId)

          if (agent) {
            formData.value = {
              name: agent.name || '',
              agent_type: agent.agent_type || '',
              description: agent.description || '',
              system_prompt: agent.system_prompt || '',
              learning_language: agent.learning_settings?.language || ''
            }
            console.log('从本地加载智能体:', agent.name)
            return
          }
        }

        // 从API加载
        const response = await agentService.getAgentDetail(agentId)
        if (response && response.data) {
          const agent = response.data
          formData.value = {
            name: agent.name || '',
            agent_type: agent.agent_type || '',
            description: agent.description || '',
            system_prompt: agent.system_prompt || '',
            learning_language: agent.learning_settings?.language || ''
          }
          console.log('从API加载智能体:', agent.name)
        } else {
          console.warn('未找到智能体数据')
          message.error('未找到智能体数据')
        }
      } catch (error) {
        console.error('加载智能体失败:', error)
        message.error('加载智能体失败')
      }
    }
    
    const handleSubmit = async () => {
      if (!isFormValid.value) {
        message.warning('请填写必填字段')
        return
      }
      
      try {
        saving.value = true
        console.log('保存智能体:', formData.value)
        
        const submitData = {
          name: formData.value.name.trim(),
          agent_type: formData.value.agent_type,
          description: formData.value.description.trim(),
          system_prompt: formData.value.system_prompt.trim() || undefined,
          learning_settings: isLanguageLearning.value ? {
            language: formData.value.learning_language
          } : undefined
        }
        
        let response
        const agentId = route.query.id

        // 如果是编辑本地智能体
        if (isEditing.value && agentId && agentId.startsWith('local-')) {
          const localAgents = JSON.parse(localStorage.getItem('local_agents') || '[]')
          const agentIndex = localAgents.findIndex(a => a.id === agentId)

          if (agentIndex !== -1) {
            localAgents[agentIndex] = {
              ...localAgents[agentIndex],
              ...submitData,
              updated_at: new Date().toISOString()
            }
            localStorage.setItem('local_agents', JSON.stringify(localAgents))
            message.success('智能体更新成功')
            router.push('/utilities/daily/agent-marketplace')
            return
          }
        }

        // API操作
        if (isEditing.value) {
          response = await agentService.updateAgent(agentId, submitData)
        } else {
          response = await agentService.createAgent(submitData)
        }
        
        if (response) {
          message.success(isEditing.value ? '智能体更新成功' : '智能体创建成功')
          router.push('/utilities/daily/agent-marketplace')
        }
      } catch (error) {
        console.error('保存智能体失败:', error)
        
        // 处理认证错误
        if (error.response?.status === 401) {
          console.warn('认证失败，使用离线模式')
          message.warning('当前为演示模式，智能体已保存到本地')

          // 保存到本地存储作为演示
          const localAgents = JSON.parse(localStorage.getItem('local_agents') || '[]')
          const newAgent = {
            id: 'local-' + Date.now(),
            ...submitData,
            created_at: new Date().toISOString()
          }
          localAgents.push(newAgent)
          localStorage.setItem('local_agents', JSON.stringify(localAgents))

          router.push('/utilities/daily/agent-marketplace')
        } else {
          message.error('保存失败：' + (error.response?.data?.detail || error.message))
        }
      } finally {
        saving.value = false
      }
    }
    
    const goBack = () => {
      router.push('/utilities/daily/agent-marketplace')
    }
    
    // 生命周期
    onMounted(() => {
      loadAgentTypes()
      loadAgent()
    })
    
    return {
      saving,
      agentTypes,
      formData,
      isEditing,
      isLanguageLearning,
      isFormValid,
      selectType,
      handleSubmit,
      goBack
    }
  }
}
</script>

<style scoped>
.agent-editor-simple {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.editor-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e2e8f0;
}

.back-btn {
  padding: 8px 16px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s;
}

.back-btn:hover {
  background: #e2e8f0;
  color: #475569;
}

.editor-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
}

.editor-content {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.form-section {
  margin-bottom: 32px;
}

.form-section h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.optional {
  font-size: 14px;
  font-weight: 400;
  color: #64748b;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.2s;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.type-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.type-card {
  padding: 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
}

.type-card:hover {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.type-card.selected {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
}

.type-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.type-name {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.type-category {
  font-size: 12px;
  color: #64748b;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 24px;
  border-top: 1px solid #e2e8f0;
}

.btn-primary,
.btn-secondary {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-secondary {
  background: white;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.btn-secondary:hover {
  background: #f8fafc;
  color: #475569;
}

@media (max-width: 768px) {
  .agent-editor-simple {
    padding: 15px;
  }
  
  .type-grid {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>
