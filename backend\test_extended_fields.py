#!/usr/bin/env python3
"""
测试扩展字段（头像、个性等）的更新和加载
"""

import requests
import json
import time

def test_extended_fields_update():
    """测试扩展字段更新"""
    agent_id = "e55f5e84-6d8b-4265-8e55-728bdb0d2455"
    
    print("🎭 测试扩展字段更新（头像、个性、领域等）")
    print("=" * 60)
    
    # 创建包含所有扩展字段的测试数据
    timestamp = int(time.time())
    test_data = {
        # 基本字段
        "name": f"完整测试智能体-{timestamp}",
        "description": f"包含所有扩展字段的测试，时间戳：{timestamp}",
        "systemPrompt": f"你是一个完整配置的测试助手，时间：{timestamp}",
        
        # 扩展字段（这些之前没有被保存）
        "avatar": "🚀",  # 头像
        "personality": "professional",  # 个性
        "domain": "technology",  # 领域
        "language": "zh-en",  # 语言
        
        # 复杂配置
        "memory": {
            "short_term": {"enabled": True, "duration": "session"},
            "long_term": {"enabled": True, "strategy": "important"},
            "semantic": {"enabled": True, "learning": "active"}
        },
        "advanced": {
            "creativity": 80,
            "professionalism": 90,
            "responseLength": "detailed"
        },
        "safety": {
            "contentFilter": True,
            "privacyProtection": True,
            "accessControl": "restricted"
        },
        
        # 基本配置
        "capabilities": [
            {"type": "translation", "config": {}},
            {"type": "web_search", "config": {}},
            {"type": "code_generation", "config": {}}
        ],
        "knowledge": [],
        "maxTokens": 8000,
        "temperature": 0.8
    }
    
    print(f"📝 测试数据包含的扩展字段:")
    print(f"  头像: {test_data['avatar']}")
    print(f"  个性: {test_data['personality']}")
    print(f"  领域: {test_data['domain']}")
    print(f"  语言: {test_data['language']}")
    print(f"  高级设置: {test_data['advanced']}")
    
    try:
        # 1. 更新智能体（包含所有扩展字段）
        update_url = f"http://localhost:8000/api/v1/agents/{agent_id}"
        print(f"\n🚀 发送更新请求到: {update_url}")
        
        response = requests.put(update_url, json=test_data, timeout=15)
        
        print(f"📊 更新响应状态: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 更新成功: {result.get('message')}")
        else:
            print(f"❌ 更新失败: {response.text}")
            return False
            
        # 等待数据库更新完成
        time.sleep(2)
        
        # 2. 重新加载智能体数据，检查扩展字段
        get_url = f"http://localhost:8000/api/v1/agents/{agent_id}"
        print(f"\n🔍 重新加载数据: {get_url}")
        
        response = requests.get(get_url, timeout=15)
        
        print(f"📊 加载响应状态: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                agent = result.get('agent', {})
                
                print(f"\n✅ 数据加载成功!")
                print(f"📄 基本信息:")
                print(f"  名称: {agent.get('name')}")
                print(f"  描述: {agent.get('description', '')[:50]}...")
                
                print(f"\n🎭 扩展字段验证:")
                print(f"  头像: {agent.get('avatar', '未设置')} {'✅' if agent.get('avatar') == test_data['avatar'] else '❌'}")
                print(f"  个性: {agent.get('personality', '未设置')} {'✅' if agent.get('personality') == test_data['personality'] else '❌'}")
                print(f"  领域: {agent.get('domain', '未设置')} {'✅' if agent.get('domain') == test_data['domain'] else '❌'}")
                print(f"  语言: {agent.get('language', '未设置')} {'✅' if agent.get('language') == test_data['language'] else '❌'}")
                
                print(f"\n⚙️ 高级配置验证:")
                advanced = agent.get('advanced', {})
                print(f"  创造力: {advanced.get('creativity', '未设置')} {'✅' if advanced.get('creativity') == test_data['advanced']['creativity'] else '❌'}")
                print(f"  专业性: {advanced.get('professionalism', '未设置')} {'✅' if advanced.get('professionalism') == test_data['advanced']['professionalism'] else '❌'}")
                
                # 验证所有扩展字段是否正确保存和加载
                success_count = 0
                total_count = 4
                
                if agent.get('avatar') == test_data['avatar']:
                    success_count += 1
                if agent.get('personality') == test_data['personality']:
                    success_count += 1
                if agent.get('domain') == test_data['domain']:
                    success_count += 1
                if agent.get('language') == test_data['language']:
                    success_count += 1
                
                print(f"\n📊 扩展字段验证结果: {success_count}/{total_count} 成功")
                
                if success_count == total_count:
                    print("🎉 所有扩展字段都正确保存和加载！")
                    return True
                else:
                    print("⚠️ 部分扩展字段未正确保存")
                    return False
                    
            else:
                print(f"❌ 数据加载失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 请求失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        return False

def test_basic_fields_still_work():
    """确保基本字段仍然正常工作"""
    agent_id = "e55f5e84-6d8b-4265-8e55-728bdb0d2455"
    
    print("\n📋 验证基本字段仍然正常工作")
    print("=" * 40)
    
    try:
        # 获取当前数据
        get_url = f"http://localhost:8000/api/v1/agents/{agent_id}"
        response = requests.get(get_url, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                agent = result.get('agent', {})
                
                print(f"✅ 基本字段验证:")
                print(f"  ID: {agent.get('id', '未设置')}")
                print(f"  名称: {agent.get('name', '未设置')}")
                print(f"  类型: {agent.get('agent_type', '未设置')}")
                print(f"  状态: {agent.get('status', '未设置')}")
                print(f"  最大令牌: {agent.get('max_tokens', '未设置')}")
                print(f"  温度: {agent.get('temperature', '未设置')}")
                
                return True
            else:
                print(f"❌ 获取失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 请求失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 验证出错: {e}")
        return False

if __name__ == "__main__":
    print("🧪 开始测试扩展字段功能")
    print("=" * 70)
    
    # 测试扩展字段更新
    extended_test = test_extended_fields_update()
    
    # 验证基本字段
    basic_test = test_basic_fields_still_work()
    
    print("\n" + "=" * 70)
    print("📊 测试结果汇总:")
    print(f"  扩展字段更新: {'✅ 成功' if extended_test else '❌ 失败'}")
    print(f"  基本字段验证: {'✅ 成功' if basic_test else '❌ 失败'}")
    
    if extended_test and basic_test:
        print("\n🎉 所有测试通过！扩展字段功能完全正常！")
        print("💡 现在头像、个性、领域、语言等设置都会被正确保存和加载")
    else:
        print("\n⚠️ 部分测试失败，请检查相关功能")
