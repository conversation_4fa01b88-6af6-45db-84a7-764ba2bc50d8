#!/usr/bin/env python3
"""
修复知识库表结构
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.database import get_db_manager

def fix_knowledge_table():
    """修复知识库表结构"""
    
    print("🔧 修复知识库表结构")
    print("=" * 40)
    
    try:
        db_manager = get_db_manager()
        
        # 1. 修改knowledge_base_id字段，允许NULL
        print("📝 修改knowledge_base_id字段允许NULL...")
        alter_sql = """
        ALTER TABLE knowledge_documents 
        ALTER COLUMN knowledge_base_id DROP NOT NULL
        """
        
        db_manager.execute_query(alter_sql)
        print("✅ knowledge_base_id字段修改成功")
        
        # 2. 验证表结构
        print("🔍 验证表结构...")
        columns_sql = """
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns
        WHERE table_name = 'knowledge_documents'
        AND column_name = 'knowledge_base_id'
        """
        
        columns = db_manager.execute_query(columns_sql)
        if columns:
            col = columns[0]
            nullable = "NULL" if col['is_nullable'] == 'YES' else "NOT NULL"
            print(f"✅ knowledge_base_id: {col['data_type']} ({nullable})")
            
            if col['is_nullable'] == 'YES':
                print("🎉 字段修改成功，现在允许NULL值")
                return True
            else:
                print("❌ 字段仍然是NOT NULL")
                return False
        else:
            print("❌ 无法获取字段信息")
            return False
            
    except Exception as e:
        print(f"❌ 修复表结构失败: {e}")
        return False

def test_insert_with_null():
    """测试使用NULL值插入"""
    
    print("\n🧪 测试使用NULL值插入")
    print("=" * 30)
    
    try:
        db_manager = get_db_manager()
        
        import uuid
        import json
        from datetime import datetime
        
        # 生成测试数据
        doc_id = str(uuid.uuid4())
        timestamp = datetime.now().isoformat()
        
        test_data = {
            'id': doc_id,
            'knowledge_base_id': None,  # 使用NULL
            'title': 'null_test_document.txt',
            'content': '这是使用NULL knowledge_base_id的测试文档',
            'content_type': 'txt',
            'doc_metadata': json.dumps({
                'summary': 'NULL测试',
                'type': 'txt',
                'size': 100
            }),
            'created_at': timestamp
        }
        
        print(f"📝 插入测试数据 (knowledge_base_id = NULL)...")
        
        insert_sql = """
        INSERT INTO knowledge_documents 
        (id, knowledge_base_id, title, content, content_type, doc_metadata, created_at)
        VALUES (%s, %s, %s, %s, %s, %s, %s) 
        RETURNING id
        """
        
        result = db_manager.execute_query(
            insert_sql,
            [
                test_data['id'],
                test_data['knowledge_base_id'],  # NULL
                test_data['title'],
                test_data['content'],
                test_data['content_type'],
                test_data['doc_metadata'],
                test_data['created_at']
            ]
        )
        
        if result:
            inserted_id = result[0]['id'] if isinstance(result[0], dict) else result[0][0]
            print(f"✅ 插入成功，ID: {inserted_id}")
            
            # 验证插入
            verify_sql = "SELECT * FROM knowledge_documents WHERE id = %s"
            verify_result = db_manager.execute_query(verify_sql, [inserted_id])
            
            if verify_result:
                doc = verify_result[0]
                print(f"✅ 验证成功:")
                print(f"  ID: {doc.get('id')}")
                print(f"  标题: {doc.get('title')}")
                print(f"  knowledge_base_id: {doc.get('knowledge_base_id')}")
                print(f"  创建时间: {doc.get('created_at')}")
                return True
            else:
                print("❌ 验证失败")
                return False
        else:
            print("❌ 插入失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试插入失败: {e}")
        return False

if __name__ == "__main__":
    print("🔧 知识库表结构修复工具")
    print("=" * 50)
    
    # 修复表结构
    fix_success = fix_knowledge_table()
    
    if fix_success:
        # 测试插入
        test_success = test_insert_with_null()
        
        print("\n" + "=" * 50)
        print("📊 修复结果:")
        print(f"  表结构修复: {'✅ 成功' if fix_success else '❌ 失败'}")
        print(f"  NULL值插入测试: {'✅ 成功' if test_success else '❌ 失败'}")
        
        if fix_success and test_success:
            print("\n🎉 知识库表结构修复完成！")
            print("💡 现在可以正常保存文档到知识库了")
        else:
            print("\n⚠️ 部分修复失败")
    else:
        print("\n❌ 表结构修复失败，无法继续测试")
