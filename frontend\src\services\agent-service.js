/**
 * AI智能体服务
 */

import api from './api.js'

class AgentService {
  /**
   * 获取智能体列表
   */
  async getAgentList(params = {}) {
    try {
      const response = await api.get('/api/v1/agents/list', { params })
      return response.data
    } catch (error) {
      console.error('获取智能体列表失败:', error)
      throw error
    }
  }

  /**
   * 获取智能体详情
   */
  async getAgentDetail(agentId) {
    try {
      const response = await api.get(`/api/v1/agents/${agentId}`)
      return response.data
    } catch (error) {
      console.error('获取智能体详情失败:', error)
      throw error
    }
  }

  /**
   * 创建智能体
   */
  async createAgent(agentData) {
    try {
      const response = await api.post('/api/v1/agents', agentData)
      return response.data
    } catch (error) {
      console.error('创建智能体失败:', error)
      throw error
    }
  }

  /**
   * 更新智能体
   */
  async updateAgent(agentId, agentData) {
    try {
      const response = await api.put(`/api/v1/agents/${agentId}`, agentData)
      return response.data
    } catch (error) {
      console.error('更新智能体失败:', error)
      throw error
    }
  }

  /**
   * 删除智能体
   */
  async deleteAgent(agentId) {
    try {
      const response = await api.delete(`/api/v1/agents/${agentId}`)
      return response.data
    } catch (error) {
      console.error('删除智能体失败:', error)
      throw error
    }
  }

  /**
   * 获取可用模型列表
   */
  async getAvailableModels() {
    try {
      const response = await api.get('/api/v1/agents/available-models')
      return response.data
    } catch (error) {
      console.error('获取可用模型失败:', error)
      throw error
    }
  }

  /**
   * 获取智能体模型配置
   */
  async getModelConfig(agentId) {
    try {
      const response = await api.get(`/api/v1/agents/${agentId}/model-config`)
      return response.data
    } catch (error) {
      console.error('获取模型配置失败:', error)
      throw error
    }
  }

  /**
   * 更新智能体模型配置
   */
  async updateModelConfig(agentId, config) {
    try {
      const response = await api.put(`/api/v1/agents/${agentId}/model-config`, config)
      return response.data
    } catch (error) {
      console.error('更新模型配置失败:', error)
      throw error
    }
  }

  /**
   * 与智能体对话
   */
  async chat(agentId, message, options = {}) {
    try {
      const response = await api.post(`/api/v1/agents/${agentId}/chat`, {
        message,
        ...options
      })
      return response.data
    } catch (error) {
      console.error('智能体对话失败:', error)
      throw error
    }
  }

  /**
   * 获取对话历史
   */
  async getChatHistory(agentId, params = {}) {
    try {
      const response = await api.get(`/api/v1/agents/${agentId}/chat/history`, { params })
      return response.data
    } catch (error) {
      console.error('获取对话历史失败:', error)
      throw error
    }
  }

  /**
   * 清除对话历史
   */
  async clearChatHistory(agentId) {
    try {
      const response = await api.delete(`/api/v1/agents/${agentId}/chat/history`)
      return response.data
    } catch (error) {
      console.error('清除对话历史失败:', error)
      throw error
    }
  }

  /**
   * 上传知识库文件
   */
  async uploadKnowledgeFile(file, options = {}) {
    try {
      const formData = new FormData()
      formData.append('file', file)
      
      Object.keys(options).forEach(key => {
        formData.append(key, options[key])
      })

      const response = await api.post('/api/v1/ai-agent/upload-file', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      return response.data
    } catch (error) {
      console.error('上传知识库文件失败:', error)
      throw error
    }
  }

  /**
   * 获取知识库文档列表
   */
  async getKnowledgeDocuments() {
    try {
      const response = await api.get('/api/v1/ai-agent/knowledge-documents')
      return response.data
    } catch (error) {
      console.error('获取知识库文档失败:', error)
      throw error
    }
  }

  /**
   * 删除知识库文档
   */
  async deleteKnowledgeDocument(documentId) {
    try {
      const response = await api.delete(`/api/v1/ai-agent/knowledge-documents/${documentId}`)
      return response.data
    } catch (error) {
      console.error('删除知识库文档失败:', error)
      throw error
    }
  }

  /**
   * 获取智能体统计信息
   */
  async getAgentStats(agentId) {
    try {
      const response = await api.get(`/api/v1/agents/${agentId}/stats`)
      return response.data
    } catch (error) {
      console.error('获取智能体统计失败:', error)
      throw error
    }
  }

  /**
   * 测试智能体
   */
  async testAgent(agentId, testData) {
    try {
      const response = await api.post(`/api/v1/agents/${agentId}/test`, testData)
      return response.data
    } catch (error) {
      console.error('测试智能体失败:', error)
      throw error
    }
  }
}

// 创建单例实例
const agentService = new AgentService()

export default agentService
