"""
翻译API路由
"""

from fastapi import APIRouter, HTTPException, UploadFile, File, Form, BackgroundTasks
from typing import Optional, Dict, Any, List
from pydantic import BaseModel
import uuid
import logging
from datetime import datetime
from ...core.database import get_db_manager
from ...services.translation_service import TranslationService
from ...services.translation_model_service import translation_model_service

router = APIRouter()
translation_service = TranslationService()
logger = logging.getLogger(__name__)


class TextTranslationRequest(BaseModel):
    """文本翻译请求模型"""
    text: str
    source_lang: str = "auto"
    target_lang: str = "zh"
    domain: Optional[str] = "general"
    style: Optional[str] = "standard"
    model: Optional[str] = None


class AsyncTranslationRequest(BaseModel):
    """异步翻译请求模型"""
    text: str
    source_lang: str = "auto"
    target_lang: str = "zh"
    domain: Optional[str] = "general"
    style: Optional[str] = "standard"
    model: Optional[str] = None


class TranslationFeedbackRequest(BaseModel):
    """翻译反馈请求模型"""
    source_text: str
    translated_text: str
    source_language: str
    target_language: str
    feedback_text: Optional[str] = None
    feedback_options: List[str] = []
    rating: Optional[int] = None  # 1-5星评分
    model_used: Optional[str] = None
    task_id: Optional[str] = None


@router.post("/text")
async def translate_text(request: TextTranslationRequest):
    """文本翻译"""
    try:
        if not request.text.strip():
            return {"success": False, "error": "文本不能为空"}
        
        if len(request.text) > 5000:
            return {"success": False, "error": "文本长度不能超过5000字符"}
        
        # 调用翻译服务
        result = await translation_service.translate_text(
            text=request.text,
            source_lang=request.source_lang,
            target_lang=request.target_lang,
            domain=request.domain,
            style=request.style,
            model=request.model
        )
        
        if result["success"]:
            # 保存翻译历史
            db_manager = get_db_manager()
            db_manager.execute_query(
                """
                INSERT INTO translation_history 
                (original_text, translated_text, source_language, target_language, translation_type)
                VALUES (?, ?, ?, ?, ?)
                """,
                (request.text, result["translated_text"], request.source_lang, request.target_lang, "text")
            )
        
        return result
        
    except Exception as e:
        return {"success": False, "error": str(e)}


@router.post("/document")
async def translate_document(
    file: UploadFile = File(...),
    from_lang: str = Form("auto"),
    to_lang: str = Form("zh-CN")
):
    """文档翻译"""
    try:
        # 检查文件类型
        allowed_types = ["text/plain", "application/pdf", "application/msword", 
                        "application/vnd.openxmlformats-officedocument.wordprocessingml.document"]
        
        if file.content_type not in allowed_types:
            return {"success": False, "error": "不支持的文件类型"}
        
        # 检查文件大小
        if file.size > 10 * 1024 * 1024:  # 10MB
            return {"success": False, "error": "文件大小不能超过10MB"}
        
        # 调用文档翻译服务
        result = await translation_service.translate_document(file, from_lang, to_lang)
        
        return result
        
    except Exception as e:
        return {"success": False, "error": str(e)}


@router.post("/audio")
async def translate_audio(
    file: UploadFile = File(...),
    from_lang: str = Form("auto"),
    to_lang: str = Form("zh-CN")
):
    """音频翻译"""
    try:
        # 检查文件类型
        allowed_types = ["audio/mpeg", "audio/wav", "audio/mp3", "audio/m4a"]
        
        if file.content_type not in allowed_types:
            return {"success": False, "error": "不支持的音频格式"}
        
        # 检查文件大小
        if file.size > 50 * 1024 * 1024:  # 50MB
            return {"success": False, "error": "音频文件大小不能超过50MB"}
        
        # 调用音频翻译服务
        result = await translation_service.translate_audio(file, from_lang, to_lang)
        
        return result
        
    except Exception as e:
        return {"success": False, "error": str(e)}


@router.get("/history")
async def get_all_translation_history(limit: int = 50):
    """获取所有类型的翻译历史"""
    try:
        db_manager = get_db_manager()

        # 根据数据库类型使用不同的查询语法
        if hasattr(db_manager, 'use_postgres') and db_manager.use_postgres:
            # PostgreSQL语法
            history = db_manager.execute_query(
                """
                SELECT * FROM translation_history
                ORDER BY created_at DESC
                LIMIT %s
                """,
                (limit,)
            )
        else:
            # SQLite语法
            history = db_manager.execute_query(
                """
                SELECT * FROM translation_history
                ORDER BY created_at DESC
                LIMIT ?
                """,
                (limit,)
            )

        # 处理不同数据库返回格式
        formatted_history = []
        for item in (history or []):
            if hasattr(item, 'keys'):  # PostgreSQL RealDictRow
                formatted_history.append({
                    "id": item.get("id"),
                    "original_text": item.get("original_text"),
                    "translated_text": item.get("translated_text"),
                    "source_language": item.get("source_language"),
                    "target_language": item.get("target_language"),
                    "translation_type": item.get("translation_type"),
                    "created_at": item.get("created_at").isoformat() if hasattr(item.get("created_at"), 'isoformat') else str(item.get("created_at"))
                })
            else:  # SQLite tuple
                formatted_history.append({
                    "id": item[0] if len(item) > 0 else None,
                    "original_text": item[1] if len(item) > 1 else "",
                    "translated_text": item[2] if len(item) > 2 else "",
                    "source_language": item[3] if len(item) > 3 else "",
                    "target_language": item[4] if len(item) > 4 else "",
                    "translation_type": item[5] if len(item) > 5 else "",
                    "created_at": item[6] if len(item) > 6 else ""
                })

        return {
            "success": True,
            "history": formatted_history
        }

    except Exception as e:
        return {"success": False, "error": str(e)}


@router.post("/text/async")
async def translate_text_async(request: AsyncTranslationRequest):
    """异步文本翻译"""
    try:
        if not request.text.strip():
            return {"success": False, "error": "文本不能为空"}

        if len(request.text) > 10000:
            return {"success": False, "error": "异步翻译文本长度不能超过10000字符"}

        # 生成任务ID
        import uuid
        task_id = str(uuid.uuid4())

        # 启动异步翻译任务
        result = await translation_service.translate_text_async(
            task_id=task_id,
            text=request.text,
            source_lang=request.source_lang,
            target_lang=request.target_lang,
            domain=request.domain,
            style=request.style,
            model=request.model
        )

        return result
    except Exception as e:
        return {"success": False, "error": str(e)}


@router.get("/text/result/{task_id}")
async def get_translation_result(task_id: str):
    """获取异步翻译结果"""
    try:
        result = await translation_service.get_translation_result(task_id)
        return result
    except Exception as e:
        return {"success": False, "error": str(e)}


@router.get("/debug/routes")
async def debug_routes():
    """调试路由 - 列出所有路由"""
    print("=" * 50)
    print("[DEBUG] 调试路由被调用！！！")
    print("=" * 50)

    routes = []
    for route in router.routes:
        if hasattr(route, 'path') and hasattr(route, 'methods'):
            routes.append({
                "path": route.path,
                "methods": list(route.methods),
                "name": getattr(route, 'name', 'unknown')
            })

    return {
        "success": True,
        "message": "调试路由被正确调用",
        "routes": routes,
        "total_routes": len(routes)
    }


@router.get("/text/history-test")
async def get_text_translation_history_test(limit: int = 20):
    """测试路由 - 获取翻译历史"""
    print("=" * 50)
    print(f"[DEBUG] 测试路由被调用！！！ limit={limit}")
    print("=" * 50)

    # 直接返回一个测试响应
    return {
        "success": True,
        "message": "测试路由被正确调用",
        "limit": limit,
        "test": True,
        "history": [
            {
                "id": 1,
                "source_text": "测试文本",
                "translated_text": "Test text",
                "source_language": "zh",
                "target_language": "en",
                "created_at": "2025-08-04T16:00:00",
                "timestamp": "2025-08-04T16:00:00",
                "type": "text"
            }
        ]
    }


@router.get("/text/history")
async def text_history_endpoint(limit: int = 20):
    """获取文本翻译历史 - 重写版本"""
    try:
        print("🔥🔥🔥 文本翻译历史API被调用！🔥🔥🔥")

        db_manager = get_db_manager()

        # 使用PostgreSQL语法
        history = db_manager.execute_query(
            """
            SELECT original_text, translated_text, source_language, target_language,
                   created_at, translation_type
            FROM translation_history
            WHERE translation_type = %s
            ORDER BY created_at DESC
            LIMIT %s
            """,
            ('text', limit)
        )

        print(f"🔥 查询到 {len(history)} 条记录")

        # 处理结果
        formatted_history = []
        for i, row in enumerate(history):
            formatted_history.append({
                "id": i + 1,
                "source_text": row['original_text'],
                "translated_text": row['translated_text'],
                "source_language": row['source_language'],
                "target_language": row['target_language'],
                "created_at": row['created_at'].isoformat() if hasattr(row['created_at'], 'isoformat') else str(row['created_at']),
                "timestamp": row['created_at'].isoformat() if hasattr(row['created_at'], 'isoformat') else str(row['created_at']),
                "type": row.get('translation_type', 'text')
            })

        result = {
            "success": True,
            "history": formatted_history
        }

        print(f"🔥 返回结果: success={result['success']}, count={len(formatted_history)}")
        return result

    except Exception as e:
        print(f"🔥 异常: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}


@router.get("/languages")
async def get_supported_languages():
    """获取支持的语言列表 - 扩展版"""
    return {
        "success": True,
        "languages": {
            # 自动检测
            "auto": "自动检测",

            # 中文相关
            "zh": "中文 (简体)",
            "zh-tw": "中文 (繁體)",
            "zh-hk": "中文 (香港)",

            # 英语
            "en": "English",

            # 亚洲语言
            "ja": "日本語",
            "ko": "한국어",
            "th": "ไทย",
            "vi": "Tiếng Việt",
            "id": "Bahasa Indonesia",
            "ms": "Bahasa Melayu",
            "tl": "Filipino",
            "hi": "हिन्दी",
            "ur": "اردو",
            "bn": "বাংলা",
            "ta": "தமிழ்",
            "te": "తెలుగు",
            "mr": "मराठी",
            "gu": "ગુજરાતી",
            "kn": "ಕನ್ನಡ",
            "ml": "മലയാളം",
            "pa": "ਪੰਜਾਬੀ",

            # 欧洲语言
            "fr": "Français",
            "de": "Deutsch",
            "es": "Español",
            "it": "Italiano",
            "pt": "Português",
            "ru": "Русский",
            "nl": "Nederlands",
            "sv": "Svenska",
            "da": "Dansk",
            "no": "Norsk",
            "fi": "Suomi",
            "pl": "Polski",
            "cs": "Čeština",
            "sk": "Slovenčina",
            "hu": "Magyar",
            "ro": "Română",
            "bg": "Български",
            "hr": "Hrvatski",
            "sr": "Српски",
            "sl": "Slovenščina",
            "et": "Eesti",
            "lv": "Latviešu",
            "lt": "Lietuvių",
            "uk": "Українська",
            "be": "Беларуская",
            "el": "Ελληνικά",
            "tr": "Türkçe",

            # 中东语言
            "ar": "العربية",
            "fa": "فارسی",
            "he": "עברית",

            # 非洲语言
            "sw": "Kiswahili",
            "zu": "isiZulu",
            "af": "Afrikaans",

            # 其他语言
            "ca": "Català",
            "eu": "Euskera",
            "gl": "Galego",
            "cy": "Cymraeg",
            "ga": "Gaeilge",
            "mt": "Malti",
            "is": "Íslenska",
            "mk": "Македонски",
            "sq": "Shqip",
            "az": "Azərbaycan",
            "ka": "ქართული",
            "hy": "Հայերեն",
            "kk": "Қазақша",
            "ky": "Кыргызча",
            "uz": "O'zbek",
            "tg": "Тоҷикӣ",
            "mn": "Монгол"
        },
        "categories": {
            "常用语言": ["zh", "en", "ja", "ko", "fr", "de", "es", "ru"],
            "中文变体": ["zh", "zh-tw", "zh-hk"],
            "亚洲语言": ["ja", "ko", "th", "vi", "id", "ms", "tl", "hi", "ur", "bn"],
            "欧洲语言": ["fr", "de", "es", "it", "pt", "ru", "nl", "sv", "da", "no"],
            "中东语言": ["ar", "fa", "he", "tr"],
            "其他语言": ["sw", "zu", "af", "ca", "eu", "gl"]
        },
        "popular": ["zh", "en", "ja", "ko", "fr", "de", "es", "ru", "ar", "pt", "it", "th", "vi"]
    }


@router.get("/models/recommendations")
async def get_model_recommendations():
    """获取翻译模型推荐"""
    try:
        recommendations = translation_model_service.get_model_recommendations()
        return {
            "success": True,
            "models": recommendations,
            "total": len(recommendations)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模型推荐失败: {str(e)}")


@router.post("/models/select")
async def select_best_model(request: Dict[str, Any]):
    """智能选择最佳翻译模型"""
    try:
        # 提取参数
        source_lang = request.get("source_lang", "auto")
        target_lang = request.get("target_lang", "zh")
        text_length = request.get("text_length", 0)
        domain = request.get("domain", "general")
        quality_preference = request.get("quality_preference", "balanced")
        user_model = request.get("user_model")

        # 选择最佳模型
        selected_model, model_params = translation_model_service.select_best_model(
            source_lang=source_lang,
            target_lang=target_lang,
            text_length=text_length,
            domain=domain,
            quality_preference=quality_preference,
            user_model=user_model
        )

        return {
            "success": True,
            "selected_model": selected_model,
            "model_parameters": model_params,
            "recommendation_reason": f"基于语言对({source_lang}->{target_lang})、文本长度({text_length})、领域({domain})和质量偏好({quality_preference})的智能推荐"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"模型选择失败: {str(e)}")


@router.get("/models/usage-stats")
async def get_model_usage_stats():
    """获取模型使用统计"""
    try:
        stats = translation_model_service.get_usage_statistics()
        return {
            "success": True,
            "statistics": stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取使用统计失败: {str(e)}")


@router.post("/feedback")
async def submit_translation_feedback(request: TranslationFeedbackRequest):
    """提交翻译反馈"""
    try:
        db_manager = get_db_manager()

        # 生成反馈ID
        feedback_id = str(uuid.uuid4())

        # 准备反馈数据
        feedback_data = {
            "id": feedback_id,
            "source_text": request.source_text,
            "translated_text": request.translated_text,
            "source_language": request.source_language,
            "target_language": request.target_language,
            "feedback_text": request.feedback_text,
            "feedback_options": ",".join(request.feedback_options) if request.feedback_options else None,
            "rating": request.rating,
            "model_used": request.model_used,
            "task_id": request.task_id,
            "created_at": datetime.now().isoformat(),
            "status": "pending"  # pending, reviewed, resolved
        }

        # 创建反馈表（如果不存在）
        create_feedback_table_sql = """
        CREATE TABLE IF NOT EXISTS translation_feedback (
            id VARCHAR(36) PRIMARY KEY,
            source_text TEXT NOT NULL,
            translated_text TEXT NOT NULL,
            source_language VARCHAR(10) NOT NULL,
            target_language VARCHAR(10) NOT NULL,
            feedback_text TEXT,
            feedback_options TEXT,
            rating INTEGER,
            model_used VARCHAR(50),
            task_id VARCHAR(36),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status VARCHAR(20) DEFAULT 'pending'
        )
        """

        db_manager.execute_query(create_feedback_table_sql)

        # 插入反馈数据
        if hasattr(db_manager, 'use_postgres') and db_manager.use_postgres:
            # PostgreSQL语法
            insert_sql = """
            INSERT INTO translation_feedback
            (id, source_text, translated_text, source_language, target_language,
             feedback_text, feedback_options, rating, model_used, task_id, created_at, status)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
        else:
            # SQLite语法
            insert_sql = """
            INSERT INTO translation_feedback
            (id, source_text, translated_text, source_language, target_language,
             feedback_text, feedback_options, rating, model_used, task_id, created_at, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

        db_manager.execute_query(
            insert_sql,
            (
                feedback_data["id"],
                feedback_data["source_text"],
                feedback_data["translated_text"],
                feedback_data["source_language"],
                feedback_data["target_language"],
                feedback_data["feedback_text"],
                feedback_data["feedback_options"],
                feedback_data["rating"],
                feedback_data["model_used"],
                feedback_data["task_id"],
                feedback_data["created_at"],
                feedback_data["status"]
            )
        )

        logger.info(f"收到翻译反馈: {feedback_id}")
        logger.info(f"反馈内容: {request.feedback_text}")
        logger.info(f"反馈选项: {request.feedback_options}")
        logger.info(f"评分: {request.rating}")

        return {
            "success": True,
            "message": "感谢您的反馈！我们会认真处理您的建议。",
            "feedback_id": feedback_id
        }

    except Exception as e:
        logger.error(f"提交反馈失败: {e}")
        return {"success": False, "error": f"提交反馈失败: {str(e)}"}
