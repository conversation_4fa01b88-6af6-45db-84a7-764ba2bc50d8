"""
AI智能体API路由
"""

from fastapi import APIRouter, HTTPException, UploadFile, File
from typing import List, Optional
from pydantic import BaseModel
import json
import logging
from ...core.database import get_db_manager
from ...services.ai_agent_service import AiAgentService
from ...services.simple_agent_service import SimpleAgentService
from ...services.basic_agent_service import BasicAgentService
from app.services.realtime_conversation import conversation_manager
from app.services.advanced_ai_chat import advanced_ai_chat, emotion_analyzer
import tempfile
import os
import time
from datetime import datetime

router = APIRouter()

# 全局配置存储（用于存储更新后的默认智能体配置）
updated_default_agents = {}
ai_agent_service = AiAgentService()


class AgentCreate(BaseModel):
    """创建智能体请求模型"""
    name: str
    description: Optional[str] = None
    agent_type: str
    config: Optional[dict] = None


class AgentResponse(BaseModel):
    """智能体响应模型"""
    id: int
    name: str
    description: Optional[str]
    agent_type: str
    status: str
    created_at: str


class KnowledgeBaseCreate(BaseModel):
    """创建知识库请求模型"""
    name: str
    type: str = "document"
    description: Optional[str] = None


class ChatRequest(BaseModel):
    """对话请求模型"""
    message: str
    session_id: Optional[str] = None
    conversation_id: Optional[str] = None
    user_id: Optional[str] = None


class KnowledgeBaseCreate(BaseModel):
    """创建知识库请求模型"""
    name: str
    type: str
    description: Optional[str] = None


@router.get("/list")
async def get_agent_list(
    page: int = 1,
    size: int = 12,
    category: str = None,
    search: str = None
):
    """获取智能体列表"""
    try:
        db_manager = get_db_manager()

        # 构建查询条件
        where_conditions = ["is_active = true"]
        params = []

        if category:
            where_conditions.append("agent_type = %s")
            params.append(category)

        if search:
            where_conditions.append("(name ILIKE %s OR description ILIKE %s)")
            params.extend([f"%{search}%", f"%{search}%"])

        where_clause = " AND ".join(where_conditions)

        # 查询总数
        count_sql = f"SELECT COUNT(*) as total FROM true_agents WHERE {where_clause}"
        total_result = db_manager.execute_query(count_sql, params)
        total = total_result[0]['total'] if total_result else 0

        # 计算偏移量
        offset = (page - 1) * size

        # 查询分页数据
        agents_sql = f"""
            SELECT * FROM true_agents
            WHERE {where_clause}
            ORDER BY created_at DESC
            LIMIT %s OFFSET %s
        """
        agents = db_manager.execute_query(agents_sql, params + [size, offset])

        # 构建智能体列表，包含更新后的配置
        agent_list = []
        for agent in (agents or []):
            agent_data = {
                "id": agent.get("id"),
                "name": agent.get("name"),
                "description": agent.get("description"),
                "agent_type": agent.get("agent_type"),
                "status": "active" if agent.get("is_active") else "inactive",
                "created_at": str(agent.get("created_at", "")),
                "rating": 4.5,  # 默认评分
                "usage_count": agent.get("usage_count", 0),
                "is_featured": False,
                "system_prompt": agent.get("system_prompt"),
                "tools": agent.get("tools", []),
                "knowledge_bases": agent.get("knowledge_bases", []),
                "memory_types": agent.get("memory_types", []),
                "workflow_enabled": agent.get("workflow_enabled", False),
                "max_tokens": agent.get("max_tokens", 4000),
                "temperature": agent.get("temperature", 0.7),
                "success_rate": agent.get("success_rate", 0.85)
            }

            # 如果有更新后的配置，使用更新后的数据
            agent_id = agent.get("id")
            if agent_id in updated_default_agents:
                updated_data = updated_default_agents[agent_id]
                agent_data.update({
                    "name": updated_data.get("name", agent_data["name"]),
                    "description": updated_data.get("description", agent_data["description"]),
                    "system_prompt": updated_data.get("system_prompt", agent_data["system_prompt"]),
                    "tools": updated_data.get("tools", agent_data["tools"]),
                    "knowledge_bases": updated_data.get("knowledge_bases", agent_data["knowledge_bases"]),
                    "memory_types": updated_data.get("memory_types", agent_data["memory_types"]),
                    "max_tokens": updated_data.get("max_tokens", agent_data["max_tokens"]),
                    "temperature": updated_data.get("temperature", agent_data["temperature"]),
                })
                print(f"[DEBUG] 列表API使用更新后的配置: {agent_id} - {updated_data.get('name')}")

            agent_list.append(agent_data)

        return {
            "success": True,
            "agents": agent_list,
            "pagination": {
                "page": page,
                "size": size,
                "total": total,
                "pages": (total + size - 1) // size
            }
        }

    except Exception as e:
        return {"success": False, "error": str(e)}


@router.put("/{agent_id}")
async def update_agent(agent_id: str, agent_data: dict):
    """更新智能体配置"""
    try:
        print(f"[DEBUG] 更新智能体: {agent_id}")
        print(f"[DEBUG] 更新数据: {agent_data}")

        # 对于默认智能体，尝试更新数据库，如果失败则更新内存配置
        if agent_id == 'e55f5e84-6d8b-4265-8e55-728bdb0d2455':
            # 首先尝试更新数据库
            try:
                db_manager = get_db_manager()
                print(f"[DEBUG] 获取数据库管理器成功")

                # 检查智能体是否存在于数据库中
                print(f"[DEBUG] 检查智能体是否存在: {agent_id}")
                print(f"[DEBUG] 数据库类型: {'PostgreSQL' if db_manager.use_postgres else 'SQLite'}")

                # 根据数据库类型使用不同的占位符
                if db_manager.use_postgres:
                    query = "SELECT * FROM true_agents WHERE id = %s AND is_active = true"
                else:
                    query = "SELECT * FROM true_agents WHERE id = ? AND is_active = true"

                existing_agent = db_manager.execute_query(query, (agent_id,))
                print(f"[DEBUG] 查询结果: {existing_agent}")

                if existing_agent and len(existing_agent) > 0:
                    print(f"[DEBUG] 智能体存在，执行更新操作")
                    # 准备扩展配置（包含前端的所有配置）
                    extended_config = {
                        'avatar': agent_data.get('avatar', '🤖'),
                        'personality': agent_data.get('personality', 'friendly'),
                        'domain': agent_data.get('domain', ''),
                        'language': agent_data.get('language', 'zh-CN'),
                        'memory': agent_data.get('memory', {}),
                        'advanced': agent_data.get('advanced', {}),
                        'safety': agent_data.get('safety', {}),
                        'ui_config': {
                            'avatar': agent_data.get('avatar', '🤖'),
                            'personality': agent_data.get('personality', 'friendly'),
                            'domain': agent_data.get('domain', ''),
                            'language': agent_data.get('language', 'zh-CN')
                        }
                    }

                    # 更新数据库中的智能体
                    update_params = [
                        agent_data.get('name', '语言学习助手'),
                        agent_data.get('description', '专业的语言学习和教学助手'),
                        agent_data.get('systemPrompt', agent_data.get('system_prompt', '')),
                        json.dumps(agent_data.get('capabilities', agent_data.get('tools', []))),
                        json.dumps(agent_data.get('knowledge', agent_data.get('knowledge_bases', []))),
                        json.dumps(agent_data.get('memory_types', ['short_term', 'long_term'])),
                        json.dumps(extended_config),  # 将扩展配置保存到workflow_config字段
                        agent_data.get('maxTokens', agent_data.get('max_tokens', 4000)),
                        agent_data.get('temperature', 0.7),
                        agent_id
                    ]
                    print(f"[DEBUG] 更新参数: {update_params[:3]}... (共{len(update_params)}个参数)")

                    # 根据数据库类型使用不同的占位符和语法
                    if db_manager.use_postgres:
                        update_query = """UPDATE true_agents SET
                           name = %s, description = %s, system_prompt = %s,
                           tools = %s, knowledge_bases = %s, memory_types = %s,
                           workflow_config = %s, max_tokens = %s, temperature = %s, updated_at = NOW()
                           WHERE id = %s AND is_active = true"""
                    else:
                        update_query = """UPDATE true_agents SET
                           name = ?, description = ?, system_prompt = ?,
                           tools = ?, knowledge_bases = ?, memory_types = ?,
                           workflow_config = ?, max_tokens = ?, temperature = ?, updated_at = datetime('now')
                           WHERE id = ? AND is_active = 1"""

                    update_result = db_manager.execute_query(update_query, update_params)
                    print(f"[DEBUG] 更新结果: {update_result}")

                    if update_result and update_result > 0:
                        print(f"[DEBUG] 数据库更新成功: {agent_id}")
                        return {
                            "success": True,
                            "message": "智能体更新成功（数据库）",
                            "agent_id": agent_id
                        }
                    else:
                        print(f"[WARNING] 数据库更新返回结果为空或0: {update_result}")
                else:
                    # 如果数据库中不存在，插入新记录
                    print(f"[DEBUG] 智能体不存在，执行插入操作")
                    insert_params = [
                        agent_id,
                        agent_data.get('name', '语言学习助手'),
                        agent_data.get('description', '专业的语言学习和教学助手'),
                        'language_tutor',
                        agent_data.get('systemPrompt', agent_data.get('system_prompt', '')),
                        json.dumps(agent_data.get('capabilities', agent_data.get('tools', []))),
                        json.dumps(agent_data.get('knowledge', agent_data.get('knowledge_bases', []))),
                        json.dumps(agent_data.get('memory_types', ['short_term', 'long_term'])),
                        agent_data.get('maxTokens', agent_data.get('max_tokens', 4000)),
                        agent_data.get('temperature', 0.7),
                        True
                    ]
                    print(f"[DEBUG] 插入参数: {insert_params}")

                    # 根据数据库类型使用不同的占位符和语法
                    if db_manager.use_postgres:
                        insert_query = """INSERT INTO true_agents
                           (id, name, description, agent_type, system_prompt, tools, knowledge_bases,
                            memory_types, max_tokens, temperature, is_active, created_at, updated_at)
                           VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())"""
                    else:
                        insert_query = """INSERT INTO true_agents
                           (id, name, description, agent_type, system_prompt, tools, knowledge_bases,
                            memory_types, max_tokens, temperature, is_active, created_at, updated_at)
                           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))"""

                    insert_result = db_manager.execute_query(insert_query, insert_params)
                    print(f"[DEBUG] 插入结果: {insert_result}")

                    if insert_result:
                        print(f"[DEBUG] 数据库插入成功: {agent_id}")
                        return {
                            "success": True,
                            "message": "智能体创建成功（数据库）",
                            "agent_id": agent_id
                        }
                    else:
                        print(f"[WARNING] 数据库插入返回结果为空: {insert_result}")

            except Exception as db_error:
                print(f"[WARNING] 数据库操作失败，使用内存存储: {db_error}")

            # 如果数据库操作失败，使用内存存储作为备用
            updated_agent = {
                "id": agent_id,
                "name": agent_data.get('name', '语言学习助手'),
                "description": agent_data.get('description', '专业的语言学习和教学助手'),
                "agent_type": "language_tutor",
                "status": "active",
                "system_prompt": agent_data.get('systemPrompt', agent_data.get('system_prompt', '')),
                "tools": agent_data.get('capabilities', agent_data.get('tools', [])),
                "knowledge_bases": agent_data.get('knowledge', agent_data.get('knowledge_bases', [])),
                "memory_types": agent_data.get('memory_types', ['short_term', 'long_term']),
                "workflow_enabled": True,
                "max_tokens": agent_data.get('maxTokens', agent_data.get('max_tokens', 4000)),
                "temperature": agent_data.get('temperature', 0.7),
                "usage_count": 30,
                "rating": 4.5,
                "is_featured": False,
                "created_at": "2025-07-11 09:00:42.468409",
                "updated_at": "now"
            }

            # 保存到全局配置存储
            updated_default_agents[agent_id] = updated_agent
            print(f"[DEBUG] 内存更新成功: {agent_id}")

            return {
                "success": True,
                "message": "智能体更新成功（内存）",
                "agent_id": agent_id,
                "updated_data": updated_agent
            }

        # 尝试更新数据库中的智能体
        try:
            db_manager = get_db_manager()
            result = db_manager.execute_query(
                """UPDATE true_agents SET
                   name = %s, description = %s, system_prompt = %s,
                   tools = %s, knowledge_bases = %s, memory_types = %s,
                   max_tokens = %s, temperature = %s, updated_at = NOW()
                   WHERE id = %s AND is_active = true""",
                [
                    agent_data.get('name'),
                    agent_data.get('description'),
                    agent_data.get('systemPrompt', agent_data.get('system_prompt')),
                    json.dumps(agent_data.get('capabilities', agent_data.get('tools', []))),
                    json.dumps(agent_data.get('knowledge', agent_data.get('knowledge_bases', []))),
                    json.dumps(agent_data.get('memory_types', ['short_term'])),
                    agent_data.get('maxTokens', agent_data.get('max_tokens', 4000)),
                    agent_data.get('temperature', 0.7),
                    agent_id
                ]
            )

            if result and result > 0:
                return {
                    "success": True,
                    "message": "智能体更新成功",
                    "agent_id": agent_id
                }
            else:
                return {"success": False, "error": "智能体不存在或更新失败"}

        except Exception as db_error:
            print(f"[WARNING] 数据库更新失败: {db_error}")
            return {"success": False, "error": f"数据库更新失败: {str(db_error)}"}

    except Exception as e:
        print(f"[ERROR] 更新智能体失败: {e}")
        return {"success": False, "error": str(e)}


@router.get("/knowledge-documents")
async def get_knowledge_documents():
    """获取知识库文档列表"""
    try:
        db_manager = get_db_manager()

        # 查询知识库文档
        if db_manager.use_postgres:
            query = "SELECT * FROM knowledge_documents ORDER BY created_at DESC"
        else:
            query = "SELECT * FROM knowledge_documents ORDER BY created_at DESC"

        documents = db_manager.execute_query(query)

        if documents:
            doc_list = []
            for doc in documents:
                # 解析doc_metadata（适配现有表结构）
                doc_metadata = {}
                if doc.get('doc_metadata'):
                    try:
                        doc_metadata = json.loads(doc.get('doc_metadata')) if isinstance(doc.get('doc_metadata'), str) else doc.get('doc_metadata')
                    except:
                        doc_metadata = {}

                doc_list.append({
                    "id": doc.get("id"),
                    "filename": doc.get("title"),  # 使用title作为filename
                    "file_type": doc.get("content_type"),  # 使用content_type作为file_type
                    "size": doc.get("file_size", 0),  # 使用file_size
                    "created_at": str(doc.get("created_at", "")),
                    "metadata": doc_metadata  # 使用doc_metadata
                })

            return {
                "success": True,
                "documents": doc_list,
                "total": len(doc_list)
            }
        else:
            return {
                "success": True,
                "documents": [],
                "total": 0
            }

    except Exception as e:
        print(f"[ERROR] 获取知识库文档失败: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "documents": [],
            "total": 0
        }


@router.get("/{agent_id}")
async def get_agent_by_id(agent_id: str):
    """获取单个智能体详情"""
    try:
        print(f"[DEBUG] 获取智能体详情: {agent_id}")

        # 首先返回默认配置，避免数据库问题
        default_agents = {
            'e55f5e84-6d8b-4265-8e55-728bdb0d2455': {
                "id": "e55f5e84-6d8b-4265-8e55-728bdb0d2455",
                "name": "语言学习助手",
                "description": "专业的语言学习和教学助手，提供个性化的语言学习指导",
                "agent_type": "language_tutor",
                "status": "active",
                "system_prompt": "You are an intelligent English conversation partner and language tutor. Your role is to:\n\n1. **Engage in natural English conversations** - Always respond in English unless specifically asked to use Chinese\n2. **Adapt to the user's level** - Use appropriate vocabulary and grammar complexity  \n3. **Provide gentle corrections** - When you notice errors, correct them naturally in your response\n4. **Be encouraging and supportive** - Create a comfortable learning environment\n5. **Ask follow-up questions** - Keep the conversation flowing naturally\n\n**Conversation Guidelines:**\n- Start conversations in English immediately\n- Use simple, clear English for beginners\n- Gradually increase complexity as the user improves\n- Provide translations in parentheses when needed: \"Hello (你好)\"\n- Correct errors by restating correctly: \"You said 'I am go', but we say 'I am going'\"\n- Ask about interests, daily life, hobbies to practice real conversation\n\n**Example interactions:**\nUser: \"我想练习英语对话\"\nYou: \"Great! Let's practice English conversation. Hi there! How are you today? What did you do this morning?\"\n\nUser: \"Hello, I am fine\"  \nYou: \"Hello! I'm glad to hear you're fine. What's your name? And what do you like to do in your free time?\"\n\nAlways be natural, friendly, and focus on real conversation practice rather than just giving advice about learning.",
                "tools": [
                    {
                        "type": "translation",
                        "config": {}
                    },
                    {
                        "type": "web_search",
                        "config": {}
                    },
                    {
                        "type": "file_operation",
                        "config": {}
                    }
                ],
                "knowledge_bases": [],
                "memory_types": [
                    "short_term",
                    "long_term",
                    "episodic",
                    "procedural"
                ],
                "workflow_enabled": True,
                "max_tokens": 4000,
                "temperature": 0.7,
                "usage_count": 30,
                "rating": 4.5,
                "is_featured": False,
                "created_at": "2025-07-11 09:00:42.468409"
            }
        }

        # 优先从数据库查询（包括默认智能体）
        try:
            db_manager = get_db_manager()

            # 根据数据库类型使用正确的占位符
            if db_manager.use_postgres:
                query = "SELECT * FROM true_agents WHERE id = %s AND is_active = true"
            else:
                query = "SELECT * FROM true_agents WHERE id = ? AND is_active = true"

            agents = db_manager.execute_query(query, (agent_id,))
            print(f"[DEBUG] 数据库查询结果: {len(agents) if agents else 0} 个智能体")

            if agents and len(agents) > 0:
                agent_data = agents[0]
                print(f"[DEBUG] 从数据库返回智能体: {agent_data.get('name', 'Unknown')}")

                # 解析扩展配置
                workflow_config = agent_data.get("workflow_config", {})
                if isinstance(workflow_config, str):
                    try:
                        workflow_config = json.loads(workflow_config)
                    except:
                        workflow_config = {}

                # 从扩展配置中提取前端需要的字段
                ui_config = workflow_config.get('ui_config', {})

                # 转换数据库格式为API格式
                agent_response = {
                    "id": agent_data.get("id"),
                    "name": agent_data.get("name"),
                    "description": agent_data.get("description"),
                    "agent_type": agent_data.get("agent_type"),
                    "status": "active" if agent_data.get("is_active") else "inactive",
                    "system_prompt": agent_data.get("system_prompt", ""),
                    "tools": agent_data.get("tools", []),
                    "knowledge_bases": agent_data.get("knowledge_bases", []),
                    "memory_types": agent_data.get("memory_types", []),
                    "workflow_enabled": agent_data.get("workflow_enabled", False),
                    "max_tokens": agent_data.get("max_tokens", 4000),
                    "temperature": agent_data.get("temperature", 0.7),
                    "usage_count": agent_data.get("usage_count", 0),
                    "rating": 4.5,
                    "is_featured": False,
                    "created_at": str(agent_data.get("created_at", "")),
                    "updated_at": str(agent_data.get("updated_at", "")),

                    # 添加前端需要的扩展字段
                    "avatar": workflow_config.get('avatar', ui_config.get('avatar', '🤖')),
                    "personality": workflow_config.get('personality', ui_config.get('personality', 'friendly')),
                    "domain": workflow_config.get('domain', ui_config.get('domain', '')),
                    "language": workflow_config.get('language', ui_config.get('language', 'zh-CN')),
                    "memory": workflow_config.get('memory', {}),
                    "advanced": workflow_config.get('advanced', {}),
                    "safety": workflow_config.get('safety', {})
                }

                print(f"[DEBUG] 返回扩展配置: avatar={agent_response['avatar']}, personality={agent_response['personality']}")

                return {
                    "success": True,
                    "agent": agent_response
                }

        except Exception as db_error:
            print(f"[WARNING] 数据库查询失败: {db_error}")
            agents = None

        # 如果数据库查询失败，且是默认智能体，返回默认配置
        if agent_id in default_agents:
            print(f"[DEBUG] 数据库查询失败，返回默认智能体配置: {agent_id}")

            # 如果有内存中的更新配置，返回更新后的
            if agent_id in updated_default_agents:
                print(f"[DEBUG] 返回内存中的更新配置")
                return {
                    "success": True,
                    "agent": updated_default_agents[agent_id]
                }

            # 否则返回硬编码的默认配置
            return {
                "success": True,
                "agent": default_agents[agent_id]
            }

        if not agents:
            print(f"[DEBUG] 数据库中未找到智能体，返回不存在错误")
            return {"success": False, "error": "智能体不存在"}

        agent = agents[0]
        return {
            "success": True,
            "agent": {
                "id": agent.get("id"),
                "name": agent.get("name"),
                "description": agent.get("description"),
                "agent_type": agent.get("agent_type"),
                "status": "active" if agent.get("is_active") else "inactive",
                "created_at": str(agent.get("created_at", "")),
                "rating": 4.5,
                "usage_count": agent.get("usage_count", 0),
                "is_featured": False,
                "system_prompt": agent.get("system_prompt"),
                "tools": agent.get("tools", []),
                "knowledge_bases": agent.get("knowledge_bases", []),
                "memory_types": agent.get("memory_types", []),
                "workflow_enabled": agent.get("workflow_enabled", False),
                "max_tokens": agent.get("max_tokens", 4000),
                "temperature": agent.get("temperature", 0.7),
                "success_rate": agent.get("success_rate", 0.85)
            }
        }

    except Exception as e:
        return {"success": False, "error": str(e)}


@router.post("/create")
async def create_agent(agent_data: AgentCreate):
    """创建智能体"""
    try:
        db_manager = get_db_manager()
        
        # 将config转换为JSON字符串
        config_str = str(agent_data.config) if agent_data.config else None
        
        result = db_manager.execute_query(
            """
            INSERT INTO ai_agents (name, description, agent_type, config, status)
            VALUES (?, ?, ?, ?, ?)
            """,
            (agent_data.name, agent_data.description, agent_data.agent_type, config_str, "active")
        )
        
        if result:
            # 获取新创建的智能体信息
            new_agent = db_manager.execute_query(
                "SELECT * FROM ai_agents WHERE rowid = last_insert_rowid()"
            )
            
            if new_agent:
                agent = new_agent[0]
                return {
                    "success": True,
                    "agent": {
                        "id": agent["id"],
                        "name": agent["name"],
                        "description": agent["description"],
                        "agent_type": agent["agent_type"],
                        "status": agent["status"],
                        "created_at": agent["created_at"]
                    }
                }
        
        return {"success": False, "error": "创建失败"}
        
    except Exception as e:
        return {"success": False, "error": str(e)}


@router.delete("/{agent_id}")
async def delete_agent(agent_id: int):
    """删除智能体"""
    try:
        db_manager = get_db_manager()
        result = db_manager.execute_query(
            "UPDATE ai_agents SET status = 'deleted' WHERE id = ?",
            (agent_id,)
        )
        
        if result > 0:
            return {"success": True}
        else:
            return {"success": False, "error": "智能体不存在"}
            
    except Exception as e:
        return {"success": False, "error": str(e)}


@router.post("/{agent_id}/chat")
async def chat_with_agent(agent_id: str, request: ChatRequest):
    """与智能体对话"""
    try:
        print(f"[DEBUG] 收到对话请求: agent_id={agent_id}, request={request}")
        user_message = request.message
        if not user_message:
            return {"success": False, "error": "消息不能为空"}
        
        # 获取智能体信息
        db_manager = get_db_manager()
        agent = db_manager.execute_query(
            "SELECT * FROM true_agents WHERE id = %s AND is_active = true",
            [agent_id]
        )

        if not agent:
            return {"success": False, "error": "智能体不存在"}

        # 优先使用本地 Ollama 服务
        response = None
        service_used = None

        # 1. 首先尝试本地 Ollama 服务（最佳选择）
        try:
            from app.services.ai_agent_service import AiAgentService
            ai_service = AiAgentService()
            response = await ai_service.chat_with_agent(agent[0], user_message)
            service_used = "AiAgentService (Ollama)"
            print(f"[INFO] 使用本地 Ollama 服务成功")
        except Exception as e:
            print(f"[WARNING] 本地 Ollama 服务失败: {e}")

        # 2. 如果 Ollama 失败，尝试简单智能体服务（也使用 Ollama）
        if not response:
            try:
                simple_agent_service = SimpleAgentService()
                response = await simple_agent_service.chat_with_agent(agent[0], user_message)
                service_used = "SimpleAgentService (Ollama)"
                print(f"[INFO] 使用简单智能体服务（Ollama）")
            except Exception as e:
                print(f"[WARNING] 简单智能体服务失败: {e}")

        # 3. 最后回退到基础服务（预设回复）
        if not response:
            print(f"[WARNING] 所有 Ollama 服务失败，使用基础预设回复")
            basic_agent_service = BasicAgentService()
            response = await basic_agent_service.chat_with_agent(agent[0], user_message)
            service_used = "BasicAgentService (预设回复)"

        print(f"[INFO] 最终使用服务: {service_used}")
        
        return {
            "success": True,
            "response": response
        }
        
    except Exception as e:
        return {"success": False, "error": str(e)}


@router.get("/conversations")
async def get_conversations(agent_id: str = None, user_id: str = None):
    """获取对话历史记录"""
    try:
        # 这里应该从数据库查询对话历史
        # 暂时返回模拟数据
        mock_conversations = [
            {
                "id": "conv_1",
                "title": "英语学习对话",
                "last_message": "Hello! How are you today?",
                "message_count": 15,
                "created_at": "2024-01-20T10:00:00Z",
                "updated_at": "2024-01-20T11:30:00Z",
                "agent_id": agent_id,
                "user_id": user_id
            },
            {
                "id": "conv_2",
                "title": "日常英语练习",
                "last_message": "What's your favorite hobby?",
                "message_count": 8,
                "created_at": "2024-01-19T14:00:00Z",
                "updated_at": "2024-01-19T14:45:00Z",
                "agent_id": agent_id,
                "user_id": user_id
            }
        ]

        return {
            "success": True,
            "conversations": mock_conversations,
            "total": len(mock_conversations)
        }

    except Exception as e:
        return {"success": False, "error": str(e)}


@router.get("/conversations/{conversation_id}/messages")
async def get_conversation_messages(conversation_id: str):
    """获取特定对话的消息历史"""
    try:
        # 这里应该从数据库查询消息历史
        # 暂时返回模拟数据
        mock_messages = [
            {
                "id": "msg_1",
                "type": "user",
                "content": "Hello, I want to practice English",
                "timestamp": "2024-01-20T10:00:00Z"
            },
            {
                "id": "msg_2",
                "type": "agent",
                "content": "Hello! I'm glad to help you practice English. What would you like to talk about?",
                "timestamp": "2024-01-20T10:00:30Z"
            }
        ]

        return {
            "success": True,
            "messages": mock_messages,
            "conversation_id": conversation_id
        }

    except Exception as e:
        return {"success": False, "error": str(e)}


# 重复的路由已删除，使用上面的get_agent_by_id函数


@router.post("/upload-file")
async def upload_file(file: UploadFile = File(...)):
    """上传并处理文件（包括Excel）- 完整实现"""
    try:
        print(f"[DEBUG] 开始处理文件上传: {file.filename}")

        # 检查文件大小（限制10MB）
        content = await file.read()
        file_size = len(content)

        if file_size > 10 * 1024 * 1024:
            return {"success": False, "error": "文件大小不能超过10MB"}

        # 获取文件扩展名
        file_extension = file.filename.split('.')[-1].lower() if '.' in file.filename else ''

        # 支持的文件类型
        supported_types = ['xlsx', 'xls', 'csv', 'txt', 'md', 'pdf', 'doc', 'docx']
        if file_extension not in supported_types:
            return {"success": False, "error": f"不支持的文件类型: {file_extension}"}

        print(f"[DEBUG] 文件验证通过: {file.filename}, 大小: {file_size} bytes")

        # 创建临时文件进行处理
        temp_file_path = None
        try:
            with tempfile.NamedTemporaryFile(delete=False, suffix=f'.{file_extension}') as temp_file:
                temp_file.write(content)
                temp_file_path = temp_file.name

            # 根据文件类型进行真实的内容处理
            processed_result = None
            if file_extension in ['xlsx', 'xls']:
                processed_result = await process_excel_file_real(temp_file_path, file.filename)
            elif file_extension == 'csv':
                processed_result = await process_csv_file_real(temp_file_path, file.filename)
            elif file_extension in ['txt', 'md']:
                processed_result = await process_text_file_real(temp_file_path, file.filename)
            elif file_extension == 'pdf':
                processed_result = await process_pdf_file_real(temp_file_path, file.filename)
            elif file_extension in ['doc', 'docx']:
                processed_result = await process_word_file_real(temp_file_path, file.filename)
            else:
                processed_result = {"error": "不支持的文件类型"}

            # 保存到知识库数据库（即使处理有错误也保存基本信息）
            if processed_result:
                # 如果处理失败，创建基本的文档信息
                if processed_result.get("error"):
                    processed_result["content"] = f"文件上传成功，但处理失败: {processed_result.get('error')}"
                    processed_result["summary"] = f"处理失败的{file_extension}文件"

                knowledge_id = await save_to_knowledge_base(file.filename, file_extension, processed_result)
                processed_result["knowledge_id"] = knowledge_id

            print(f"[DEBUG] 文件处理完成: {file.filename}")

            return {
                "success": True,
                "message": "文件上传并处理成功",
                "filename": file.filename,
                "type": file_extension,
                "size": file_size,
                "data": processed_result
            }

        finally:
            # 清理临时文件
            if temp_file_path and os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    except Exception as e:
        print(f"[ERROR] 文件上传处理失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}


async def process_excel_file_real(file_path: str, filename: str):
    """真实处理Excel文件"""
    try:
        # 尝试导入openpyxl，如果没有则安装
        try:
            import openpyxl
        except ImportError:
            print("[WARNING] openpyxl未安装，尝试使用pandas读取Excel")
            try:
                import pandas as pd
                df = pd.read_excel(file_path)
                return {
                    "type": "excel",
                    "sheets": [{
                        "name": "Sheet1",
                        "rows": len(df),
                        "columns": len(df.columns),
                        "headers": df.columns.tolist(),
                        "data": df.head(10).values.tolist(),  # 只返回前10行
                        "summary": f"Excel文件包含 {len(df)} 行数据，{len(df.columns)} 列"
                    }]
                }
            except ImportError:
                return {"error": "缺少Excel处理库，请安装 openpyxl 或 pandas"}

        workbook = openpyxl.load_workbook(file_path, data_only=True)
        sheets_data = []

        for sheet_name in workbook.sheetnames:
            sheet = workbook[sheet_name]

            # 获取数据范围
            if sheet.max_row == 1 and sheet.max_column == 1:
                continue  # 跳过空工作表

            # 读取表头
            headers = []
            if sheet.max_row > 0:
                for cell in sheet[1]:
                    headers.append(str(cell.value) if cell.value is not None else '')

            # 读取数据（限制前100行避免内存问题）
            data = []
            for row in sheet.iter_rows(min_row=2, max_row=min(sheet.max_row, 101), values_only=True):
                if any(cell is not None for cell in row):  # 跳过空行
                    data.append([str(cell) if cell is not None else '' for cell in row])

            if headers or data:
                sheets_data.append({
                    'sheet_name': sheet_name,
                    'rows': len(data),
                    'columns': len(headers),
                    'headers': headers,
                    'sample_data': data[:10],  # 前10行数据
                    'total_rows': sheet.max_row - 1  # 减去表头行
                })

        return {
            'type': 'excel',
            'filename': filename,
            'sheets': sheets_data,
            'summary': f"Excel文件包含 {len(sheets_data)} 个工作表，总计 {sum(s['total_rows'] for s in sheets_data)} 行数据"
        }

    except Exception as e:
        print(f"[ERROR] Excel处理失败: {str(e)}")
        return {'type': 'excel', 'error': f"Excel处理失败: {str(e)}"}


async def process_csv_file_real(file_path: str, filename: str):
    """真实处理CSV文件"""
    try:
        import csv

        # 尝试导入chardet，如果失败则使用默认编码
        try:
            import chardet
            # 检测文件编码
            with open(file_path, 'rb') as file:
                raw_data = file.read()
                encoding = chardet.detect(raw_data)['encoding'] or 'utf-8'
        except ImportError:
            print("[WARNING] chardet未安装，使用默认编码utf-8")
            encoding = 'utf-8'

        with open(file_path, 'r', encoding=encoding) as file:
            # 尝试检测分隔符
            sample = file.read(1024)
            file.seek(0)
            sniffer = csv.Sniffer()
            delimiter = sniffer.sniff(sample).delimiter

            reader = csv.reader(file, delimiter=delimiter)
            rows = list(reader)

            if not rows:
                return {'type': 'csv', 'error': 'CSV文件为空'}

            headers = rows[0] if rows else []
            data = rows[1:11] if len(rows) > 1 else []  # 前10行数据

            return {
                'type': 'csv',
                'filename': filename,
                'headers': headers,
                'sample_data': data,
                'total_rows': len(rows) - 1,  # 减去表头
                'columns': len(headers),
                'delimiter': delimiter,
                'encoding': encoding,
                'summary': f"CSV文件包含 {len(rows)-1} 行数据，{len(headers)} 列"
            }

    except Exception as e:
        print(f"[ERROR] CSV处理失败: {str(e)}")
        return {'type': 'csv', 'error': f"CSV处理失败: {str(e)}"}


async def process_text_file_real(file_path: str, filename: str):
    """真实处理文本文件"""
    try:
        import chardet

        # 检测文件编码
        with open(file_path, 'rb') as file:
            raw_data = file.read()
            encoding = chardet.detect(raw_data)['encoding'] or 'utf-8'

        with open(file_path, 'r', encoding=encoding) as file:
            content = file.read()

            # 分析文本内容
            lines = content.split('\n')
            words = content.split()

            return {
                'type': 'text',
                'filename': filename,
                'content': content[:2000],  # 前2000字符
                'full_content': content,  # 完整内容用于知识库
                'lines': len(lines),
                'words': len(words),
                'characters': len(content),
                'encoding': encoding,
                'summary': f"文本文件包含 {len(lines)} 行，{len(words)} 个单词，{len(content)} 个字符"
            }

    except Exception as e:
        print(f"[ERROR] 文本处理失败: {str(e)}")
        return {'type': 'text', 'error': f"文本处理失败: {str(e)}"}


async def process_pdf_file_real(file_path: str, filename: str):
    """真实处理PDF文件"""
    try:
        # 尝试使用PyPDF2
        try:
            import PyPDF2
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                num_pages = len(pdf_reader.pages)

                # 提取前几页的文本
                text_content = ""
                for i in range(min(3, num_pages)):  # 只处理前3页
                    page = pdf_reader.pages[i]
                    text_content += page.extract_text() + "\n"

                return {
                    'type': 'pdf',
                    'filename': filename,
                    'pages': num_pages,
                    'content': text_content[:2000],  # 前2000字符
                    'full_content': text_content,  # 完整内容
                    'summary': f"PDF文件包含 {num_pages} 页，已提取文本内容"
                }
        except ImportError:
            return {
                'type': 'pdf',
                'filename': filename,
                'error': '缺少PDF处理库，请安装 PyPDF2',
                'summary': 'PDF文件已上传，但无法提取内容'
            }

    except Exception as e:
        print(f"[ERROR] PDF处理失败: {str(e)}")
        return {'type': 'pdf', 'error': f"PDF处理失败: {str(e)}"}


async def process_word_file_real(file_path: str, filename: str):
    """真实处理Word文件"""
    try:
        # 尝试使用python-docx
        try:
            from docx import Document
            doc = Document(file_path)

            # 提取文本内容
            full_text = []
            for paragraph in doc.paragraphs:
                full_text.append(paragraph.text)

            content = '\n'.join(full_text)

            return {
                'type': 'word',
                'filename': filename,
                'paragraphs': len(doc.paragraphs),
                'content': content[:2000],  # 前2000字符
                'full_content': content,  # 完整内容
                'summary': f"Word文档包含 {len(doc.paragraphs)} 个段落"
            }
        except ImportError:
            return {
                'type': 'word',
                'filename': filename,
                'error': '缺少Word处理库，请安装 python-docx',
                'summary': 'Word文件已上传，但无法提取内容'
            }

    except Exception as e:
        print(f"[ERROR] Word处理失败: {str(e)}")
        return {'type': 'word', 'error': f"Word处理失败: {str(e)}"}


async def save_to_knowledge_base(filename: str, file_type: str, processed_data: dict):
    """保存处理后的文件数据到知识库"""
    try:
        db_manager = get_db_manager()

        # 准备知识库数据
        knowledge_data = {
            'filename': filename,
            'file_type': file_type,
            'content': processed_data.get('full_content', processed_data.get('content', '')),
            'metadata': json.dumps({
                'summary': processed_data.get('summary', ''),
                'type': processed_data.get('type', file_type),
                'headers': processed_data.get('headers', []),
                'rows': processed_data.get('total_rows', 0),
                'columns': processed_data.get('columns', 0),
                'pages': processed_data.get('pages', 0),
                'encoding': processed_data.get('encoding', 'utf-8')
            }),
            'created_at': datetime.now().isoformat()
        }

        # 尝试保存到数据库（适配现有表结构）
        try:
            import uuid
            # 生成UUID作为ID
            doc_id = str(uuid.uuid4())

            # 直接使用NULL作为knowledge_base_id，避免外键约束问题
            result = db_manager.execute_query(
                """INSERT INTO knowledge_documents
                   (id, knowledge_base_id, title, content, content_type, doc_metadata, created_at)
                   VALUES (%s, %s, %s, %s, %s, %s, %s) RETURNING id""",
                [
                    doc_id,  # id - 手动生成UUID
                    None,  # knowledge_base_id - 使用NULL避免外键约束
                    knowledge_data['filename'],  # title - 使用文件名作为标题
                    knowledge_data['content'],   # content
                    knowledge_data['file_type'], # content_type - 使用文件类型
                    knowledge_data['metadata'],  # doc_metadata - JSON格式的元数据
                    knowledge_data['created_at'] # created_at
                ]
            )

            if result:
                try:
                    if isinstance(result[0], dict):
                        knowledge_id = result[0]['id']
                    elif isinstance(result[0], (list, tuple)):
                        knowledge_id = result[0][0]
                    else:
                        knowledge_id = str(result[0])
                    print(f"[DEBUG] 知识库保存成功，ID: {knowledge_id}")
                    return knowledge_id
                except (IndexError, KeyError, TypeError) as e:
                    print(f"[WARNING] 解析返回ID失败: {e}, result: {result}")
                    return None
            else:
                print("[WARNING] 数据库插入未返回ID")
                return None

        except Exception as db_error:
            print(f"[WARNING] 数据库保存失败，尝试创建表: {db_error}")

            # 尝试创建knowledge_documents表（使用现有结构）
            try:
                db_manager.execute_query("""
                    CREATE TABLE IF NOT EXISTS knowledge_documents (
                        id VARCHAR PRIMARY KEY DEFAULT gen_random_uuid()::text,
                        agent_id VARCHAR,
                        knowledge_base_id VARCHAR,
                        title VARCHAR NOT NULL,
                        content TEXT NOT NULL,
                        content_type VARCHAR DEFAULT 'text',
                        doc_metadata JSON DEFAULT '{}',
                        file_path VARCHAR,
                        file_size INTEGER DEFAULT 0,
                        chunks JSON DEFAULT '[]',
                        embeddings JSON DEFAULT '[]',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    );

                    -- 添加 agent_id 字段（如果不存在）
                    DO $$
                    BEGIN
                        IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                                     WHERE table_name='knowledge_documents' AND column_name='agent_id') THEN
                            ALTER TABLE knowledge_documents ADD COLUMN agent_id VARCHAR;
                        END IF;
                    END $$
                """)

                # 重新尝试插入（使用NULL避免外键约束）
                import uuid
                doc_id = str(uuid.uuid4())

                result = db_manager.execute_query(
                    """INSERT INTO knowledge_documents
                       (id, knowledge_base_id, title, content, content_type, doc_metadata, created_at)
                       VALUES (%s, %s, %s, %s, %s, %s, %s) RETURNING id""",
                    [
                        doc_id,  # id - 手动生成UUID
                        None,  # knowledge_base_id - 使用NULL避免外键约束
                        knowledge_data['filename'],  # title
                        knowledge_data['content'],   # content
                        knowledge_data['file_type'], # content_type
                        knowledge_data['metadata'],  # doc_metadata
                        knowledge_data['created_at'] # created_at
                    ]
                )

                if result:
                    try:
                        if isinstance(result[0], dict):
                            knowledge_id = result[0]['id']
                        elif isinstance(result[0], (list, tuple)):
                            knowledge_id = result[0][0]
                        else:
                            knowledge_id = str(result[0])
                        print(f"[DEBUG] 知识库保存成功（创建表后），ID: {knowledge_id}")
                        return knowledge_id
                    except (IndexError, KeyError, TypeError) as e:
                        print(f"[WARNING] 解析返回ID失败（创建表后）: {e}, result: {result}")
                        return None

            except Exception as create_error:
                print(f"[ERROR] 创建表失败: {create_error}")
                return None

    except Exception as e:
        print(f"[ERROR] 保存到知识库失败: {str(e)}")
        return None


async def ensure_default_knowledge_base(db_manager):
    """确保存在默认知识库 - 简化版本，直接返回NULL"""
    try:
        # 由于外键约束复杂，直接返回None，让knowledge_base_id为NULL
        # 这样可以避免复杂的外键关系问题
        print("[DEBUG] 使用NULL作为knowledge_base_id，避免外键约束问题")
        return None

    except Exception as e:
        print(f"[ERROR] 确保默认知识库失败: {str(e)}")
        return None


@router.post("")
async def create_agent(agent_data: dict):
    """创建新的智能体"""
    try:
        db_manager = get_db_manager()

        # 生成新的智能体ID
        import uuid
        agent_id = str(uuid.uuid4())

        # 准备智能体数据
        agent_name = agent_data.get('name', '新智能体')
        agent_description = agent_data.get('description', '')
        agent_type = agent_data.get('agent_type', 'general')
        system_prompt = agent_data.get('system_prompt', '')

        # 准备workflow_config
        workflow_config = {
            'ui_config': agent_data.get('ui_config', {}),
            'avatar': agent_data.get('avatar', '🤖'),
            'personality': agent_data.get('personality', 'friendly'),
            'domain': agent_data.get('domain', ''),
            'language': agent_data.get('language', 'zh-CN'),
            'memory': agent_data.get('memory', {}),
            'advanced': agent_data.get('advanced', {}),
            'safety': agent_data.get('safety', {}),
            'model_id': agent_data.get('model_id', 'gpt-3.5-turbo'),
            'use_custom_model': agent_data.get('use_custom_model', False),
            'custom_model_id': agent_data.get('custom_model_id', '')
        }

        # 插入数据库
        if db_manager.use_postgres:
            insert_query = """
                INSERT INTO true_agents
                (id, name, description, agent_type, system_prompt, workflow_config,
                 temperature, max_tokens, tools, knowledge_bases, memory_types,
                 workflow_enabled, usage_count, is_active, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
                RETURNING id
            """
        else:
            insert_query = """
                INSERT INTO true_agents
                (id, name, description, agent_type, system_prompt, workflow_config,
                 temperature, max_tokens, tools, knowledge_bases, memory_types,
                 workflow_enabled, usage_count, is_active, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
            """

        insert_params = [
            agent_id,
            agent_name,
            agent_description,
            agent_type,
            system_prompt,
            json.dumps(workflow_config),
            agent_data.get('temperature', 0.7),
            agent_data.get('max_tokens', 4000),
            json.dumps(agent_data.get('tools', [])),
            json.dumps(agent_data.get('knowledge_bases', [])),
            json.dumps(agent_data.get('memory_types', ['short_term', 'long_term'])),
            agent_data.get('workflow_enabled', True),
            0,  # usage_count
            True  # is_active
        ]

        result = db_manager.execute_query(insert_query, insert_params)

        if result is not None:
            print(f"[DEBUG] 成功创建智能体: {agent_id} - {agent_name}")

            # 返回创建的智能体信息
            return {
                "success": True,
                "message": "智能体创建成功",
                "agent": {
                    "id": agent_id,
                    "name": agent_name,
                    "description": agent_description,
                    "agent_type": agent_type,
                    "status": "active",
                    "created_at": datetime.now().isoformat()
                }
            }
        else:
            return {
                "success": False,
                "error": "创建智能体失败"
            }

    except Exception as e:
        print(f"[ERROR] 创建智能体失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }


@router.get("/{agent_id}/knowledge-documents")
async def get_agent_knowledge_documents(agent_id: str):
    """获取特定智能体的知识库文档"""
    try:
        db_manager = get_db_manager()

        # 检查智能体是否存在
        if db_manager.use_postgres:
            check_query = "SELECT * FROM true_agents WHERE id = %s AND is_active = true"
        else:
            check_query = "SELECT * FROM true_agents WHERE id = ? AND is_active = true"

        agents = db_manager.execute_query(check_query, (agent_id,))

        if not agents:
            return {
                "success": False,
                "error": "智能体不存在"
            }

        # 查询该智能体的知识库文档
        # 先检查表结构是否包含 agent_id 字段
        try:
            if db_manager.use_postgres:
                check_column_query = """
                    SELECT column_name FROM information_schema.columns
                    WHERE table_name = 'knowledge_documents' AND column_name = 'agent_id'
                """
            else:
                check_column_query = "PRAGMA table_info(knowledge_documents)"

            column_info = db_manager.execute_query(check_column_query)

            if db_manager.use_postgres:
                has_agent_id = len(column_info or []) > 0
            else:
                has_agent_id = any(col[1] == 'agent_id' for col in (column_info or []))

            if has_agent_id:
                # 表包含 agent_id 字段，使用新的查询
                if db_manager.use_postgres:
                    query = """SELECT * FROM knowledge_documents
                              WHERE agent_id = %s OR (agent_id IS NULL AND knowledge_base_id LIKE %s)
                              ORDER BY created_at DESC"""
                    params = (agent_id, f"{agent_id}-%")
                else:
                    query = """SELECT * FROM knowledge_documents
                              WHERE agent_id = ? OR (agent_id IS NULL AND knowledge_base_id LIKE ?)
                              ORDER BY created_at DESC"""
                    params = (agent_id, f"{agent_id}-%")
            else:
                # 表不包含 agent_id 字段，使用旧的查询（返回空结果，因为这是新功能）
                if db_manager.use_postgres:
                    query = """SELECT * FROM knowledge_documents WHERE 1=0"""
                else:
                    query = """SELECT * FROM knowledge_documents WHERE 1=0"""
                params = ()

        except Exception as check_error:
            print(f"[WARNING] 检查表结构失败: {check_error}")
            # 兜底查询
            if db_manager.use_postgres:
                query = """SELECT * FROM knowledge_documents WHERE 1=0"""
            else:
                query = """SELECT * FROM knowledge_documents WHERE 1=0"""
            params = ()

        documents = db_manager.execute_query(query, params)

        if documents is None:
            documents = []

        # 格式化文档数据
        formatted_docs = []
        for doc in documents:
            # 解析元数据
            metadata = doc.get('doc_metadata', {})
            if isinstance(metadata, str):
                try:
                    metadata = json.loads(metadata)
                except:
                    metadata = {}

            formatted_docs.append({
                "id": doc.get('id'),
                "title": doc.get('title'),
                "filename": doc.get('title'),  # 兼容前端
                "content_type": doc.get('content_type'),
                "file_type": doc.get('content_type'),  # 兼容前端
                "size": len(doc.get('content', '')),
                "created_at": str(doc.get('created_at', '')),
                "metadata": metadata,
                "agent_id": agent_id  # 标记所属智能体
            })

        print(f"[DEBUG] 获取智能体 {agent_id} 的知识库文档: {len(formatted_docs)} 个")

        return {
            "success": True,
            "documents": formatted_docs,
            "total": len(formatted_docs)
        }

    except Exception as e:
        print(f"[ERROR] 获取智能体知识库文档失败: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "documents": []
        }


@router.post("/{agent_id}/upload-file")
async def upload_agent_file(agent_id: str, file: UploadFile = File(...)):
    """上传文件到特定智能体的知识库"""
    try:
        db_manager = get_db_manager()

        # 检查智能体是否存在
        if db_manager.use_postgres:
            check_query = "SELECT * FROM true_agents WHERE id = %s AND is_active = true"
        else:
            check_query = "SELECT * FROM true_agents WHERE id = ? AND is_active = true"

        agents = db_manager.execute_query(check_query, (agent_id,))

        if not agents:
            return {
                "success": False,
                "error": "智能体不存在"
            }

        print(f"[DEBUG] 开始处理智能体 {agent_id} 的文件上传: {file.filename}")

        # 读取文件内容
        file_content = await file.read()
        file_size = len(file_content)

        # 验证文件大小
        max_size = 10 * 1024 * 1024  # 10MB
        if file_size > max_size:
            return {
                "success": False,
                "error": f"文件大小超过限制 ({max_size // 1024 // 1024}MB)"
            }

        # 处理文件内容
        content_text = ""
        file_type = file.content_type or "application/octet-stream"

        try:
            if file_type.startswith('text/') or file.filename.endswith(('.txt', '.md', '.csv')):
                content_text = file_content.decode('utf-8')
            elif file.filename.endswith(('.pdf', '.docx', '.xlsx')):
                # 这里可以添加更复杂的文档解析逻辑
                content_text = f"[{file_type}文件] {file.filename}"
            else:
                content_text = f"[二进制文件] {file.filename}"
        except:
            content_text = f"[无法解析的文件] {file.filename}"

        # 生成文档ID
        import uuid
        doc_id = str(uuid.uuid4())

        # 准备元数据
        metadata = {
            "original_filename": file.filename,
            "file_size": file_size,
            "content_type": file_type,
            "agent_id": agent_id,
            "upload_time": datetime.now().isoformat()
        }

        # 保存到数据库
        try:
            if db_manager.use_postgres:
                insert_query = """
                    INSERT INTO knowledge_documents
                    (id, agent_id, knowledge_base_id, title, content, content_type, doc_metadata, created_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, NOW())
                """
                insert_params = [
                    doc_id,
                    agent_id,  # 关联到特定智能体
                    f"{agent_id}-kb",  # 智能体专属知识库ID
                    file.filename,
                    content_text,
                    file_type,
                    json.dumps(metadata)
                ]
            else:
                insert_query = """
                    INSERT INTO knowledge_documents
                    (id, agent_id, knowledge_base_id, title, content, content_type, doc_metadata, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'))
                """
                insert_params = [
                    doc_id,
                    agent_id,  # 关联到特定智能体
                    f"{agent_id}-kb",  # 智能体专属知识库ID
                    file.filename,
                    content_text,
                    file_type,
                    json.dumps(metadata)
                ]

            result = db_manager.execute_query(insert_query, insert_params)

        except Exception as db_error:
            print(f"[ERROR] 数据库插入失败: {db_error}")
            # 尝试简化插入（不使用 RETURNING）
            try:
                if db_manager.use_postgres:
                    simple_insert = """
                        INSERT INTO knowledge_documents
                        (id, agent_id, title, content, content_type, created_at)
                        VALUES (%s, %s, %s, %s, %s, NOW())
                    """
                    simple_params = [doc_id, agent_id, file.filename, content_text, file_type]
                else:
                    simple_insert = """
                        INSERT INTO knowledge_documents
                        (id, agent_id, title, content, content_type, created_at)
                        VALUES (?, ?, ?, ?, ?, datetime('now'))
                    """
                    simple_params = [doc_id, agent_id, file.filename, content_text, file_type]

                result = db_manager.execute_query(simple_insert, simple_params)
                print(f"[DEBUG] 使用简化插入成功")

            except Exception as simple_error:
                print(f"[ERROR] 简化插入也失败: {simple_error}")
                result = None

        if result is not None:
            print(f"[DEBUG] 成功保存智能体 {agent_id} 的文档: {doc_id}")

            return {
                "success": True,
                "message": "文件上传成功",
                "document": {
                    "id": doc_id,
                    "title": file.filename,
                    "content_type": file_type,
                    "size": file_size,
                    "agent_id": agent_id,
                    "created_at": datetime.now().isoformat()
                }
            }
        else:
            return {
                "success": False,
                "error": "保存文件失败"
            }

    except Exception as e:
        print(f"[ERROR] 上传智能体文件失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }


@router.delete("/knowledge-documents/{document_id}")
async def delete_knowledge_document(document_id: str):
    """删除知识库文档"""
    try:
        db_manager = get_db_manager()

        # 检查文档是否存在
        if db_manager.use_postgres:
            check_query = "SELECT * FROM knowledge_documents WHERE id = %s"
        else:
            check_query = "SELECT * FROM knowledge_documents WHERE id = ?"

        documents = db_manager.execute_query(check_query, (document_id,))

        if not documents:
            return {
                "success": False,
                "error": "文档不存在"
            }

        # 删除文档
        if db_manager.use_postgres:
            delete_query = "DELETE FROM knowledge_documents WHERE id = %s"
        else:
            delete_query = "DELETE FROM knowledge_documents WHERE id = ?"

        result = db_manager.execute_query(delete_query, (document_id,))

        if result is not None:
            print(f"[DEBUG] 成功删除知识库文档: {document_id}")
            return {
                "success": True,
                "message": "文档删除成功"
            }
        else:
            return {
                "success": False,
                "error": "删除失败"
            }

    except Exception as e:
        print(f"[ERROR] 删除知识库文档失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }


@router.get("/knowledge-documents/{document_id}/preview")
async def preview_knowledge_document(document_id: str):
    """预览知识库文档内容"""
    try:
        db_manager = get_db_manager()

        # 查询文档
        if db_manager.use_postgres:
            query = "SELECT * FROM knowledge_documents WHERE id = %s"
        else:
            query = "SELECT * FROM knowledge_documents WHERE id = ?"

        documents = db_manager.execute_query(query, (document_id,))

        if not documents:
            return {
                "success": False,
                "error": "文档不存在"
            }

        document = documents[0]

        # 获取文档内容（限制长度以避免过大的响应）
        content = document.get('content', '')
        if len(content) > 5000:  # 限制预览内容长度
            content = content[:5000] + "...\n\n[内容已截断，完整内容请下载查看]"

        # 解析元数据
        metadata = document.get('doc_metadata', {})
        if isinstance(metadata, str):
            try:
                metadata = json.loads(metadata)
            except:
                metadata = {}

        return {
            "success": True,
            "document": {
                "id": document.get('id'),
                "title": document.get('title'),
                "content": content,
                "content_type": document.get('content_type'),
                "metadata": metadata,
                "created_at": str(document.get('created_at', '')),
                "file_size": len(document.get('content', '')),
                "preview_truncated": len(document.get('content', '')) > 5000
            }
        }

    except Exception as e:
        print(f"[ERROR] 预览知识库文档失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }


@router.get("/available-models")
async def get_available_models():
    """获取可用模型列表"""
    try:
        # 返回可用的模型列表
        models = [
            {
                "id": "gpt-3.5-turbo",
                "name": "GPT-3.5 Turbo",
                "provider": "OpenAI",
                "type": "chat",
                "description": "快速响应的对话模型"
            },
            {
                "id": "gpt-4",
                "name": "GPT-4",
                "provider": "OpenAI",
                "type": "chat",
                "description": "更强大的推理能力"
            },
            {
                "id": "claude-3-sonnet",
                "name": "Claude 3 Sonnet",
                "provider": "Anthropic",
                "type": "chat",
                "description": "平衡性能和成本"
            },
            {
                "id": "gemini-pro",
                "name": "Gemini Pro",
                "provider": "Google",
                "type": "chat",
                "description": "Google的多模态模型"
            }
        ]

        return {
            "success": True,
            "models": models
        }

    except Exception as e:
        print(f"[ERROR] 获取可用模型失败: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "models": []
        }


@router.get("/{agent_id}/model-config")
async def get_agent_model_config(agent_id: str):
    """获取智能体模型配置"""
    try:
        db_manager = get_db_manager()

        # 查询智能体配置
        if db_manager.use_postgres:
            query = "SELECT * FROM true_agents WHERE id = %s AND is_active = true"
        else:
            query = "SELECT * FROM true_agents WHERE id = ? AND is_active = true"

        agents = db_manager.execute_query(query, (agent_id,))

        if not agents:
            return {
                "success": False,
                "error": "智能体不存在"
            }

        agent_data = agents[0]

        # 解析workflow_config中的模型配置
        workflow_config = agent_data.get("workflow_config", {})
        if isinstance(workflow_config, str):
            try:
                workflow_config = json.loads(workflow_config)
            except:
                workflow_config = {}

        # 返回模型配置
        model_config = {
            "model_id": workflow_config.get("model_id", "gpt-3.5-turbo"),
            "temperature": agent_data.get("temperature", 0.7),
            "max_tokens": agent_data.get("max_tokens", 4000),
            "use_custom_model": workflow_config.get("use_custom_model", False),
            "custom_model_id": workflow_config.get("custom_model_id", ""),
            "system_prompt": agent_data.get("system_prompt", ""),
            "tools": agent_data.get("tools", [])
        }

        return {
            "success": True,
            "model_config": model_config,  # 前端期望的字段名
            "config": model_config  # 保持兼容性
        }

    except Exception as e:
        print(f"[ERROR] 获取智能体模型配置失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }


@router.put("/{agent_id}/model-config")
async def update_agent_model_config(agent_id: str, config: dict):
    """更新智能体模型配置"""
    try:
        db_manager = get_db_manager()

        # 检查智能体是否存在
        if db_manager.use_postgres:
            check_query = "SELECT * FROM true_agents WHERE id = %s AND is_active = true"
        else:
            check_query = "SELECT * FROM true_agents WHERE id = ? AND is_active = true"

        agents = db_manager.execute_query(check_query, (agent_id,))

        if not agents:
            return {
                "success": False,
                "error": "智能体不存在"
            }

        # 更新模型配置
        agent_data = agents[0]
        workflow_config = agent_data.get("workflow_config", {})
        if isinstance(workflow_config, str):
            try:
                workflow_config = json.loads(workflow_config)
            except:
                workflow_config = {}

        # 更新配置
        workflow_config.update({
            "model_id": config.get("model_id"),
            "use_custom_model": config.get("use_custom_model", False),
            "custom_model_id": config.get("custom_model_id", "")
        })

        # 保存到数据库
        if db_manager.use_postgres:
            update_query = """UPDATE true_agents SET
                           temperature = %s, max_tokens = %s, system_prompt = %s,
                           workflow_config = %s, updated_at = NOW()
                           WHERE id = %s AND is_active = true"""
        else:
            update_query = """UPDATE true_agents SET
                           temperature = ?, max_tokens = ?, system_prompt = ?,
                           workflow_config = ?, updated_at = datetime('now')
                           WHERE id = ? AND is_active = 1"""

        update_params = [
            config.get("temperature", 0.7),
            config.get("max_tokens", 4000),
            config.get("system_prompt", ""),
            json.dumps(workflow_config),
            agent_id
        ]

        result = db_manager.execute_query(update_query, update_params)

        if result is not None:
            return {
                "success": True,
                "message": "模型配置更新成功"
            }
        else:
            return {
                "success": False,
                "error": "更新失败"
            }

    except Exception as e:
        print(f"[ERROR] 更新智能体模型配置失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }


@router.put("/{agent_id}/memory-settings")
async def update_agent_memory_settings(agent_id: str, settings: dict):
    """更新智能体记忆设置"""
    try:
        db_manager = get_db_manager()

        # 检查智能体是否存在
        if db_manager.use_postgres:
            check_query = "SELECT * FROM true_agents WHERE id = %s AND is_active = true"
        else:
            check_query = "SELECT * FROM true_agents WHERE id = ? AND is_active = true"

        agents = db_manager.execute_query(check_query, (agent_id,))

        if not agents:
            return {
                "success": False,
                "error": "智能体不存在"
            }

        # 获取当前的workflow_config
        agent_data = agents[0]
        workflow_config = agent_data.get("workflow_config", {})
        if isinstance(workflow_config, str):
            try:
                workflow_config = json.loads(workflow_config)
            except:
                workflow_config = {}

        # 更新记忆设置
        workflow_config["memory"] = settings

        # 保存到数据库
        if db_manager.use_postgres:
            update_query = """UPDATE true_agents SET
                           workflow_config = %s, updated_at = NOW()
                           WHERE id = %s AND is_active = true"""
        else:
            update_query = """UPDATE true_agents SET
                           workflow_config = ?, updated_at = datetime('now')
                           WHERE id = ? AND is_active = 1"""

        update_params = [
            json.dumps(workflow_config),
            agent_id
        ]

        result = db_manager.execute_query(update_query, update_params)

        if result is not None:
            print(f"[DEBUG] 成功更新智能体记忆设置: {agent_id}")
            return {
                "success": True,
                "message": "记忆设置更新成功",
                "settings": settings
            }
        else:
            return {
                "success": False,
                "error": "更新失败"
            }

    except Exception as e:
        print(f"[ERROR] 更新智能体记忆设置失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }


@router.get("/{agent_id}/memory-settings")
async def get_agent_memory_settings(agent_id: str):
    """获取智能体记忆设置"""
    try:
        db_manager = get_db_manager()

        # 查询智能体配置
        if db_manager.use_postgres:
            query = "SELECT * FROM true_agents WHERE id = %s AND is_active = true"
        else:
            query = "SELECT * FROM true_agents WHERE id = ? AND is_active = true"

        agents = db_manager.execute_query(query, (agent_id,))

        if not agents:
            return {
                "success": False,
                "error": "智能体不存在"
            }

        agent_data = agents[0]

        # 解析workflow_config中的记忆设置
        workflow_config = agent_data.get("workflow_config", {})
        if isinstance(workflow_config, str):
            try:
                workflow_config = json.loads(workflow_config)
            except:
                workflow_config = {}

        # 获取记忆设置，如果不存在则返回默认值
        memory_settings = workflow_config.get("memory", {
            "short_term": {
                "enabled": True,
                "max_messages": 20,
                "retention_hours": 24
            },
            "long_term": {
                "enabled": True,
                "max_entries": 1000,
                "retention_days": 30
            },
            "episodic": {
                "enabled": False,
                "max_episodes": 100,
                "retention_days": 7
            },
            "procedural": {
                "enabled": False,
                "max_procedures": 50,
                "auto_learn": False
            },
            "semantic": {
                "enabled": True,
                "max_concepts": 500,
                "auto_extract": True
            },
            "working": {
                "enabled": True,
                "max_items": 10,
                "timeout_minutes": 30
            }
        })

        return {
            "success": True,
            "settings": memory_settings
        }

    except Exception as e:
        print(f"[ERROR] 获取智能体记忆设置失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }


@router.put("/{agent_id}/advanced-settings")
async def update_agent_advanced_settings(agent_id: str, settings: dict):
    """更新智能体高级设置"""
    try:
        db_manager = get_db_manager()

        # 检查智能体是否存在
        if db_manager.use_postgres:
            check_query = "SELECT * FROM true_agents WHERE id = %s AND is_active = true"
        else:
            check_query = "SELECT * FROM true_agents WHERE id = ? AND is_active = true"

        agents = db_manager.execute_query(check_query, (agent_id,))

        if not agents:
            return {
                "success": False,
                "error": "智能体不存在"
            }

        # 获取当前的workflow_config
        agent_data = agents[0]
        workflow_config = agent_data.get("workflow_config", {})
        if isinstance(workflow_config, str):
            try:
                workflow_config = json.loads(workflow_config)
            except:
                workflow_config = {}

        # 更新高级设置
        workflow_config["advanced"] = settings

        # 保存到数据库
        if db_manager.use_postgres:
            update_query = """UPDATE true_agents SET
                           workflow_config = %s, updated_at = NOW()
                           WHERE id = %s AND is_active = true"""
        else:
            update_query = """UPDATE true_agents SET
                           workflow_config = ?, updated_at = datetime('now')
                           WHERE id = ? AND is_active = 1"""

        update_params = [
            json.dumps(workflow_config),
            agent_id
        ]

        result = db_manager.execute_query(update_query, update_params)

        if result is not None:
            print(f"[DEBUG] 成功更新智能体高级设置: {agent_id}")
            return {
                "success": True,
                "message": "高级设置更新成功",
                "settings": settings
            }
        else:
            return {
                "success": False,
                "error": "更新失败"
            }

    except Exception as e:
        print(f"[ERROR] 更新智能体高级设置失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }


@router.get("/{agent_id}/advanced-settings")
async def get_agent_advanced_settings(agent_id: str):
    """获取智能体高级设置"""
    try:
        db_manager = get_db_manager()

        # 查询智能体配置
        if db_manager.use_postgres:
            query = "SELECT * FROM true_agents WHERE id = %s AND is_active = true"
        else:
            query = "SELECT * FROM true_agents WHERE id = ? AND is_active = true"

        agents = db_manager.execute_query(query, (agent_id,))

        if not agents:
            return {
                "success": False,
                "error": "智能体不存在"
            }

        agent_data = agents[0]

        # 解析workflow_config中的高级设置
        workflow_config = agent_data.get("workflow_config", {})
        if isinstance(workflow_config, str):
            try:
                workflow_config = json.loads(workflow_config)
            except:
                workflow_config = {}

        # 获取高级设置，如果不存在则返回默认值
        advanced_settings = workflow_config.get("advanced", {
            "context_window": 4000,
            "response_format": "markdown",
            "streaming": True,
            "function_calling": True,
            "parallel_function_calls": False,
            "max_function_calls": 10,
            "timeout_seconds": 30,
            "retry_attempts": 3,
            "fallback_model": "gpt-3.5-turbo",
            "custom_headers": {},
            "rate_limiting": {
                "enabled": False,
                "requests_per_minute": 60,
                "tokens_per_minute": 40000
            },
            "logging": {
                "enabled": True,
                "level": "info",
                "include_requests": False,
                "include_responses": False
            },
            "safety": {
                "content_filter": True,
                "pii_detection": True,
                "toxicity_filter": True,
                "custom_filters": []
            }
        })

        return {
            "success": True,
            "settings": advanced_settings
        }

    except Exception as e:
        print(f"[ERROR] 获取智能体高级设置失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }


# 旧的处理函数（已被新的真实处理函数替代，保留作为备用）
async def process_text_file_legacy(file_path: str, filename: str):
    """处理文本文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read(5000)  # 限制读取长度

        return {
            'type': 'text',
            'content': content,
            'length': len(content),
            'summary': f"文本文件包含 {len(content)} 个字符"
        }

    except Exception as e:
        return {'type': 'text', 'error': f"文本处理失败: {str(e)}"}


@router.post("/{agent_id}/knowledge-bases")
async def create_knowledge_base(agent_id: str, kb_data: KnowledgeBaseCreate):
    """为智能体创建知识库"""
    try:
        db_manager = get_db_manager()

        # 检查智能体是否存在
        agent = db_manager.execute_query(
            "SELECT * FROM true_agents WHERE id = %s AND is_active = true",
            [agent_id]
        )

        if not agent:
            return {"success": False, "error": "智能体不存在"}

        # 创建知识库记录
        kb_id = f"kb_{agent_id}_{int(time.time())}"

        # 这里应该调用 KnowledgeManager，但为了简化，先直接返回成功
        # 实际项目中应该：
        # from ...shared.services.knowledge_manager import KnowledgeManager
        # km = KnowledgeManager()
        # result = await km.create_knowledge_base(agent_id, kb_data.name, kb_data.type, kb_data.description)

        return {
            "success": True,
            "knowledge_base": {
                "id": kb_id,
                "name": kb_data.name,
                "type": kb_data.type,
                "description": kb_data.description,
                "document_count": 0,
                "created_at": datetime.now().isoformat()
            }
        }

    except Exception as e:
        print(f"[ERROR] 创建知识库失败: {str(e)}")
        return {"success": False, "error": str(e)}


@router.get("/{agent_id}/knowledge-bases")
async def get_knowledge_bases(agent_id: str):
    """获取智能体的知识库列表"""
    try:
        # 这里应该从数据库查询，但为了简化，返回模拟数据
        return {
            "success": True,
            "knowledge_bases": []
        }

    except Exception as e:
        print(f"[ERROR] 获取知识库列表失败: {str(e)}")
        return {"success": False, "error": str(e)}


@router.delete("/{agent_id}/knowledge-bases/{kb_id}")
async def delete_knowledge_base(agent_id: str, kb_id: str):
    """删除知识库"""
    try:
        # 这里应该从数据库删除，但为了简化，直接返回成功
        return {"success": True, "message": "知识库删除成功"}

    except Exception as e:
        print(f"[ERROR] 删除知识库失败: {str(e)}")
        return {"success": False, "error": str(e)}


@router.post("/{agent_id}/knowledge-bases/{kb_id}/documents")
async def add_document_to_kb(agent_id: str, kb_id: str, file: UploadFile = File(...)):
    """向知识库添加文档"""
    try:
        # 检查文件大小
        if file.size > 10 * 1024 * 1024:
            return {"success": False, "error": "文件大小不能超过10MB"}

        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix=f'.{file.filename.split(".")[-1]}') as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name

        try:
            # 处理文档（这里应该调用文档处理服务）
            file_extension = file.filename.split('.')[-1].lower()

            if file_extension in ['xlsx', 'xls']:
                result = await process_excel_file(temp_file_path, file.filename)
            elif file_extension == 'csv':
                result = await process_csv_file(temp_file_path, file.filename)
            else:
                result = await process_text_file(temp_file_path, file.filename)

            return {
                "success": True,
                "document": {
                    "id": f"doc_{kb_id}_{int(time.time())}",
                    "filename": file.filename,
                    "type": file_extension,
                    "processed_result": result,
                    "uploaded_at": datetime.now().isoformat()
                }
            }

        finally:
            # 清理临时文件
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    except Exception as e:
        print(f"[ERROR] 添加文档失败: {str(e)}")
        return {"success": False, "error": str(e)}


@router.post("/{agent_id}/advanced-chat")
async def advanced_chat_with_agent(agent_id: str, request: ChatRequest):
    """与智能体进行高级对话 - 集成情感分析"""
    try:
        print(f"[DEBUG] 智能体对话请求: {agent_id}, 消息: {request.message}")

        # 获取智能体信息
        agent_response = await get_agent_by_id(agent_id)
        if not agent_response.get("success"):
            raise HTTPException(status_code=404, detail="智能体不存在")

        agent = agent_response["agent"]

        # 使用高级AI聊天服务
        try:
            # 分析用户情感
            user_emotion = emotion_analyzer.analyze_emotion(request.message)

            # 构建对话历史（简化版）
            conversation_history = []

            # 构建上下文
            context = {
                "agent_id": agent_id,
                "agent_name": agent["name"],
                "agent_type": agent["agent_type"],
                "user_emotion": user_emotion
            }

            # 使用高级AI生成回复
            ai_response = await advanced_ai_chat.generate_response(
                user_input=request.message,
                conversation_history=conversation_history,
                user_emotion=user_emotion,
                context=context
            )

            print(f"[DEBUG] 高级AI回复: {ai_response}")

            return {
                "success": True,
                "response": ai_response,
                "emotion": user_emotion,
                "agent_info": {
                    "id": agent["id"],
                    "name": agent["name"],
                    "type": agent["agent_type"]
                }
            }

        except Exception as ai_error:
            print(f"[WARNING] 高级AI失败，使用Ollama备用: {ai_error}")

            # 回退到Ollama
            response = await ai_agent_service.chat_with_agent(agent, request.message)

            return {
                "success": True,
                "response": response,
                "emotion": "neutral",
                "agent_info": {
                    "id": agent["id"],
                    "name": agent["name"],
                    "type": agent["agent_type"]
                }
            }

    except HTTPException:
        raise
    except Exception as e:
        print(f"[ERROR] 智能体对话失败: {e}")
        raise HTTPException(status_code=500, detail=f"对话失败: {str(e)}")

@router.post("/{agent_id}/digital-human-chat")
async def digital_human_chat_with_agent(agent_id: str, request: ChatRequest):
    """与智能体进行数字人对话"""
    try:
        print(f"[DEBUG] 数字人智能体对话: {agent_id}, 消息: {request.message}")

        # 获取智能体信息
        agent_response = await get_agent_by_id(agent_id)
        if not agent_response.get("success"):
            raise HTTPException(status_code=404, detail="智能体不存在")

        agent = agent_response["agent"]

        # 创建数字人配置
        digital_human_config = {
            "name": f"{agent['name']} 数字人",
            "agent_id": agent_id,
            "agent_info": agent,
            "avatar_url": "/storage/digital_humans/avatar_22_20250725_172235.jpg",  # 使用最新生成的头像
            "voice_settings": {
                "selected_voice": "zh-cn-xiaoxiao",
                "voice_speed": 1.0,
                "voice_pitch": 1.0
            }
        }

        # 创建实时对话会话
        session_id = await conversation_manager.create_session(digital_human_config)

        if not session_id:
            raise HTTPException(status_code=500, detail="创建数字人会话失败")

        # 获取会话并发送消息
        session = conversation_manager.get_session(session_id)
        if session:
            # 模拟用户消息处理
            await session._on_final_speech(request.message)

        return {
            "success": True,
            "session_id": session_id,
            "message": "数字人会话已创建，正在生成回复...",
            "websocket_url": f"/api/v1/realtime/ws/conversation/{session_id}",
            "agent_info": {
                "id": agent["id"],
                "name": agent["name"],
                "type": agent["agent_type"]
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        print(f"[ERROR] 数字人智能体对话失败: {e}")
        raise HTTPException(status_code=500, detail=f"数字人对话失败: {str(e)}")

@router.get("/types")
async def get_agent_types():
    """获取智能体类型列表"""
    return {
        "success": True,
        "types": [
            {"id": "chat", "name": "对话助手", "description": "通用对话智能体"},
            {"id": "translation", "name": "翻译助手", "description": "专业翻译智能体"},
            {"id": "writing", "name": "写作助手", "description": "文案写作智能体"},
            {"id": "coding", "name": "编程助手", "description": "代码编写智能体"},
            {"id": "learning", "name": "学习助手", "description": "教育学习智能体"},
            {"id": "language_tutor", "name": "语言导师", "description": "语言学习智能体"}
        ]
    }

@router.get("/available-models")
async def get_available_models():
    """获取可用的模型列表"""
    try:
        from app.services.ollama_service import OllamaService
        ollama_service = OllamaService()

        # 获取本地Ollama模型
        models = ollama_service.list_models()

        # 格式化模型信息
        formatted_models = []
        for model in models:
            model_info = {
                "name": model.get("name", ""),
                "display_name": model.get("name", "").replace(":latest", ""),
                "size": model.get("size", 0),
                "modified_at": model.get("modified_at", ""),
                "description": get_model_description(model.get("name", "")),
                "recommended": model.get("name", "") in ["qwen2.5:7b", "mistral:7b", "llama3.2:3b"],
                "language_support": get_model_language_support(model.get("name", "")),
                "use_cases": get_model_use_cases(model.get("name", ""))
            }
            formatted_models.append(model_info)

        # 按推荐程度和大小排序
        formatted_models.sort(key=lambda x: (not x["recommended"], x["size"]))

        return {
            "success": True,
            "data": {
                "models": formatted_models,
                "total": len(formatted_models),
                "default_model": ollama_service.best_model,
                "ollama_status": "connected" if ollama_service.available else "disconnected"
            }
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "data": {
                "models": [],
                "total": 0,
                "default_model": "qwen2.5:7b",
                "ollama_status": "error"
            }
        }

def get_model_description(model_name: str) -> str:
    """获取模型描述"""
    descriptions = {
        "qwen2.5:7b": "阿里巴巴开源的中文大语言模型，7B参数，中文理解能力强",
        "mistral:7b": "Mistral AI开源的多语言模型，7B参数，性能均衡",
        "llama3.2:3b": "Meta开源的轻量级模型，3B参数，响应速度快",
        "deepseek-r1:8b": "DeepSeek推理模型，8B参数，逻辑推理能力强",
        "deepseek-ultra-fast:latest": "DeepSeek超快速模型，响应速度极快"
    }

    for key, desc in descriptions.items():
        if key in model_name:
            return desc

    return "本地训练或下载的自定义模型"

def get_model_language_support(model_name: str) -> list:
    """获取模型语言支持"""
    if "qwen" in model_name.lower():
        return ["中文", "英文"]
    elif "mistral" in model_name.lower():
        return ["英文", "法文", "德文", "西班牙文"]
    elif "llama" in model_name.lower():
        return ["英文", "多语言"]
    elif "deepseek" in model_name.lower():
        return ["中文", "英文"]
    else:
        return ["通用"]

def get_model_use_cases(model_name: str) -> list:
    """获取模型适用场景"""
    if "qwen" in model_name.lower():
        return ["中文对话", "翻译", "写作", "问答"]
    elif "mistral" in model_name.lower():
        return ["多语言对话", "代码生成", "推理"]
    elif "llama" in model_name.lower():
        return ["通用对话", "创意写作", "分析"]
    elif "deepseek" in model_name.lower():
        return ["代码生成", "逻辑推理", "数学计算"]
    else:
        return ["专业对话", "定制化服务"]

@router.put("/{agent_id}/model-config")
async def update_agent_model_config(agent_id: str, config: dict):
    """更新智能体的模型配置"""
    try:
        db_manager = get_db_manager()

        # 获取当前智能体信息
        agent = db_manager.execute_query(
            "SELECT * FROM agents WHERE id = ?",
            (agent_id,)
        )

        if not agent:
            raise HTTPException(status_code=404, detail="智能体不存在")

        # 更新模型配置
        current_config = agent[0].get("config", {}) if agent[0].get("config") else {}

        # 合并新的模型配置
        model_config = {
            "model_name": config.get("model_name", "qwen2.5:7b"),
            "temperature": config.get("temperature", 0.7),
            "max_tokens": config.get("max_tokens", 2048),
            "top_p": config.get("top_p", 0.9),
            "top_k": config.get("top_k", 40),
            "repeat_penalty": config.get("repeat_penalty", 1.1),
            "system_prompt": config.get("system_prompt", ""),
            "use_custom_model": config.get("use_custom_model", False),
            "custom_model_id": config.get("custom_model_id", "")
        }

        current_config["model"] = model_config

        # 更新数据库
        db_manager.execute_query(
            "UPDATE agents SET config = ? WHERE id = ?",
            (json.dumps(current_config), agent_id)
        )

        return {
            "success": True,
            "message": "模型配置更新成功",
            "config": model_config
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

@router.get("/{agent_id}/model-config")
async def get_agent_model_config(agent_id: str):
    """获取智能体的模型配置"""
    try:
        db_manager = get_db_manager()

        # 获取智能体信息
        agent = db_manager.execute_query(
            "SELECT * FROM agents WHERE id = ?",
            (agent_id,)
        )

        if not agent:
            raise HTTPException(status_code=404, detail="智能体不存在")

        agent_data = agent[0]
        config = agent_data.get("config", {}) if agent_data.get("config") else {}

        # 获取模型配置，如果不存在则使用默认值
        model_config = config.get("model", {
            "model_name": "qwen2.5:7b",
            "temperature": 0.7,
            "max_tokens": 2048,
            "top_p": 0.9,
            "top_k": 40,
            "repeat_penalty": 1.1,
            "system_prompt": "",
            "use_custom_model": False,
            "custom_model_id": ""
        })

        return {
            "success": True,
            "data": {
                "agent_id": agent_id,
                "agent_name": agent_data.get("name", ""),
                "model_config": model_config
            }
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }
