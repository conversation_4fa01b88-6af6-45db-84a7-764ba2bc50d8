#!/usr/bin/env python3
"""
直接测试save_to_knowledge_base函数
"""

import sys
import os
import asyncio
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.api.v1.ai_agent import save_to_knowledge_base
import json
from datetime import datetime

async def test_save_function():
    """测试save_to_knowledge_base函数"""
    
    print("🧪 测试save_to_knowledge_base函数")
    print("=" * 40)
    
    # 创建测试数据
    filename = "test_save_function.txt"
    file_type = "txt"
    processed_data = {
        "content": "这是测试save_to_knowledge_base函数的内容",
        "summary": "测试函数调用",
        "type": "text",
        "size": 100,
        "encoding": "utf-8"
    }
    
    print(f"📝 测试参数:")
    print(f"  文件名: {filename}")
    print(f"  文件类型: {file_type}")
    print(f"  处理数据: {processed_data}")
    
    try:
        print("🚀 调用save_to_knowledge_base函数...")
        result = await save_to_knowledge_base(filename, file_type, processed_data)
        
        print(f"📊 函数返回结果: {result}")
        
        if result:
            print(f"✅ 保存成功，知识库ID: {result}")
            return True
        else:
            print("❌ 保存失败，返回None")
            return False
            
    except Exception as e:
        print(f"❌ 函数调用出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 直接测试save_to_knowledge_base函数")
    print("=" * 50)
    
    # 运行异步测试
    result = asyncio.run(test_save_function())
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {'✅ 成功' if result else '❌ 失败'}")
    
    if result:
        print("🎉 save_to_knowledge_base函数工作正常！")
    else:
        print("⚠️ save_to_knowledge_base函数存在问题")
