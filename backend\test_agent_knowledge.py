#!/usr/bin/env python3
"""
测试智能体专属知识库功能
"""

import requests
import json
import io

def test_agent_knowledge_isolation():
    base_url = "http://localhost:8000/api/v1"
    
    # 测试用的两个智能体ID
    agent1_id = "e55f5e84-6d8b-4265-8e55-728bdb0d2455"
    agent2_id = "test-agent-2"
    
    print("🧪 测试智能体专属知识库功能")
    print("=" * 60)
    
    # 1. 创建第二个测试智能体
    print(f"\n1. 创建测试智能体 {agent2_id}")
    try:
        test_agent_data = {
            "name": "测试智能体2",
            "description": "用于测试知识库隔离的智能体",
            "agent_type": "general"
        }
        
        # 先尝试创建智能体
        create_response = requests.post(
            f"{base_url}/agents",
            json=test_agent_data,
            headers={"Content-Type": "application/json"}
        )
        
        if create_response.status_code == 200:
            print(f"   ✅ 测试智能体创建成功")
        else:
            print(f"   ⚠️  测试智能体可能已存在，继续测试")
            
    except Exception as e:
        print(f"   ⚠️  创建测试智能体失败: {e}，继续测试")
    
    # 2. 测试智能体1的知识库
    print(f"\n2. 测试智能体1 ({agent1_id}) 的知识库")
    try:
        # 获取智能体1的知识库文档
        response = requests.get(f"{base_url}/agents/{agent1_id}/knowledge-documents")
        print(f"   URL: {base_url}/agents/{agent1_id}/knowledge-documents")
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                docs = data.get('documents', [])
                print(f"   ✅ 智能体1知识库文档数量: {len(docs)}")
                for i, doc in enumerate(docs[:3]):
                    print(f"   📄 文档{i+1}: {doc.get('title', 'N/A')} (ID: {doc.get('id', 'N/A')[:8]}...)")
            else:
                print(f"   ❌ 获取失败: {data.get('error', 'Unknown error')}")
        else:
            print(f"   ❌ 请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
    
    # 3. 测试智能体2的知识库
    print(f"\n3. 测试智能体2 ({agent2_id}) 的知识库")
    try:
        # 获取智能体2的知识库文档
        response = requests.get(f"{base_url}/agents/{agent2_id}/knowledge-documents")
        print(f"   URL: {base_url}/agents/{agent2_id}/knowledge-documents")
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                docs = data.get('documents', [])
                print(f"   ✅ 智能体2知识库文档数量: {len(docs)}")
                for i, doc in enumerate(docs[:3]):
                    print(f"   📄 文档{i+1}: {doc.get('title', 'N/A')} (ID: {doc.get('id', 'N/A')[:8]}...)")
            else:
                print(f"   ❌ 获取失败: {data.get('error', 'Unknown error')}")
        else:
            print(f"   ❌ 请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
    
    # 4. 测试上传文件到智能体1
    print(f"\n4. 测试上传文件到智能体1")
    try:
        # 创建测试文件
        test_content = f"这是智能体1的专属文档内容。\n创建时间: {requests.get('http://worldtimeapi.org/api/timezone/Asia/Shanghai').json().get('datetime', 'unknown')}"
        test_file = io.BytesIO(test_content.encode('utf-8'))
        
        files = {
            'file': ('agent1_test.txt', test_file, 'text/plain')
        }
        
        response = requests.post(f"{base_url}/agents/{agent1_id}/upload-file", files=files)
        print(f"   URL: {base_url}/agents/{agent1_id}/upload-file")
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                doc = data.get('document', {})
                print(f"   ✅ 文件上传成功")
                print(f"   📄 文档ID: {doc.get('id', 'N/A')}")
                print(f"   📄 文档标题: {doc.get('title', 'N/A')}")
                print(f"   📄 所属智能体: {doc.get('agent_id', 'N/A')}")
                agent1_doc_id = doc.get('id')
            else:
                print(f"   ❌ 上传失败: {data.get('error', 'Unknown error')}")
                agent1_doc_id = None
        else:
            print(f"   ❌ 上传请求失败: {response.status_code}")
            agent1_doc_id = None
            
    except Exception as e:
        print(f"   ❌ 上传异常: {e}")
        agent1_doc_id = None
    
    # 5. 测试上传文件到智能体2
    print(f"\n5. 测试上传文件到智能体2")
    try:
        # 创建测试文件
        test_content = f"这是智能体2的专属文档内容。\n创建时间: {requests.get('http://worldtimeapi.org/api/timezone/Asia/Shanghai').json().get('datetime', 'unknown')}"
        test_file = io.BytesIO(test_content.encode('utf-8'))
        
        files = {
            'file': ('agent2_test.txt', test_file, 'text/plain')
        }
        
        response = requests.post(f"{base_url}/agents/{agent2_id}/upload-file", files=files)
        print(f"   URL: {base_url}/agents/{agent2_id}/upload-file")
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                doc = data.get('document', {})
                print(f"   ✅ 文件上传成功")
                print(f"   📄 文档ID: {doc.get('id', 'N/A')}")
                print(f"   📄 文档标题: {doc.get('title', 'N/A')}")
                print(f"   📄 所属智能体: {doc.get('agent_id', 'N/A')}")
                agent2_doc_id = doc.get('id')
            else:
                print(f"   ❌ 上传失败: {data.get('error', 'Unknown error')}")
                agent2_doc_id = None
        else:
            print(f"   ❌ 上传请求失败: {response.status_code}")
            agent2_doc_id = None
            
    except Exception as e:
        print(f"   ❌ 上传异常: {e}")
        agent2_doc_id = None
    
    # 6. 验证知识库隔离
    print(f"\n6. 验证知识库隔离效果")
    try:
        # 再次获取两个智能体的知识库
        response1 = requests.get(f"{base_url}/agents/{agent1_id}/knowledge-documents")
        response2 = requests.get(f"{base_url}/agents/{agent2_id}/knowledge-documents")
        
        if response1.status_code == 200 and response2.status_code == 200:
            data1 = response1.json()
            data2 = response2.json()
            
            docs1 = data1.get('documents', []) if data1.get('success') else []
            docs2 = data2.get('documents', []) if data2.get('success') else []
            
            print(f"   智能体1文档数量: {len(docs1)}")
            print(f"   智能体2文档数量: {len(docs2)}")
            
            # 检查是否有交叉污染
            agent1_titles = [doc.get('title', '') for doc in docs1]
            agent2_titles = [doc.get('title', '') for doc in docs2]
            
            cross_contamination = False
            if 'agent2_test.txt' in agent1_titles:
                print(f"   ❌ 发现交叉污染: 智能体1的知识库中包含智能体2的文档")
                cross_contamination = True
            if 'agent1_test.txt' in agent2_titles:
                print(f"   ❌ 发现交叉污染: 智能体2的知识库中包含智能体1的文档")
                cross_contamination = True
            
            if not cross_contamination:
                print(f"   ✅ 知识库隔离正常，无交叉污染")
            
        else:
            print(f"   ❌ 验证请求失败")
            
    except Exception as e:
        print(f"   ❌ 验证异常: {e}")
    
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("1. 智能体专属知识库API - 检查是否正常响应")
    print("2. 文件上传隔离 - 检查文件是否上传到正确的智能体")
    print("3. 知识库隔离 - 检查不同智能体的知识库是否独立")
    
    print(f"\n🌐 前端测试页面:")
    print(f"智能体1编辑器: http://192.168.1.143:3000/utilities/daily/ai-agent/editor?id={agent1_id}")
    print(f"智能体2编辑器: http://192.168.1.143:3000/utilities/daily/ai-agent/editor?id={agent2_id}")
    print(f"测试页面: file:///E:/workspace/AI_system/frontend/test-knowledge-functions.html")

if __name__ == "__main__":
    test_agent_knowledge_isolation()
