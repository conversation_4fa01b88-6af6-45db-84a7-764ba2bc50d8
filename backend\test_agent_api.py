#!/usr/bin/env python3
"""
测试智能体API的脚本
"""

import requests
import json

def test_agent_api():
    base_url = "http://localhost:8000/api/v1"
    agent_id = "e55f5e84-6d8b-4265-8e55-728bdb0d2455"
    
    print("🧪 测试智能体API")
    print("=" * 50)
    
    # 1. 测试获取智能体详情
    print("\n1. 测试获取智能体详情")
    try:
        response = requests.get(f"{base_url}/agents/{agent_id}")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   智能体名称: {data.get('name', 'N/A')}")
            print(f"   智能体类型: {data.get('agent_type', 'N/A')}")
            print("   ✅ 获取智能体详情成功")
        else:
            print(f"   ❌ 获取智能体详情失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    # 2. 测试获取可用模型
    print("\n2. 测试获取可用模型")
    try:
        response = requests.get(f"{base_url}/agents/available-models")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            models = data.get('models', [])
            print(f"   可用模型数量: {len(models)}")
            for model in models[:3]:  # 显示前3个
                print(f"   - {model.get('name', 'N/A')} ({model.get('provider', 'N/A')})")
            print("   ✅ 获取可用模型成功")
        else:
            print(f"   ❌ 获取可用模型失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    # 3. 测试获取模型配置
    print("\n3. 测试获取模型配置")
    try:
        response = requests.get(f"{base_url}/agents/{agent_id}/model-config")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                config = data.get('model_config', {})
                print(f"   模型ID: {config.get('model_id', 'N/A')}")
                print(f"   温度: {config.get('temperature', 'N/A')}")
                print(f"   最大令牌: {config.get('max_tokens', 'N/A')}")
                print("   ✅ 获取模型配置成功")
            else:
                print(f"   ❌ 获取模型配置失败: {data.get('error', 'Unknown error')}")
        else:
            print(f"   ❌ 获取模型配置失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    # 4. 测试更新模型配置
    print("\n4. 测试更新模型配置")
    try:
        test_config = {
            "model_id": "gpt-3.5-turbo",
            "temperature": 0.8,
            "max_tokens": 3000,
            "use_custom_model": False,
            "system_prompt": "你是一个测试助手"
        }
        
        response = requests.put(
            f"{base_url}/agents/{agent_id}/model-config",
            json=test_config,
            headers={"Content-Type": "application/json"}
        )
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("   ✅ 更新模型配置成功")
            else:
                print(f"   ❌ 更新模型配置失败: {data.get('error', 'Unknown error')}")
        else:
            print(f"   ❌ 更新模型配置失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    # 5. 测试知识库文档列表
    print("\n5. 测试知识库文档列表")
    try:
        response = requests.get(f"{base_url}/ai-agent/knowledge-documents")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            documents = data.get('documents', [])
            print(f"   文档数量: {len(documents)}")
            for doc in documents[:3]:  # 显示前3个
                print(f"   - {doc.get('title', 'N/A')} ({doc.get('content_type', 'N/A')})")
            print("   ✅ 获取知识库文档成功")
        else:
            print(f"   ❌ 获取知识库文档失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 API测试完成！")

if __name__ == "__main__":
    test_agent_api()
