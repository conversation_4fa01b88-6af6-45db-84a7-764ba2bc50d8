<template>
  <div class="language-learning-chat">
    <!-- 左侧边栏 - 学习资源管理 -->
    <div class="sidebar" :class="{ 'collapsed': sidebarCollapsed }">
      <div class="sidebar-header">
        <div class="logo">
          <Reading class="logo-icon" />
          <span v-if="!sidebarCollapsed" class="logo-text">外语学习</span>
        </div>
        <el-button
          text
          @click="sidebarCollapsed = !sidebarCollapsed"
          class="collapse-btn"
        >
          <Fold v-if="!sidebarCollapsed" />
          <Expand v-else />
        </el-button>
      </div>

      <!-- 学习设置 -->
      <div class="learning-settings" v-if="!sidebarCollapsed">
        <div class="settings-section">
          <h4>学习设置</h4>
          <div class="setting-item">
            <label>目标语言</label>
            <a-select v-model:value="learningSettings.targetLanguage" style="width: 100%">
              <a-select-option value="english">英语</a-select-option>
              <a-select-option value="chinese">中文</a-select-option>
              <a-select-option value="japanese">日语</a-select-option>
              <a-select-option value="korean">韩语</a-select-option>
              <a-select-option value="french">法语</a-select-option>
              <a-select-option value="german">德语</a-select-option>
              <a-select-option value="spanish">西班牙语</a-select-option>
            </a-select>
          </div>

          <div class="setting-item">
            <label>语言水平</label>
            <a-select v-model:value="learningSettings.level" style="width: 100%">
              <a-select-option value="beginner">初学者</a-select-option>
              <a-select-option value="elementary">基础</a-select-option>
              <a-select-option value="intermediate">中级</a-select-option>
              <a-select-option value="upper-intermediate">中高级</a-select-option>
              <a-select-option value="advanced">高级</a-select-option>
              <a-select-option value="proficient">精通</a-select-option>
            </a-select>
          </div>

          <div class="setting-item">
            <label>口音偏好</label>
            <a-select v-model:value="learningSettings.accent" style="width: 100%">
              <a-select-option value="american">美式</a-select-option>
              <a-select-option value="british">英式</a-select-option>
              <a-select-option value="australian">澳式</a-select-option>
              <a-select-option value="canadian">加拿大式</a-select-option>
            </a-select>
          </div>
        </div>

        <!-- 学习资源 -->
        <div class="resources-section">
          <h4>学习资源</h4>

          <!-- 术语库 -->
          <div class="resource-item">
            <div class="resource-header">
              <Reading />
              <span>术语库</span>
              <el-button text size="small" @click="showTerminologyUpload = true">
                <Plus />
              </el-button>
            </div>
            <div class="resource-list">
              <div
                v-for="terminology in terminologies"
                :key="terminology.id"
                :class="['terminology-item', { active: selectedTerminology === terminology.id }]"
                @click="selectTerminology(terminology.id)"
              >
                <div class="terminology-name">{{ terminology.name }}</div>
                <div class="terminology-count">{{ terminology.term_count }} 个术语</div>
              </div>
            </div>
          </div>

          <!-- 学习材料 -->
          <div class="resource-item">
            <div class="resource-header">
              <Document />
              <span>学习材料</span>
              <el-button text size="small" @click="showMaterialUpload = true">
                <Plus />
              </el-button>
            </div>
            <div class="resource-list">
              <div
                v-for="material in learningMaterials"
                :key="material.id"
                :class="['material-item', { active: selectedMaterial === material.id }]"
                @click="selectMaterial(material.id)"
              >
                <div class="material-name">{{ material.name }}</div>
                <div class="material-type">{{ material.type }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 学习模式 -->
        <div class="learning-modes">
          <h4>学习模式</h4>
          <div class="mode-buttons">
            <el-button
              :type="learningMode === 'conversation' ? 'primary' : 'default'"
              @click="setLearningMode('conversation')"
              style="width: 100%; margin-bottom: 8px;"
            >
              💬 对话练习
            </el-button>
            <el-button
              :type="learningMode === 'vocabulary' ? 'primary' : 'default'"
              @click="setLearningMode('vocabulary')"
              style="width: 100%; margin-bottom: 8px;"
            >
              📚 词汇学习
            </el-button>
            <el-button
              :type="learningMode === 'grammar' ? 'primary' : 'default'"
              @click="setLearningMode('grammar')"
              style="width: 100%; margin-bottom: 8px;"
            >
              📝 语法练习
            </el-button>
            <el-button
              :type="learningMode === 'pronunciation' ? 'primary' : 'default'"
              @click="setLearningMode('pronunciation')"
              style="width: 100%;"
            >
              🎤 发音练习
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 主聊天区域 -->
    <div class="main-chat">
      <!-- 顶部信息栏 -->
      <div class="chat-header">
        <div class="learning-info">
          <h3>{{ agent?.name || '外语学习助手' }}</h3>
          <div class="learning-status">
            <span class="language">{{ getLanguageName(learningSettings.targetLanguage) }}</span>
            <span class="level">{{ getLevelName(learningSettings.level) }}</span>
            <span class="mode">{{ getModeName(learningMode) }}</span>
          </div>
        </div>

        <div class="header-actions">
          <el-tooltip content="学习统计">
            <el-button text @click="showStats = true" class="action-btn">
              <TrendCharts />
            </el-button>
          </el-tooltip>
          <el-tooltip content="学习设置">
            <el-button text @click="showSettings = true" class="action-btn">
              <Setting />
            </el-button>
          </el-tooltip>
        </div>
      </div>

      <!-- 聊天消息区域 -->
      <div class="chat-messages" ref="messagesContainer">
        <div class="messages-container">
          <!-- 欢迎消息 -->
          <div v-if="messages.length === 0" class="welcome-screen">
            <div class="welcome-icon">
              <Location />
            </div>
            <h2>欢迎来到外语学习助手！</h2>
            <p>我是您的专属外语学习伙伴，可以帮助您进行对话练习、词汇学习、语法纠正和发音指导。</p>

            <div class="learning-features">
              <div class="feature-card" @click="startConversationPractice">
                <div class="feature-icon">💬</div>
                <div class="feature-title">对话练习</div>
                <div class="feature-desc">真实场景对话，提升口语表达</div>
              </div>
              <div class="feature-card" @click="startVocabularyLearning">
                <div class="feature-icon">📚</div>
                <div class="feature-title">词汇学习</div>
                <div class="feature-desc">基于术语库的词汇扩展</div>
              </div>
              <div class="feature-card" @click="startGrammarPractice">
                <div class="feature-icon">📝</div>
                <div class="feature-title">语法练习</div>
                <div class="feature-desc">语法规则学习和纠错</div>
              </div>
              <div class="feature-card" @click="startPronunciationPractice">
                <div class="feature-icon">🎤</div>
                <div class="feature-title">发音练习</div>
                <div class="feature-desc">语音识别和发音指导</div>
              </div>
            </div>
          </div>

          <!-- 消息列表 -->
          <div v-for="(message, index) in messages" :key="message.id" class="message-group">
            <div :class="['message-wrapper', message.sender_type]">
              <!-- 用户消息 -->
              <div v-if="message.sender_type === 'user'" class="user-message">
                <div class="message-content">
                  <div class="message-text">{{ message.content }}</div>
                  <!-- 语音波形显示（如果有录音） -->
                  <div v-if="message.audio_url" class="audio-waveform">
                    <audio :src="message.audio_url" controls></audio>
                  </div>
                </div>
                <div class="message-avatar">
                  <div class="user-avatar">
                    <User />
                  </div>
                </div>
              </div>

              <!-- AI消息 -->
              <div v-else class="ai-message">
                <div class="message-avatar">
                  <div class="ai-avatar">
                    <Location />
                  </div>
                </div>
                <div class="message-content">
                  <div class="message-header">
                    <span class="sender-name">外语学习助手</span>
                    <span class="message-time">{{ formatTime(message.created_at) }}</span>
                  </div>

                  <div v-if="message.isTyping" class="typing-indicator">
                    <div class="typing-animation">
                      <span></span>
                      <span></span>
                      <span></span>
                    </div>
                    <span class="typing-text">正在分析您的语言表达...</span>
                  </div>

                  <div v-else class="message-text" v-html="formatLearningMessage(message.content)"></div>

                  <!-- 学习反馈卡片 -->
                  <div v-if="message.learning_feedback && !message.isTyping" class="learning-feedback">
                    <div class="feedback-header">
                      <Trophy />
                      <span>学习反馈</span>
                    </div>
                    <div class="feedback-scores">
                      <div class="score-item">
                        <span class="score-label">准确度</span>
                        <div class="score-bar">
                          <div class="score-fill" :style="{ width: message.learning_feedback.accuracy + '%' }"></div>
                        </div>
                        <span class="score-value">{{ message.learning_feedback.accuracy }}%</span>
                      </div>
                      <div class="score-item">
                        <span class="score-label">流利度</span>
                        <div class="score-bar">
                          <div class="score-fill" :style="{ width: message.learning_feedback.fluency + '%' }"></div>
                        </div>
                        <span class="score-value">{{ message.learning_feedback.fluency }}%</span>
                      </div>
                      <div class="score-item">
                        <span class="score-label">语法</span>
                        <div class="score-bar">
                          <div class="score-fill" :style="{ width: message.learning_feedback.grammar + '%' }"></div>
                        </div>
                        <span class="score-value">{{ message.learning_feedback.grammar }}%</span>
                      </div>
                    </div>
                    <div v-if="message.learning_feedback.suggestions" class="feedback-suggestions">
                      <h5>💡 学习建议</h5>
                      <ul>
                        <li v-for="suggestion in message.learning_feedback.suggestions" :key="suggestion">
                          {{ suggestion }}
                        </li>
                      </ul>
                    </div>
                  </div>

                  <div v-if="!message.isTyping" class="message-actions">
                    <el-tooltip content="复制">
                      <el-button text size="small" @click="copyText(message.content)" class="action-btn">
                        <CopyDocument />
                      </el-button>
                    </el-tooltip>
                    <el-tooltip content="朗读">
                      <el-button text size="small" @click="speakText(message.content)" class="action-btn">
                        <Microphone />
                      </el-button>
                    </el-tooltip>
                    <el-tooltip content="语法分析">
                      <el-button text size="small" @click="analyzeGrammar(message.content)" class="action-btn">
                        <Search />
                      </el-button>
                    </el-tooltip>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 智能输入区域 -->
      <div class="chat-input-area">
        <div class="input-container">
          <!-- 学习模式提示 -->
          <div v-if="learningMode !== 'conversation'" class="mode-hint">
            <div class="hint-content">
              <span class="hint-icon">{{ getModeIcon(learningMode) }}</span>
              <span class="hint-text">{{ getModeHint(learningMode) }}</span>
            </div>
          </div>

          <!-- 输入框 -->
          <div class="input-box" :class="{ 'focused': inputFocused, 'has-content': inputText.trim() }">
            <div class="input-wrapper">
              <a-textarea
                ref="inputRef"
                v-model:value="inputText"
                :placeholder="getInputPlaceholder()"
                :auto-size="{ minRows: 1, maxRows: 6 }"
                @keydown="handleKeydown"
                @focus="inputFocused = true"
                @blur="inputFocused = false"
                :disabled="isLoading"
                class="message-input"
              />

              <!-- 右侧按钮组 -->
              <div class="input-actions">
                <!-- 语音输入 -->
                <el-tooltip content="语音输入">
                  <el-button
                    text
                    size="small"
                    class="tool-btn"
                    :class="{ 'recording': isRecording }"
                    @click="toggleVoiceInput"
                  >
                    <Microphone v-if="!isRecording" />
                    <Loading v-else />
                  </el-button>
                </el-tooltip>

                <!-- 翻译输入 -->
                <el-tooltip content="翻译输入">
                  <el-button text size="small" class="tool-btn" @click="translateInput">
                    <Switch />
                  </el-button>
                </el-tooltip>

                <!-- 语法检查 -->
                <el-tooltip content="语法检查">
                  <el-button text size="small" class="tool-btn" @click="checkGrammar">
                    <SuccessFilled />
                  </el-button>
                </el-tooltip>

                <div class="send-button-wrapper">
                  <el-button
                    type="primary"
                    :loading="isLoading"
                    @click="sendMessage"
                    :disabled="!inputText.trim()"
                    class="send-btn"
                    circle
                  >
                    <Promotion />
                  </el-button>
                </div>
              </div>
            </div>

            <!-- 底部提示和统计 -->
            <div class="input-footer">
              <div class="input-hints">
                <span class="hint-item">
                  <kbd>Enter</kbd> 发送
                </span>
                <span class="hint-item">
                  <kbd>Ctrl</kbd> + <kbd>T</kbd> 翻译
                </span>
                <span class="hint-item">
                  <kbd>Ctrl</kbd> + <kbd>G</kbd> 语法检查
                </span>
              </div>

              <div class="input-status">
                <span class="char-count" :class="{ 'warning': inputText.length > 1800 }">
                  {{ inputText.length }}/2000
                </span>
                <span class="word-count">{{ getWordCount(inputText) }} 词</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 术语库上传弹窗 -->
    <el-dialog
      v-model="showTerminologyUpload"
      title="上传术语库"
      width="500px"
    >
      <div class="upload-section">
        <el-upload
          v-model:file-list="terminologyFiles"
          :before-upload="beforeUploadTerminology"
          accept=".xlsx,.xls,.csv,.txt"
          :auto-upload="false"
        >
          <el-button>
            <Upload />
            选择术语文件
          </el-button>
        </el-upload>
        <div class="upload-hint">
          支持 Excel (.xlsx, .xls)、CSV (.csv) 和文本 (.txt) 格式
        </div>
      </div>
      <template #footer>
        <el-button @click="showTerminologyUpload = false">取消</el-button>
        <el-button type="primary" @click="uploadTerminology">确定</el-button>
      </template>
    </el-dialog>

    <!-- 学习材料上传弹窗 -->
    <el-dialog
      v-model="showMaterialUpload"
      title="上传学习材料"
      width="500px"
    >
      <div class="upload-section">
        <el-upload
          v-model:file-list="materialFiles"
          :before-upload="beforeUploadMaterial"
          accept=".pdf,.doc,.docx,.txt,.epub"
          :auto-upload="false"
        >
          <el-button>
            <Upload />
            选择学习材料
          </el-button>
        </el-upload>
        <div class="upload-hint">
          支持 PDF、Word 文档、文本文件和 EPUB 格式
        </div>
      </div>
      <template #footer>
        <el-button @click="showMaterialUpload = false">取消</el-button>
        <el-button type="primary" @click="uploadMaterial">确定</el-button>
      </template>
    </el-dialog>

    <!-- 学习统计弹窗 -->
    <a-modal
      v-model:open="showStats"
      title="学习统计"
      :footer="null"
      width="600px"
    >
      <div class="stats-content">
        <div class="stats-overview">
          <div class="stat-card">
            <div class="stat-number">{{ learningStats.totalSessions }}</div>
            <div class="stat-label">学习会话</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ learningStats.totalWords }}</div>
            <div class="stat-label">学习词汇</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ learningStats.averageScore }}%</div>
            <div class="stat-label">平均分数</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ learningStats.studyTime }}</div>
            <div class="stat-label">学习时长</div>
          </div>
        </div>

        <div class="progress-chart">
          <h4>学习进度</h4>
          <!-- 这里可以集成图表组件 -->
          <div class="chart-placeholder">
            📊 学习进度图表（可集成 ECharts 或其他图表库）
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, nextTick, watch } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Reading,
  Location,
  User,
  Promotion,
  Microphone,
  CopyDocument,
  Setting,
  Fold,
  Expand,
  Plus,
  Document,
  Loading,
  Switch,
  SuccessFilled,
  Upload,
  Trophy,
  TrendCharts,
  Search
} from '@element-plus/icons-vue'
import agentService from '@/services/agent-service.js'

export default {
  name: 'LanguageLearningChat',
  components: {
    Reading,
    Location,
    User,
    Promotion,
    Microphone,
    CopyDocument,
    Setting,
    Fold,
    Expand,
    Plus,
    Document,
    Loading,
    Switch,
    SuccessFilled,
    Upload,
    Trophy,
    TrendCharts,
    Search
  },

  setup() {
    const route = useRoute()
    const messagesContainer = ref(null)
    const inputRef = ref(null)

    // 基础状态
    const agent = ref(null)
    const messages = ref([])
    const inputText = ref('')
    const isLoading = ref(false)
    const inputFocused = ref(false)
    const isRecording = ref(false)
    const sidebarCollapsed = ref(false)

    // 学习设置
    const learningSettings = reactive({
      targetLanguage: 'english',
      level: 'intermediate',
      accent: 'american'
    })

    // 学习模式
    const learningMode = ref('conversation')

    // 资源管理
    const terminologies = ref([])
    const learningMaterials = ref([])
    const selectedTerminology = ref(null)
    const selectedMaterial = ref(null)

    // 弹窗状态
    const showTerminologyUpload = ref(false)
    const showMaterialUpload = ref(false)
    const showStats = ref(false)
    const showSettings = ref(false)

    // 文件上传
    const terminologyFiles = ref([])
    const materialFiles = ref([])

    // 学习统计
    const learningStats = reactive({
      totalSessions: 0,
      totalWords: 0,
      averageScore: 0,
      studyTime: '0h 0m'
    })

    // 计算属性
    const getLanguageName = (code) => {
      const languages = {
        english: '英语',
        chinese: '中文',
        japanese: '日语',
        korean: '韩语',
        french: '法语',
        german: '德语',
        spanish: '西班牙语'
      }
      return languages[code] || '英语'
    }

    const getLevelName = (level) => {
      const levels = {
        beginner: '初学者',
        elementary: '基础',
        intermediate: '中级',
        'upper-intermediate': '中高级',
        advanced: '高级',
        proficient: '精通'
      }
      return levels[level] || '中级'
    }

    const getModeName = (mode) => {
      const modes = {
        conversation: '对话练习',
        vocabulary: '词汇学习',
        grammar: '语法练习',
        pronunciation: '发音练习'
      }
      return modes[mode] || '对话练习'
    }

    const getModeIcon = (mode) => {
      const icons = {
        conversation: '💬',
        vocabulary: '📚',
        grammar: '📝',
        pronunciation: '🎤'
      }
      return icons[mode] || '💬'
    }

    const getModeHint = (mode) => {
      const hints = {
        vocabulary: '尝试使用新学的词汇进行表达',
        grammar: '注意语法结构的正确性',
        pronunciation: '可以使用语音输入来练习发音'
      }
      return hints[mode] || ''
    }

    const getInputPlaceholder = () => {
      const placeholders = {
        conversation: '开始用目标语言对话...',
        vocabulary: '使用新词汇造句...',
        grammar: '练习语法结构...',
        pronunciation: '点击语音按钮练习发音...'
      }
      return placeholders[learningMode.value] || '开始学习对话...'
    }

    const getWordCount = (text) => {
      return text.trim().split(/\s+/).filter(word => word.length > 0).length
    }

    // 学习功能
    const setLearningMode = (mode) => {
      learningMode.value = mode
      ElMessage.info(`已切换到${getModeName(mode)}模式`)
    }

    const selectTerminology = (id) => {
      selectedTerminology.value = id
      const terminology = terminologies.value.find(t => t.id === id)
      if (terminology) {
        ElMessage.success(`已选择术语库：${terminology.name}`)
      }
    }

    const selectMaterial = (id) => {
      selectedMaterial.value = id
      const material = learningMaterials.value.find(m => m.id === id)
      if (material) {
        ElMessage.success(`已选择学习材料：${material.name}`)
      }
    }

    // 快速开始功能
    const startConversationPractice = () => {
      setLearningMode('conversation')
      inputText.value = "Hello! I'd like to practice conversation."
      sendMessage()
    }

    const startVocabularyLearning = () => {
      setLearningMode('vocabulary')
      inputText.value = "I want to learn new vocabulary words."
      sendMessage()
    }

    const startGrammarPractice = () => {
      setLearningMode('grammar')
      inputText.value = "Can you help me practice grammar?"
      sendMessage()
    }

    const startPronunciationPractice = () => {
      setLearningMode('pronunciation')
      inputText.value = "I need help with pronunciation."
      sendMessage()
    }

    // 输入处理
    const handleKeydown = (event) => {
      if (event.key === 'Enter') {
        if (event.shiftKey) {
          return // 换行
        } else {
          event.preventDefault()
          sendMessage()
        }
      } else if (event.ctrlKey && event.key === 't') {
        event.preventDefault()
        translateInput()
      } else if (event.ctrlKey && event.key === 'g') {
        event.preventDefault()
        checkGrammar()
      }
    }

    // 语音输入
    const toggleVoiceInput = () => {
      if (!isRecording.value) {
        startVoiceInput()
      } else {
        stopVoiceInput()
      }
    }

    const startVoiceInput = () => {
      if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
        message.error('当前浏览器不支持语音识别功能')
        return
      }

      try {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
        const recognition = new SpeechRecognition()

        // 根据目标语言设置识别语言
        const langCodes = {
          english: 'en-US',
          chinese: 'zh-CN',
          japanese: 'ja-JP',
          korean: 'ko-KR',
          french: 'fr-FR',
          german: 'de-DE',
          spanish: 'es-ES'
        }

        recognition.lang = langCodes[learningSettings.targetLanguage] || 'en-US'
        recognition.continuous = false
        recognition.interimResults = false

        recognition.onstart = () => {
          isRecording.value = true
          message.info(`开始${getLanguageName(learningSettings.targetLanguage)}语音识别...`)
        }

        recognition.onresult = (event) => {
          if (event.results.length > 0) {
            const result = event.results[0][0].transcript
            inputText.value += result
            message.success('语音识别成功')
          }
        }

        recognition.onerror = (event) => {
          console.error('语音识别错误:', event.error)
          message.error('语音识别失败，请重试')
          isRecording.value = false
        }

        recognition.onend = () => {
          isRecording.value = false
        }

        recognition.start()
      } catch (error) {
        console.error('启动语音识别失败:', error)
        message.error('启动语音识别失败')
        isRecording.value = false
      }
    }

    const stopVoiceInput = () => {
      isRecording.value = false
    }

    // 翻译和语法检查
    const translateInput = () => {
      if (!inputText.value.trim()) {
        message.warning('请先输入要翻译的内容')
        return
      }

      message.info('翻译功能开发中...')
      // TODO: 集成翻译API
    }

    const checkGrammar = () => {
      if (!inputText.value.trim()) {
        message.warning('请先输入要检查的内容')
        return
      }

      message.info('语法检查功能开发中...')
      // TODO: 集成语法检查API
    }

    // 消息处理
    const formatTime = (timestamp) => {
      const date = new Date(timestamp)
      return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    const formatLearningMessage = (content) => {
      if (!content) return ''

      // 处理学习反馈格式
      let formatted = content.replace(/\n/g, '<br>')

      // 高亮重要词汇
      formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong class="highlight-word">$1</strong>')

      // 处理语法纠正
      formatted = formatted.replace(/\[错误\](.*?)\[\/错误\]/g, '<span class="grammar-error">$1</span>')
      formatted = formatted.replace(/\[正确\](.*?)\[\/正确\]/g, '<span class="grammar-correct">$1</span>')

      return formatted
    }

    const sendMessage = async () => {
      const text = inputText.value.trim()
      if (!text || isLoading.value) return

      // 添加用户消息
      const userMessage = {
        id: `user-${Date.now()}`,
        content: text,
        sender_type: 'user',
        created_at: new Date().toISOString()
      }

      messages.value.push(userMessage)
      inputText.value = ''
      scrollToBottom()

      // 添加思考状态
      const typingMessage = {
        id: `typing-${Date.now()}`,
        content: '',
        sender_type: 'agent',
        created_at: new Date().toISOString(),
        isTyping: true
      }

      messages.value.push(typingMessage)
      isLoading.value = true
      scrollToBottom()

      try {
        // 构建学习请求
        const learningRequest = {
          content: text,
          sender_type: 'user',
          language_learning_mode: true,
          target_language: learningSettings.targetLanguage,
          language_level: learningSettings.level,
          accent: learningSettings.accent,
          learning_mode: learningMode.value,
          selected_terminology: selectedTerminology.value,
          selected_material: selectedMaterial.value
        }

        // 调用API（这里使用模拟响应）
        await new Promise(resolve => setTimeout(resolve, 2000))

        // 移除思考状态
        messages.value = messages.value.filter(msg => !msg.isTyping)

        // 生成学习响应
        const learningResponse = generateLearningResponse(text, learningMode.value)
        messages.value.push(learningResponse)

        scrollToBottom()

      } catch (error) {
        console.error('发送消息失败:', error)
        messages.value = messages.value.filter(msg => !msg.isTyping)
        message.error('发送失败，请重试')
      } finally {
        isLoading.value = false
      }
    }

    const generateLearningResponse = (userInput, mode) => {
      const responses = {
        conversation: {
          content: `很好的表达！让我们继续对话。关于"${userInput}"，我想了解更多细节。你能告诉我更多吗？`,
          feedback: {
            accuracy: 92,
            fluency: 88,
            grammar: 95,
            suggestions: [
              '尝试使用更多的连接词来让表达更流畅',
              '可以加入一些情感表达让对话更自然'
            ]
          }
        },
        vocabulary: {
          content: `很好！我注意到你使用了一些重要词汇。让我为你扩展相关词汇：\n\n**核心词汇：** ${userInput.split(' ').slice(0, 3).join(', ')}\n**同义词：** excellent, outstanding, remarkable\n**反义词：** poor, inadequate, insufficient`,
          feedback: {
            accuracy: 90,
            fluency: 85,
            grammar: 93,
            suggestions: [
              '尝试在句子中使用刚学的同义词',
              '练习用新词汇造更复杂的句子'
            ]
          }
        },
        grammar: {
          content: `语法分析：\n\n✅ **正确使用：** 时态运用准确\n⚠️ **需要注意：** 主谓一致\n📝 **建议改进：** "${userInput}" → "更正后的表达"\n\n继续练习这种语法结构！`,
          feedback: {
            accuracy: 88,
            fluency: 82,
            grammar: 90,
            suggestions: [
              '多练习复杂句型的构造',
              '注意动词时态的一致性'
            ]
          }
        },
        pronunciation: {
          content: `发音分析：\n\n🎤 **整体评价：** 发音清晰度良好\n📊 **重点音素：** /θ/, /ð/, /r/\n💡 **练习建议：** 多练习舌尖音的发音\n\n请继续用语音输入练习！`,
          feedback: {
            accuracy: 85,
            fluency: 90,
            grammar: 88,
            suggestions: [
              '注意重音的位置',
              '练习连读和弱读'
            ]
          }
        }
      }

      const response = responses[mode] || responses.conversation

      return {
        id: `agent-${Date.now()}`,
        content: response.content,
        sender_type: 'agent',
        created_at: new Date().toISOString(),
        learning_feedback: response.feedback
      }
    }

    const scrollToBottom = () => {
      nextTick(() => {
        if (messagesContainer.value) {
          messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
        }
      })
    }

    // 其他功能
    const copyText = async (text) => {
      try {
        const cleanText = text.replace(/<[^>]*>/g, '').trim()
        await navigator.clipboard.writeText(cleanText)
        message.success('已复制到剪贴板')
      } catch (error) {
        message.error('复制失败')
      }
    }

    const speakText = (text) => {
      if (!('speechSynthesis' in window)) {
        message.error('当前浏览器不支持语音朗读')
        return
      }

      try {
        speechSynthesis.cancel()
        const cleanText = text.replace(/<[^>]*>/g, '').trim()
        const utterance = new SpeechSynthesisUtterance(cleanText)

        // 根据目标语言设置朗读语言
        const langCodes = {
          english: 'en-US',
          chinese: 'zh-CN',
          japanese: 'ja-JP',
          korean: 'ko-KR',
          french: 'fr-FR',
          german: 'de-DE',
          spanish: 'es-ES'
        }

        utterance.lang = langCodes[learningSettings.targetLanguage] || 'en-US'
        utterance.rate = 0.8
        speechSynthesis.speak(utterance)
        message.info('开始朗读...')
      } catch (error) {
        message.error('朗读失败')
      }
    }

    const analyzeGrammar = (text) => {
      message.info('正在分析语法结构...')
      // TODO: 集成语法分析API
    }

    // 文件上传
    const beforeUploadTerminology = (file) => {
      const isValidType = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                          'application/vnd.ms-excel',
                          'text/csv',
                          'text/plain'].includes(file.type)
      if (!isValidType) {
        message.error('只支持 Excel、CSV 和文本文件！')
      }
      return false // 阻止自动上传
    }

    const beforeUploadMaterial = (file) => {
      const isValidType = ['application/pdf',
                          'application/msword',
                          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                          'text/plain',
                          'application/epub+zip'].includes(file.type)
      if (!isValidType) {
        message.error('只支持 PDF、Word、文本和 EPUB 文件！')
      }
      return false // 阻止自动上传
    }

    const uploadTerminology = () => {
      if (terminologyFiles.value.length === 0) {
        message.warning('请选择要上传的术语文件')
        return
      }

      message.success('术语库上传功能开发中...')
      showTerminologyUpload.value = false
      // TODO: 实现术语库上传
    }

    const uploadMaterial = () => {
      if (materialFiles.value.length === 0) {
        message.warning('请选择要上传的学习材料')
        return
      }

      message.success('学习材料上传功能开发中...')
      showMaterialUpload.value = false
      // TODO: 实现学习材料上传
    }

    // 初始化
    onMounted(() => {
      agent.value = {
        name: '外语学习助手',
        type: 'language_learning'
      }

      // 模拟加载术语库和学习材料
      terminologies.value = [
        { id: 1, name: '商务英语术语', term_count: 500 },
        { id: 2, name: '日常对话词汇', term_count: 300 },
        { id: 3, name: '学术英语词汇', term_count: 800 }
      ]

      learningMaterials.value = [
        { id: 1, name: '商务英语教材', type: 'PDF' },
        { id: 2, name: '日常对话练习', type: 'Word' },
        { id: 3, name: '语法指南', type: 'EPUB' }
      ]

      // 模拟学习统计
      learningStats.totalSessions = 15
      learningStats.totalWords = 450
      learningStats.averageScore = 88
      learningStats.studyTime = '12h 30m'

      nextTick(() => {
        inputRef.value?.focus()
      })
    })

    return {
      // 数据
      agent,
      messages,
      inputText,
      isLoading,
      inputFocused,
      isRecording,
      sidebarCollapsed,
      messagesContainer,
      inputRef,

      // 学习相关
      learningSettings,
      learningMode,
      terminologies,
      learningMaterials,
      selectedTerminology,
      selectedMaterial,
      learningStats,

      // 弹窗状态
      showTerminologyUpload,
      showMaterialUpload,
      showStats,
      showSettings,
      terminologyFiles,
      materialFiles,

      // 计算属性和方法
      getLanguageName,
      getLevelName,
      getModeName,
      getModeIcon,
      getModeHint,
      getInputPlaceholder,
      getWordCount,

      // 功能方法
      setLearningMode,
      selectTerminology,
      selectMaterial,
      startConversationPractice,
      startVocabularyLearning,
      startGrammarPractice,
      startPronunciationPractice,
      handleKeydown,
      toggleVoiceInput,
      translateInput,
      checkGrammar,
      sendMessage,
      formatTime,
      formatLearningMessage,
      copyText,
      speakText,
      analyzeGrammar,
      beforeUploadTerminology,
      beforeUploadMaterial,
      uploadTerminology,
      uploadMaterial
    }
  }
}
</script>

<style scoped>
.language-learning-chat {
  height: 100vh;
  display: flex;
  background: #f8fafc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', sans-serif;
}

/* 左侧边栏 */
.sidebar {
  width: 320px;
  background: #ffffff;
  border-right: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
}

.sidebar.collapsed {
  width: 60px;
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #e2e8f0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 16px;
}

.logo-icon {
  width: 24px;
  height: 24px;
}

.collapse-btn {
  border: none;
  box-shadow: none;
  color: white;
}

/* 学习设置 */
.learning-settings {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.settings-section {
  margin-bottom: 24px;
}

.settings-section h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.setting-item {
  margin-bottom: 16px;
}

.setting-item label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

/* 资源管理 */
.resources-section {
  margin-bottom: 24px;
}

.resources-section h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.resource-item {
  margin-bottom: 16px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.resource-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  font-weight: 500;
  color: #374151;
}

.resource-list {
  max-height: 150px;
  overflow-y: auto;
}

.terminology-item,
.material-item {
  padding: 12px;
  border-bottom: 1px solid #f1f5f9;
  cursor: pointer;
  transition: background-color 0.2s;
}

.terminology-item:hover,
.material-item:hover {
  background: #f8fafc;
}

.terminology-item.active,
.material-item.active {
  background: #eff6ff;
  border-left: 3px solid #3b82f6;
}

.terminology-name,
.material-name {
  font-weight: 500;
  color: #374151;
  margin-bottom: 4px;
}

.terminology-count,
.material-type {
  font-size: 12px;
  color: #6b7280;
}

/* 学习模式 */
.learning-modes h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.mode-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mode-buttons .ant-btn {
  height: 40px;
  text-align: left;
  justify-content: flex-start;
}

/* 主聊天区域 */
.main-chat {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #ffffff;
}

.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 1px solid #e2e8f0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.learning-info h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
}

.learning-status {
  display: flex;
  gap: 12px;
  font-size: 14px;
  opacity: 0.9;
}

.learning-status span {
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  font-size: 12px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  transition: background-color 0.2s;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* 消息区域 */
.chat-messages {
  flex: 1;
  overflow-y: auto;
  background: #f8fafc;
}

.messages-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 24px;
}

/* 欢迎界面 */
.welcome-screen {
  text-align: center;
  padding: 60px 20px;
}

.welcome-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 36px;
}

.welcome-screen h2 {
  margin: 0 0 12px 0;
  font-size: 28px;
  font-weight: 600;
  color: #111827;
}

.welcome-screen p {
  margin: 0 0 32px 0;
  font-size: 16px;
  color: #6b7280;
  line-height: 1.6;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.learning-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.feature-card {
  padding: 24px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.feature-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
}

.feature-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.feature-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.feature-desc {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.4;
}
</style>