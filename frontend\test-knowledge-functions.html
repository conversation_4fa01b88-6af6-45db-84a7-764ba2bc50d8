<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识库功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 2px solid #e2e8f0;
        }
        
        .test-section {
            margin-bottom: 24px;
            padding: 16px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: #f8fafc;
        }
        
        .test-button {
            display: inline-block;
            padding: 12px 24px;
            background: #3b82f6;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 8px 8px 8px 0;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        
        .test-button:hover {
            background: #2563eb;
            transform: translateY(-2px);
        }
        
        .test-button.danger {
            background: #ef4444;
        }
        
        .test-button.danger:hover {
            background: #dc2626;
        }
        
        .status {
            padding: 6px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 8px;
        }
        
        .status.success { background: #dcfce7; color: #166534; }
        .status.error { background: #fef2f2; color: #dc2626; }
        .status.pending { background: #fef3c7; color: #d97706; }
        
        .api-result {
            background: #1e293b;
            color: #e2e8f0;
            padding: 16px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            margin: 12px 0;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .url-box {
            background: #f3f4f6;
            padding: 12px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
            margin: 8px 0;
            border-left: 4px solid #3b82f6;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }
        
        .feature-card {
            border: 1px solid #d1d5db;
            border-radius: 8px;
            padding: 16px;
            background: white;
        }
        
        .feature-card h4 {
            margin: 0 0 12px 0;
            color: #374151;
        }
        
        .document-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 8px;
            margin: 12px 0;
        }
        
        .document-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .document-item:last-child {
            border-bottom: none;
        }
        
        .document-info {
            flex: 1;
        }
        
        .document-name {
            font-weight: 500;
            color: #374151;
        }
        
        .document-meta {
            font-size: 12px;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📚 知识库功能测试</h1>
            <p>测试知识库的删除、预览和设置保存功能</p>
        </div>
        
        <div class="test-section">
            <h3>🎯 测试目标</h3>
            <p>验证以下功能是否真实有效：</p>
            <ul>
                <li>✅ 知识库文档删除 - 真实删除数据库记录</li>
                <li>✅ 知识库文档预览 - 显示真实文档内容</li>
                <li>✅ 记忆设置保存 - 持久化到数据库</li>
                <li>✅ 高级设置保存 - 持久化到数据库</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>🔧 API测试</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>知识库文档列表</h4>
                    <button onclick="loadDocuments()" class="test-button">加载文档列表</button>
                    <span id="load-status" class="status pending">未测试</span>
                    <div id="document-list" class="document-list" style="display: none;"></div>
                </div>
                
                <div class="feature-card">
                    <h4>文档预览测试</h4>
                    <input type="text" id="preview-doc-id" placeholder="输入文档ID" style="width: 200px; padding: 8px; margin-right: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    <button onclick="previewDocument()" class="test-button">预览文档</button>
                    <span id="preview-status" class="status pending">未测试</span>
                </div>
                
                <div class="feature-card">
                    <h4>文档删除测试</h4>
                    <input type="text" id="delete-doc-id" placeholder="输入文档ID" style="width: 200px; padding: 8px; margin-right: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    <button onclick="deleteDocument()" class="test-button danger">删除文档</button>
                    <span id="delete-status" class="status pending">未测试</span>
                </div>
                
                <div class="feature-card">
                    <h4>记忆设置测试</h4>
                    <button onclick="testMemorySettings()" class="test-button">测试记忆设置</button>
                    <span id="memory-status" class="status pending">未测试</span>
                </div>
            </div>
            <div id="api-result" class="api-result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>🌐 页面测试</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>智能体编辑器</h4>
                    <a href="http://*************:3000/utilities/daily/ai-agent/editor" 
                       class="test-button" target="_blank">
                        打开智能体编辑器
                    </a>
                    <p style="font-size: 12px; color: #666; margin-top: 8px;">
                        测试知识库的预览和删除功能
                    </p>
                </div>
                
                <div class="feature-card">
                    <h4>智能体详情页</h4>
                    <a href="http://*************:3000/utilities/daily/ai-agent?id=e55f5e84-6d8b-4265-8e55-728bdb0d2455&preview=true" 
                       class="test-button" target="_blank">
                        打开智能体详情
                    </a>
                    <p style="font-size: 12px; color: #666; margin-top: 8px;">
                        测试记忆设置和高级设置保存
                    </p>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📋 测试步骤</h3>
            <ol>
                <li><strong>API测试</strong>：点击上方按钮测试各个API功能</li>
                <li><strong>页面测试</strong>：打开相应页面，手动测试功能</li>
                <li><strong>数据持久化验证</strong>：修改设置后刷新页面，检查是否保存</li>
                <li><strong>真实性验证</strong>：删除文档后检查数据库是否真的删除</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h3>✅ 预期结果</h3>
            <ul>
                <li>知识库文档列表正常加载</li>
                <li>文档预览显示真实内容</li>
                <li>文档删除真实生效</li>
                <li>记忆设置保存后持久化</li>
                <li>高级设置保存后持久化</li>
                <li>所有操作无前端错误</li>
            </ul>
        </div>
    </div>
    
    <script>
        const API_BASE = 'http://*************:8000/api/v1';
        const resultDiv = document.getElementById('api-result');
        
        function showResult(message) {
            resultDiv.style.display = 'block';
            resultDiv.textContent += message + '\n';
            resultDiv.scrollTop = resultDiv.scrollHeight;
        }
        
        function clearResult() {
            resultDiv.textContent = '';
            resultDiv.style.display = 'none';
        }
        
        async function loadDocuments() {
            const statusEl = document.getElementById('load-status');
            const listEl = document.getElementById('document-list');
            
            statusEl.textContent = '加载中...';
            statusEl.className = 'status pending';
            clearResult();
            
            try {
                const response = await fetch(`${API_BASE}/ai-agent/knowledge-documents`);
                const data = await response.json();
                
                if (response.ok && data.documents) {
                    statusEl.textContent = `✅ 成功 (${data.documents.length}个文档)`;
                    statusEl.className = 'status success';
                    
                    listEl.innerHTML = '';
                    data.documents.slice(0, 5).forEach(doc => {
                        const item = document.createElement('div');
                        item.className = 'document-item';
                        item.innerHTML = `
                            <div class="document-info">
                                <div class="document-name">${doc.title || 'N/A'}</div>
                                <div class="document-meta">ID: ${doc.id} | 类型: ${doc.content_type || 'N/A'}</div>
                            </div>
                        `;
                        listEl.appendChild(item);
                    });
                    listEl.style.display = 'block';
                    
                    showResult(`✅ 加载了 ${data.documents.length} 个文档`);
                } else {
                    statusEl.textContent = '❌ 失败';
                    statusEl.className = 'status error';
                    showResult(`❌ 加载失败: ${data.error || '未知错误'}`);
                }
            } catch (error) {
                statusEl.textContent = '❌ 错误';
                statusEl.className = 'status error';
                showResult(`❌ 请求错误: ${error.message}`);
            }
        }
        
        async function previewDocument() {
            const docId = document.getElementById('preview-doc-id').value.trim();
            const statusEl = document.getElementById('preview-status');
            
            if (!docId) {
                alert('请输入文档ID');
                return;
            }
            
            statusEl.textContent = '预览中...';
            statusEl.className = 'status pending';
            clearResult();
            
            try {
                const response = await fetch(`${API_BASE}/ai-agent/knowledge-documents/${docId}/preview`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    statusEl.textContent = '✅ 成功';
                    statusEl.className = 'status success';
                    
                    const doc = data.document;
                    showResult(`✅ 预览成功:`);
                    showResult(`   标题: ${doc.title}`);
                    showResult(`   类型: ${doc.content_type}`);
                    showResult(`   大小: ${doc.file_size} 字节`);
                    showResult(`   内容预览: ${doc.content.substring(0, 200)}...`);
                } else {
                    statusEl.textContent = '❌ 失败';
                    statusEl.className = 'status error';
                    showResult(`❌ 预览失败: ${data.error || '未知错误'}`);
                }
            } catch (error) {
                statusEl.textContent = '❌ 错误';
                statusEl.className = 'status error';
                showResult(`❌ 请求错误: ${error.message}`);
            }
        }
        
        async function deleteDocument() {
            const docId = document.getElementById('delete-doc-id').value.trim();
            const statusEl = document.getElementById('delete-status');
            
            if (!docId) {
                alert('请输入文档ID');
                return;
            }
            
            if (!confirm(`确定要删除文档 ${docId} 吗？此操作不可恢复！`)) {
                return;
            }
            
            statusEl.textContent = '删除中...';
            statusEl.className = 'status pending';
            clearResult();
            
            try {
                const response = await fetch(`${API_BASE}/ai-agent/knowledge-documents/${docId}`, {
                    method: 'DELETE'
                });
                const data = await response.json();
                
                if (response.ok && data.success) {
                    statusEl.textContent = '✅ 成功';
                    statusEl.className = 'status success';
                    showResult(`✅ 文档删除成功: ${docId}`);
                } else {
                    statusEl.textContent = '❌ 失败';
                    statusEl.className = 'status error';
                    showResult(`❌ 删除失败: ${data.error || '未知错误'}`);
                }
            } catch (error) {
                statusEl.textContent = '❌ 错误';
                statusEl.className = 'status error';
                showResult(`❌ 请求错误: ${error.message}`);
            }
        }
        
        async function testMemorySettings() {
            const statusEl = document.getElementById('memory-status');
            const agentId = 'e55f5e84-6d8b-4265-8e55-728bdb0d2455';
            
            statusEl.textContent = '测试中...';
            statusEl.className = 'status pending';
            clearResult();
            
            try {
                // 测试获取记忆设置
                showResult('📖 测试获取记忆设置...');
                const getResponse = await fetch(`${API_BASE}/agents/${agentId}/memory-settings`);
                const getData = await getResponse.json();
                
                if (getResponse.ok && getData.success) {
                    showResult('✅ 获取记忆设置成功');
                    
                    // 测试更新记忆设置
                    showResult('💾 测试更新记忆设置...');
                    const testSettings = {
                        short_term: { enabled: true, max_messages: 25 },
                        long_term: { enabled: true, max_entries: 1500 }
                    };
                    
                    const updateResponse = await fetch(`${API_BASE}/agents/${agentId}/memory-settings`, {
                        method: 'PUT',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(testSettings)
                    });
                    const updateData = await updateResponse.json();
                    
                    if (updateResponse.ok && updateData.success) {
                        statusEl.textContent = '✅ 成功';
                        statusEl.className = 'status success';
                        showResult('✅ 记忆设置更新成功');
                    } else {
                        throw new Error(updateData.error || '更新失败');
                    }
                } else {
                    throw new Error(getData.error || '获取失败');
                }
            } catch (error) {
                statusEl.textContent = '❌ 失败';
                statusEl.className = 'status error';
                showResult(`❌ 记忆设置测试失败: ${error.message}`);
            }
        }
        
        // 页面加载完成后的提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📚 知识库功能测试页面已加载');
            showResult('🚀 测试页面已准备就绪，请开始测试各项功能');
        });
    </script>
</body>
</html>
