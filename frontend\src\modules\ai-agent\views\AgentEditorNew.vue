<template>
  <div class="agent-editor-new">
    <!-- 头部导航 -->
    <div class="editor-header">
      <div class="header-left">
        <button @click="goBack" class="back-btn">
          <el-icon><arrow-left /></el-icon>
          返回
        </button>
        <div class="header-info">
          <h1>{{ isEditMode ? '编辑智能体' : '创建智能体' }}</h1>
          <p>{{ agentConfig.name || '未命名智能体' }}</p>
        </div>
      </div>
      
      <div class="header-actions">
        <el-button @click="previewAgent" type="info">
          <el-icon><view /></el-icon>
          预览
        </el-button>
        <el-button @click="testAgent" type="warning">
          <el-icon><chat-line-round /></el-icon>
          测试
        </el-button>
        <el-button @click="saveAgent" type="primary" :loading="saving">
          <el-icon><check /></el-icon>
          {{ isEditMode ? '保存更改' : '创建智能体' }}
        </el-button>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="editor-main">
      <!-- 侧边导航 -->
      <div class="editor-sidebar">
        <div class="sidebar-nav">
          <div 
            v-for="tab in editorTabs" 
            :key="tab.id"
            :class="['nav-item', { active: activeTab === tab.id }]"
            @click="activeTab = tab.id"
          >
            <div class="nav-icon">{{ tab.icon }}</div>
            <div class="nav-label">{{ tab.label }}</div>
            <div v-if="tab.badge" class="nav-badge">{{ tab.badge }}</div>
          </div>
        </div>
        
        <!-- 快速操作 -->
        <div class="quick-actions">
          <div class="action-item" @click="importConfig">
            <el-icon><upload /></el-icon>
            导入配置
          </div>
          <div class="action-item" @click="exportConfig">
            <el-icon><download /></el-icon>
            导出配置
          </div>
          <div class="action-item" @click="resetConfig">
            <el-icon><refresh /></el-icon>
            重置配置
          </div>
          <div class="action-item" @click="packageStandalone">
            <el-icon><upload-filled /></el-icon>
            打包单机版
          </div>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="editor-content">
        <!-- 基本信息 -->
        <div v-if="activeTab === 'basic'" class="tab-content">
          <div class="content-header">
            <h2>🤖 基本信息</h2>
            <p>设置智能体的基本属性和个性特征</p>
          </div>
          
          <div class="form-sections">
            <div class="form-section">
              <h3>基础设置</h3>
              <el-form :model="agentConfig" label-width="120px">
                <el-form-item label="智能体名称" required>
                  <el-input 
                    v-model="agentConfig.name" 
                    placeholder="给您的智能体起个名字"
                    maxlength="50"
                    show-word-limit
                  />
                </el-form-item>
                
                <el-form-item label="功能描述">
                  <el-input 
                    v-model="agentConfig.description" 
                    type="textarea"
                    :rows="4"
                    placeholder="描述这个智能体的主要功能和用途"
                    maxlength="500"
                    show-word-limit
                  />
                </el-form-item>
                
                <el-form-item label="智能体头像">
                  <div class="avatar-selector">
                    <div 
                      v-for="avatar in avatarOptions" 
                      :key="avatar"
                      :class="['avatar-option', { selected: agentConfig.avatar === avatar }]"
                      @click="agentConfig.avatar = avatar"
                    >
                      {{ avatar }}
                    </div>
                  </div>
                </el-form-item>
              </el-form>
            </div>

            <div class="form-section">
              <h3>个性设置</h3>
              <el-form :model="agentConfig" label-width="120px">
                <el-form-item label="对话风格">
                  <el-select v-model="agentConfig.personality" placeholder="选择对话风格">
                    <el-option
                      v-for="personality in personalityOptions"
                      :key="personality.id"
                      :label="personality.name"
                      :value="personality.id"
                    >
                      <span style="float: left">{{ personality.icon }} {{ personality.name }}</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">{{ personality.description }}</span>
                    </el-option>
                  </el-select>
                </el-form-item>
                
                <el-form-item label="专业领域">
                  <el-select v-model="agentConfig.domain" placeholder="选择专业领域" clearable>
                    <el-option
                      v-for="domain in domainOptions"
                      :key="domain.id"
                      :label="domain.name"
                      :value="domain.id"
                    />
                  </el-select>
                </el-form-item>
                
                <el-form-item label="语言设置">
                  <el-select v-model="agentConfig.language" placeholder="选择主要语言">
                    <el-option label="中文" value="zh-CN" />
                    <el-option label="英文" value="en-US" />
                    <el-option label="中英双语" value="zh-en" />
                  </el-select>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </div>

        <!-- 能力配置 -->
        <div v-if="activeTab === 'capabilities'" class="tab-content">
          <div class="content-header">
            <h2>🛠️ 能力配置</h2>
            <p>选择智能体可以使用的工具和技能</p>
          </div>
          
          <div class="capabilities-grid">
            <div 
              v-for="category in capabilityCategories" 
              :key="category.id"
              class="capability-category"
            >
              <div class="category-header">
                <div class="category-icon">{{ category.icon }}</div>
                <div class="category-info">
                  <h3>{{ category.name }}</h3>
                  <p>{{ category.description }}</p>
                </div>
                <div class="category-toggle">
                  <el-switch 
                    v-model="category.enabled"
                    @change="toggleCategory(category)"
                  />
                </div>
              </div>
              
              <div v-if="category.enabled" class="capability-items">
                <div 
                  v-for="capability in category.capabilities" 
                  :key="capability.id"
                  :class="['capability-item', { 
                    selected: isCapabilitySelected(capability.id),
                    recommended: capability.recommended 
                  }]"
                  @click="toggleCapability(capability.id)"
                >
                  <div class="capability-header">
                    <div class="capability-icon">{{ capability.icon }}</div>
                    <div class="capability-name">{{ capability.name }}</div>
                    <div v-if="capability.recommended" class="recommended-badge">推荐</div>
                  </div>
                  <div class="capability-desc">{{ capability.description }}</div>
                  <div class="capability-example">
                    <strong>示例：</strong>{{ capability.example }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 知识库管理 -->
        <div v-if="activeTab === 'knowledge'" class="tab-content">
          <div class="content-header">
            <h2>📚 知识库管理</h2>
            <p>上传和管理智能体的专业知识文档</p>
          </div>
          
          <div class="knowledge-sections">
            <!-- 知识库说明 -->
            <div class="knowledge-explanation">
              <KnowledgeBaseExplanation />
            </div>
            
            <!-- 文档上传 -->
            <div class="upload-section">
              <h3>上传文档</h3>
              <el-upload
                class="upload-dragger"
                drag
                :action="uploadUrl"
                :headers="uploadHeaders"
                :on-success="handleUploadSuccess"
                :on-error="handleUploadError"
                :before-upload="beforeUpload"
                multiple
                accept=".pdf,.doc,.docx,.txt,.md,.xlsx,.xls,.csv"
              >
                <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                <div class="el-upload__text">
                  将文件拖到此处，或<em>点击上传</em>
                </div>
                <template #tip>
                  <div class="el-upload__tip">
                    支持 PDF、Word、TXT、Markdown、Excel、CSV 格式，单个文件不超过 10MB
                  </div>
                </template>
              </el-upload>
            </div>
            
            <!-- 知识库文档列表 -->
            <div class="documents-section">
              <div class="section-header">
                <h3>已上传文档</h3>
                <el-button @click="refreshDocuments" size="small">
                  <el-icon><refresh /></el-icon>
                  刷新
                </el-button>
              </div>
              
              <div class="documents-list">
                <div 
                  v-for="doc in knowledgeDocuments" 
                  :key="doc.id"
                  class="document-item"
                >
                  <div class="doc-icon">{{ getDocIcon(doc.type) }}</div>
                  <div class="doc-info">
                    <div class="doc-name">{{ doc.name }}</div>
                    <div class="doc-meta">
                      {{ formatFileSize(doc.size) }} • {{ formatDate(doc.uploadTime) }}
                    </div>
                  </div>
                  <div class="doc-status">
                    <el-tag :type="getStatusType(doc.status)">
                      {{ getStatusText(doc.status) }}
                    </el-tag>
                  </div>
                  <div class="doc-actions">
                    <el-button @click="previewDocument(doc)" size="small" type="info">
                      预览
                    </el-button>
                    <el-button @click="deleteDocument(doc)" size="small" type="danger">
                      删除
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 记忆设置 -->
        <div v-if="activeTab === 'memory'" class="tab-content">
          <div class="content-header">
            <h2>🧠 记忆设置</h2>
            <p>配置智能体的记忆能力和存储策略</p>
          </div>
          
          <div class="memory-sections">
            <div 
              v-for="memoryType in memoryTypes" 
              :key="memoryType.id"
              class="memory-section"
            >
              <div class="memory-header">
                <div class="memory-icon">{{ memoryType.icon }}</div>
                <div class="memory-info">
                  <h3>{{ memoryType.name }}</h3>
                  <p>{{ memoryType.description }}</p>
                </div>
                <div class="memory-toggle">
                  <el-switch 
                    v-model="memoryType.enabled"
                    @change="updateMemoryConfig"
                  />
                </div>
              </div>
              
              <div v-if="memoryType.enabled" class="memory-config">
                <el-form :model="memoryType.config" label-width="120px">
                  <el-form-item v-if="memoryType.id === 'short_term'" label="保持时长">
                    <el-select v-model="memoryType.config.duration">
                      <el-option label="当前对话" value="session" />
                      <el-option label="1小时" value="1h" />
                      <el-option label="24小时" value="24h" />
                    </el-select>
                  </el-form-item>
                  
                  <el-form-item v-if="memoryType.id === 'long_term'" label="存储策略">
                    <el-select v-model="memoryType.config.strategy">
                      <el-option label="重要信息" value="important" />
                      <el-option label="用户偏好" value="preference" />
                      <el-option label="全部记录" value="all" />
                    </el-select>
                  </el-form-item>
                  
                  <el-form-item v-if="memoryType.id === 'semantic'" label="学习模式">
                    <el-select v-model="memoryType.config.learning">
                      <el-option label="被动学习" value="passive" />
                      <el-option label="主动学习" value="active" />
                      <el-option label="交互学习" value="interactive" />
                    </el-select>
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </div>
        </div>

        <!-- 高级设置 -->
        <div v-if="activeTab === 'advanced'" class="tab-content">
          <div class="content-header">
            <h2>⚙️ 高级设置</h2>
            <p>配置智能体的高级功能和行为参数</p>
          </div>
          
          <div class="advanced-sections">
            <div class="advanced-section">
              <h3>模型参数</h3>
              <el-form :model="agentConfig.advanced" label-width="120px">
                <el-form-item label="创造性">
                  <el-slider 
                    v-model="agentConfig.advanced.creativity"
                    :min="0"
                    :max="100"
                    show-tooltip
                  />
                  <div class="param-hint">控制回答的创造性和随机性</div>
                </el-form-item>
                
                <el-form-item label="专业性">
                  <el-slider 
                    v-model="agentConfig.advanced.professionalism"
                    :min="0"
                    :max="100"
                    show-tooltip
                  />
                  <div class="param-hint">控制回答的专业程度和严谨性</div>
                </el-form-item>
                
                <el-form-item label="响应长度">
                  <el-select v-model="agentConfig.advanced.responseLength">
                    <el-option label="简洁" value="short" />
                    <el-option label="适中" value="medium" />
                    <el-option label="详细" value="long" />
                  </el-select>
                </el-form-item>
              </el-form>
            </div>
            
            <div class="advanced-section">
              <h3>安全设置</h3>
              <el-form :model="agentConfig.safety" label-width="120px">
                <el-form-item label="内容过滤">
                  <el-switch v-model="agentConfig.safety.contentFilter" />
                  <div class="param-hint">启用内容安全过滤</div>
                </el-form-item>
                
                <el-form-item label="隐私保护">
                  <el-switch v-model="agentConfig.safety.privacyProtection" />
                  <div class="param-hint">保护用户隐私信息</div>
                </el-form-item>
                
                <el-form-item label="访问控制">
                  <el-select v-model="agentConfig.safety.accessControl">
                    <el-option label="公开" value="public" />
                    <el-option label="私有" value="private" />
                    <el-option label="团队" value="team" />
                  </el-select>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft, View, ChatLineRound, Check, Upload, Download, Refresh,
  UploadFilled
} from '@element-plus/icons-vue'
import KnowledgeBaseExplanation from '../components/KnowledgeBaseExplanation.vue'
import agentService from '../services/agentService.js'

export default {
  name: 'AgentEditorNew',
  components: {
    ArrowLeft,
    View,
    ChatLineRound,
    Check,
    Upload,
    Download,
    Refresh,
    UploadFilled,
    KnowledgeBaseExplanation
  },
  
  setup() {
    const router = useRouter()
    const route = useRoute()
    const saving = ref(false)
    const activeTab = ref('basic')

    // 判断是否为编辑模式
    const isEditMode = computed(() => {
      return route.query.mode === 'edit' && route.query.agent_id
    })

    // 智能体配置
    const agentConfig = reactive({
      name: '',
      description: '',
      avatar: '🤖',
      personality: 'friendly',
      domain: '',
      language: 'zh-CN',
      capabilities: [],
      knowledge: [],
      systemPrompt: '',
      maxTokens: 4000,
      temperature: 0.7,
      memory: {
        short_term: { enabled: true, duration: 'session' },
        long_term: { enabled: false, strategy: 'important' },
        semantic: { enabled: false, learning: 'passive' }
      },
      advanced: {
        creativity: 50,
        professionalism: 70,
        responseLength: 'medium'
      },
      safety: {
        contentFilter: true,
        privacyProtection: true,
        accessControl: 'private'
      }
    })

    // 编辑器标签页
    const editorTabs = ref([
      { id: 'basic', label: '基本信息', icon: '🤖' },
      { id: 'capabilities', label: '能力配置', icon: '🛠️', badge: agentConfig.capabilities.length || null },
      { id: 'knowledge', label: '知识库', icon: '📚', badge: agentConfig.knowledge.length || null },
      { id: 'memory', label: '记忆设置', icon: '🧠' },
      { id: 'advanced', label: '高级设置', icon: '⚙️' }
    ])

    // 头像选项
    const avatarOptions = ref([
      '🤖', '👨‍🏫', '👩‍🏫', '👨‍💼', '👩‍💼', '👨‍⚕️', '👩‍⚕️', 
      '👨‍💻', '👩‍💻', '👨‍🔬', '👩‍🔬', '🎭', '🎨', '📊'
    ])

    // 个性选项
    const personalityOptions = ref([
      { id: 'friendly', name: '友好亲切', icon: '😊', description: '温和友善，平易近人' },
      { id: 'professional', name: '专业严谨', icon: '👔', description: '正式专业，逻辑清晰' },
      { id: 'humorous', name: '幽默风趣', icon: '😄', description: '轻松幽默，活泼有趣' },
      { id: 'patient', name: '耐心细致', icon: '🤗', description: '耐心教导，细致入微' }
    ])

    // 领域选项
    const domainOptions = ref([
      { id: 'education', name: '教育培训' },
      { id: 'business', name: '商务办公' },
      { id: 'healthcare', name: '医疗健康' },
      { id: 'technology', name: '科技研发' },
      { id: 'creative', name: '创意设计' },
      { id: 'finance', name: '金融投资' }
    ])

    // 能力分类
    const capabilityCategories = ref([
      {
        id: 'basic',
        name: '基础能力',
        icon: '🔍',
        description: '智能体的基本技能',
        enabled: true,
        capabilities: [
          {
            id: 'web_search',
            name: '网络搜索',
            icon: '🔍',
            description: '搜索互联网信息',
            example: '查找最新新闻、资料',
            recommended: true
          },
          {
            id: 'text_analysis',
            name: '文本分析',
            icon: '📝',
            description: '分析和理解文本内容',
            example: '情感分析、关键词提取',
            recommended: true
          }
        ]
      },
      {
        id: 'advanced',
        name: '高级能力',
        icon: '⚡',
        description: '更强大的专业技能',
        enabled: false,
        capabilities: [
          {
            id: 'code_execution',
            name: '代码执行',
            icon: '💻',
            description: '执行编程代码',
            example: '数据处理、自动化脚本',
            recommended: false
          },
          {
            id: 'data_analysis',
            name: '数据分析',
            icon: '📊',
            description: '分析和处理数据',
            example: '生成图表、统计分析',
            recommended: false
          }
        ]
      }
    ])

    // 记忆类型
    const memoryTypes = ref([
      {
        id: 'short_term',
        name: '短期记忆',
        icon: '🧠',
        description: '记住当前对话的内容',
        enabled: true,
        config: { duration: 'session' }
      },
      {
        id: 'long_term',
        name: '长期记忆',
        icon: '📚',
        description: '跨对话记住重要信息',
        enabled: false,
        config: { strategy: 'important' }
      },
      {
        id: 'semantic',
        name: '知识记忆',
        icon: '🎓',
        description: '记住学到的知识和概念',
        enabled: false,
        config: { learning: 'passive' }
      }
    ])

    // 知识库文档（从后端加载真实数据）
    const knowledgeDocuments = ref([])

    // 上传配置 - 动态设置为当前智能体的上传URL
    const uploadUrl = computed(() => {
      const currentAgentId = route.query.id ||
                            route.params.id ||
                            agentConfig.value?.id ||
                            agentConfig.value?.agent_id ||
                            localStorage.getItem('current_agent_id')

      console.log('计算上传URL，智能体ID:', currentAgentId)

      if (currentAgentId) {
        const url = `/api/v1/agents/${currentAgentId}/upload-file`
        console.log('使用智能体专属上传URL:', url)
        return url
      }

      console.log('使用全局上传URL')
      return '/api/v1/ai-agent/upload-file' // 兜底使用全局上传
    })

    const uploadHeaders = ref({
      'Authorization': `Bearer ${localStorage.getItem('token')}`
    })

    // 方法
    const goBack = () => {
      router.back()
    }

    const previewAgent = () => {
      if (!agentConfig.name.trim()) {
        ElMessage.warning('请先设置智能体名称')
        return
      }

      // 创建预览数据
      const previewData = {
        id: route.query.agent_id || 'preview-' + Date.now(),
        name: agentConfig.name,
        description: agentConfig.description,
        avatar: agentConfig.avatar,
        agent_type: agentConfig.personality,
        capabilities: agentConfig.capabilities,
        knowledge: agentConfig.knowledge,
        system_prompt: agentConfig.systemPrompt,
        max_tokens: agentConfig.maxTokens,
        temperature: agentConfig.temperature
      }

      // 跳转到详情页面进行预览
      const previewUrl = `/utilities/daily/ai-agent?id=${previewData.id}&preview=true`

      // 将预览数据存储到sessionStorage
      sessionStorage.setItem('preview_agent_data', JSON.stringify(previewData))

      // 在新窗口中打开预览
      window.open(previewUrl, '_blank')

      ElMessage.success('已在新窗口中打开预览')
    }

    const testAgent = () => {
      if (!agentConfig.name.trim()) {
        ElMessage.warning('请先设置智能体名称')
        return
      }

      // 创建测试数据
      const testData = {
        id: route.query.agent_id || 'test-' + Date.now(),
        name: agentConfig.name + ' (测试)',
        description: agentConfig.description,
        avatar: agentConfig.avatar,
        agent_type: agentConfig.personality,
        system_prompt: agentConfig.systemPrompt,
        max_tokens: agentConfig.maxTokens,
        temperature: agentConfig.temperature,
        test_mode: true
      }

      // 跳转到对话页面进行测试
      const testUrl = `/utilities/daily/unified-chat?agent_id=${testData.id}&test=true`

      // 将测试数据存储到sessionStorage
      sessionStorage.setItem('test_agent_data', JSON.stringify(testData))

      // 在新窗口中打开测试对话
      window.open(testUrl, '_blank')

      ElMessage.success('已在新窗口中打开测试对话')
    }

    const saveAgent = async () => {
      if (!agentConfig.name.trim()) {
        ElMessage.warning('请输入智能体名称')
        return
      }

      saving.value = true
      try {
        const agentData = {
          name: agentConfig.name,
          description: agentConfig.description,
          avatar: agentConfig.avatar,
          personality: agentConfig.personality,
          domain: agentConfig.domain,
          language: agentConfig.language,
          capabilities: agentConfig.capabilities,
          knowledge: agentConfig.knowledge,
          memory: agentConfig.memory,
          advanced: agentConfig.advanced,
          safety: agentConfig.safety
        }

        if (isEditMode.value) {
          // 更新智能体
          const agentId = route.query.agent_id

          if (agentId.startsWith('local-')) {
            // 更新本地智能体
            const localAgents = JSON.parse(localStorage.getItem('local_agents') || '[]')
            const index = localAgents.findIndex(a => a.id === agentId)
            if (index !== -1) {
              localAgents[index] = { ...localAgents[index], ...agentData, updated_at: new Date().toISOString() }
              localStorage.setItem('local_agents', JSON.stringify(localAgents))
              ElMessage.success('智能体更新成功！')
            } else {
              ElMessage.error('未找到要更新的智能体')
              return
            }
          } else {
            // 通过API更新
            try {
              const updateResult = await agentService.updateAgent(agentId, agentData)
              console.log('智能体更新结果:', updateResult)

              // 保存智能体ID到localStorage，供知识库使用
              if (updateResult.agent_id) {
                localStorage.setItem('current_agent_id', updateResult.agent_id)
              }

              ElMessage.success('智能体更新成功！')

              // 重新加载智能体配置和知识库文档
              await loadAgentConfig()
              await loadKnowledgeDocuments()

            } catch (error) {
              console.error('API更新失败:', error)
              ElMessage.error('更新失败，请重试')
              return
            }
          }
        } else {
          // 创建新智能体
          const newAgent = {
            id: 'local-' + Date.now(),
            ...agentData,
            created_at: new Date().toISOString(),
            author: '本地用户',
            status: 'active'
          }

          const localAgents = JSON.parse(localStorage.getItem('local_agents') || '[]')
          localAgents.push(newAgent)
          localStorage.setItem('local_agents', JSON.stringify(localAgents))
          ElMessage.success('智能体创建成功！')
        }

        // 对于新创建的智能体，询问用户是否要继续编辑
        if (!isEditMode.value) {
          try {
            await ElMessageBox.confirm('智能体创建成功！是否继续编辑配置？', '创建成功', {
              confirmButtonText: '继续编辑',
              cancelButtonText: '返回列表',
              type: 'success'
            })
            // 用户选择继续编辑，不做任何操作
          } catch {
            // 用户选择返回列表
            router.push('/agents?refresh=true')
          }
        }
      } catch (error) {
        console.error('保存智能体失败:', error)
        ElMessage.error('保存失败，请重试')
      } finally {
        saving.value = false
      }
    }

    const importConfig = () => {
      // 创建文件输入元素
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = '.json'
      input.onchange = async (event) => {
        const file = event.target.files[0]
        if (!file) return

        try {
          const text = await file.text()
          const config = JSON.parse(text)

          // 验证配置格式
          if (!config.name) {
            ElMessage.error('配置文件格式错误：缺少名称字段')
            return
          }

          // 确认导入
          await ElMessageBox.confirm(
            `确定要导入配置 "${config.name}" 吗？这将覆盖当前配置。`,
            '确认导入',
            {
              confirmButtonText: '导入',
              cancelButtonText: '取消',
              type: 'warning'
            }
          )

          // 导入配置
          Object.assign(agentConfig, {
            name: config.name || '',
            description: config.description || '',
            avatar: config.avatar || '🤖',
            personality: config.personality || 'friendly',
            domain: config.domain || '',
            language: config.language || 'zh-CN',
            capabilities: config.capabilities || [],
            knowledge: config.knowledge || [],
            systemPrompt: config.systemPrompt || config.system_prompt || '',
            maxTokens: config.maxTokens || config.max_tokens || 4000,
            temperature: config.temperature || 0.7,
            memory: config.memory || agentConfig.memory,
            advanced: config.advanced || agentConfig.advanced,
            safety: config.safety || agentConfig.safety
          })

          ElMessage.success('配置导入成功！')
        } catch (error) {
          console.error('导入配置失败:', error)
          ElMessage.error('导入配置失败：' + error.message)
        }
      }
      input.click()
    }

    const exportConfig = () => {
      try {
        if (!agentConfig.name.trim()) {
          ElMessage.warning('请先设置智能体名称')
          return
        }

        // 准备导出数据
        const exportData = {
          name: agentConfig.name,
          description: agentConfig.description,
          avatar: agentConfig.avatar,
          personality: agentConfig.personality,
          domain: agentConfig.domain,
          language: agentConfig.language,
          capabilities: agentConfig.capabilities,
          knowledge: agentConfig.knowledge,
          systemPrompt: agentConfig.systemPrompt,
          maxTokens: agentConfig.maxTokens,
          temperature: agentConfig.temperature,
          memory: agentConfig.memory,
          advanced: agentConfig.advanced,
          safety: agentConfig.safety,
          exportTime: new Date().toISOString(),
          version: '1.0'
        }

        // 创建下载链接
        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
          type: 'application/json'
        })
        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `${agentConfig.name}_配置_${new Date().toISOString().split('T')[0]}.json`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url)

        ElMessage.success('配置导出成功！')
      } catch (error) {
        console.error('导出配置失败:', error)
        ElMessage.error('导出配置失败：' + error.message)
      }
    }

    const resetConfig = async () => {
      try {
        await ElMessageBox.confirm('确定要重置所有配置吗？', '确认重置', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        // 重置配置
        Object.assign(agentConfig, {
          name: '',
          description: '',
          avatar: '🤖',
          personality: 'friendly',
          domain: '',
          language: 'zh-CN',
          capabilities: []
        })
        
        ElMessage.success('配置已重置')
      } catch {
        // 用户取消
      }
    }

    const toggleCategory = (category) => {
      if (!category.enabled) {
        // 禁用分类时，移除该分类下的所有能力
        category.capabilities.forEach(cap => {
          const index = agentConfig.capabilities.indexOf(cap.id)
          if (index > -1) {
            agentConfig.capabilities.splice(index, 1)
          }
        })
      }
    }

    const isCapabilitySelected = (capabilityId) => {
      return agentConfig.capabilities.includes(capabilityId)
    }

    const toggleCapability = (capabilityId) => {
      const index = agentConfig.capabilities.indexOf(capabilityId)
      if (index > -1) {
        agentConfig.capabilities.splice(index, 1)
      } else {
        agentConfig.capabilities.push(capabilityId)
      }
    }

    const updateMemoryConfig = () => {
      // 更新记忆配置
      memoryTypes.value.forEach(type => {
        agentConfig.memory[type.id] = {
          enabled: type.enabled,
          ...type.config
        }
      })
    }

    // 知识库相关方法
    const loadKnowledgeDocuments = async () => {
      try {
        console.log('加载智能体知识库文档...')

        // 获取当前智能体ID - 多种方式尝试
        const currentAgentId = route.query.id ||
                              route.params.id ||
                              agentConfig.value?.id ||
                              agentConfig.value?.agent_id ||
                              localStorage.getItem('current_agent_id')

        console.log('尝试获取智能体ID:', {
          'route.query.id': route.query.id,
          'route.params.id': route.params.id,
          'agentConfig.value?.id': agentConfig.value?.id,
          'agentConfig.value?.agent_id': agentConfig.value?.agent_id,
          'localStorage': localStorage.getItem('current_agent_id'),
          'currentAgentId': currentAgentId
        })

        if (!currentAgentId) {
          console.log('没有智能体ID，跳过加载知识库文档')
          return
        }

        // 从后端API加载该智能体的知识库文档
        const response = await fetch(`/api/v1/agents/${currentAgentId}/knowledge-documents`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        })

        if (response.ok) {
          const result = await response.json()
          if (result.success && result.documents) {
            knowledgeDocuments.value = result.documents.map(doc => ({
              id: doc.id,
              name: doc.filename || doc.title,
              type: doc.file_type || doc.content_type,
              size: doc.size || 0,
              status: 'processed',
              uploadTime: new Date(doc.created_at),
              summary: doc.metadata?.summary || '',
              agentId: doc.agent_id || currentAgentId
            }))
            console.log(`加载了智能体 ${currentAgentId} 的 ${knowledgeDocuments.value.length} 个知识库文档`)
          }
        } else {
          console.warn('加载智能体知识库文档失败:', response.status)
        }
      } catch (error) {
        console.error('加载智能体知识库文档出错:', error)
      }
    }

    const beforeUpload = (file) => {
      const validTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain',
        'text/markdown',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'text/csv'
      ]

      const isValidType = validTypes.includes(file.type)
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isValidType) {
        ElMessage.error('只支持 PDF、Word、TXT、Markdown、Excel、CSV 格式的文件！')
        return false
      }
      if (!isLt10M) {
        ElMessage.error('文件大小不能超过 10MB！')
        return false
      }

      // 对于Excel文件，给出特殊提示
      if (file.type.includes('excel') || file.type.includes('spreadsheet') || file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
        ElMessage.info('Excel文件将被解析为表格数据，请确保数据格式正确')
      }

      return true
    }

    const handleUploadSuccess = (response, file) => {
      console.log('文件上传成功:', response)

      if (response.success) {
        // 检查响应结构，适配不同的返回格式
        const docData = response.document || response.data || {}

        // 添加新上传的文档到列表
        const newDoc = {
          id: docData.id || response.data?.knowledge_id || Date.now().toString(),
          name: response.filename || file.name,
          type: response.type || file.type,
          size: response.size || file.size,
          status: 'processed',
          uploadTime: new Date(),
          summary: docData.summary || '',
          agentId: docData.agent_id
        }

        console.log('添加新文档到列表:', newDoc)
        knowledgeDocuments.value.unshift(newDoc)
        ElMessage.success(`${file.name} 上传并处理成功！`)
      } else {
        console.warn('上传响应格式异常:', response)
        ElMessage.success(`${file.name} 上传成功！`)
      }

      // 重新加载文档列表以确保数据同步
      setTimeout(() => {
        refreshDocuments()
      }, 1000) // 延迟1秒确保数据库操作完成
    }

    const handleUploadError = (error, file) => {
      console.error('文件上传失败:', error)
      ElMessage.error(`${file.name} 上传失败：${error.message || '未知错误'}`)
    }

    const refreshDocuments = async () => {
      // 重新加载知识库文档列表
      await loadKnowledgeDocuments()
      ElMessage.success('文档列表已刷新')
    }

    const packageStandalone = async () => {
      try {
        if (!agentConfig.name.trim()) {
          ElMessage.warning('请先设置智能体名称')
          return
        }

        // 确认打包类型
        const { value: deploymentType } = await ElMessageBox.prompt(
          '请选择部署类型：\n1. 标准服务器部署\n2. 机器狗嵌入式部署\n3. 边缘计算设备部署',
          '选择部署类型',
          {
            confirmButtonText: '开始打包',
            cancelButtonText: '取消',
            inputValue: '2',
            inputValidator: (value) => {
              if (!['1', '2', '3'].includes(value)) {
                return '请输入 1、2 或 3'
              }
              return true
            }
          }
        )

        ElMessage.info('正在准备打包...')

        // 根据部署类型创建配置
        const deploymentConfigs = {
          '1': { // 标准服务器
            mode: 'server',
            port: 3000,
            resources: { cpu: '2 cores', memory: '4GB', storage: '10GB' },
            platform: 'linux/windows/macos'
          },
          '2': { // 机器狗
            mode: 'robot_dog',
            port: 8080,
            resources: { cpu: '1 core', memory: '2GB', storage: '4GB' },
            platform: 'embedded_linux',
            hardware: {
              sensors: ['camera', 'microphone', 'speaker', 'imu'],
              actuators: ['motors', 'servos'],
              communication: ['wifi', 'bluetooth', 'uart']
            }
          },
          '3': { // 边缘计算
            mode: 'edge',
            port: 5000,
            resources: { cpu: '1 core', memory: '1GB', storage: '2GB' },
            platform: 'arm_linux'
          }
        }

        const deployConfig = deploymentConfigs[deploymentType]

        // 创建单机版配置
        const standaloneConfig = {
          // 基本信息
          name: agentConfig.name,
          description: agentConfig.description,
          avatar: agentConfig.avatar,
          version: '1.0.0',
          deploymentType: deploymentType,

          // 智能体配置
          agent: {
            personality: agentConfig.personality,
            domain: agentConfig.domain,
            language: agentConfig.language,
            capabilities: agentConfig.capabilities,
            knowledge: agentConfig.knowledge,
            systemPrompt: agentConfig.systemPrompt,
            maxTokens: agentConfig.maxTokens,
            temperature: agentConfig.temperature,
            memory: agentConfig.memory,
            advanced: agentConfig.advanced,
            safety: agentConfig.safety
          },

          // 运行时配置
          runtime: {
            mode: deployConfig.mode,
            autoStart: true,
            port: deployConfig.port,
            apiKey: 'standalone-' + Date.now(),
            resources: deployConfig.resources,
            platform: deployConfig.platform,
            hardware: deployConfig.hardware || null
          },

          // 打包信息
          package: {
            createdAt: new Date().toISOString(),
            createdBy: '智能体编辑器',
            targetPlatform: deployConfig.platform,
            deploymentMode: deployConfig.mode
          }
        }

        // 根据部署类型创建启动脚本
        let startScript, startBat, additionalFiles = []

        if (deploymentType === '2') { // 机器狗部署
          startScript = `#!/bin/bash
echo "启动 ${agentConfig.name} 机器狗智能体..."
echo "配置文件: config.json"
echo "机器狗控制端口: ${standaloneConfig.runtime.port}"
echo "API密钥: ${standaloneConfig.runtime.apiKey}"
echo ""
echo "初始化硬件接口..."
# 初始化传感器
echo "- 初始化摄像头..."
echo "- 初始化麦克风..."
echo "- 初始化扬声器..."
echo "- 初始化IMU传感器..."
echo ""
echo "启动智能体服务..."
echo "按 Ctrl+C 停止服务"
# 启动机器狗智能体服务
python3 robot_dog_agent.py
`

          // 机器狗专用的Python启动脚本
          const robotDogScript = `#!/usr/bin/env python3
"""
机器狗智能体启动脚本
支持硬件接口和智能体交互
"""
import json
import asyncio
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RobotDogAgent:
    def __init__(self, config_path='config.json'):
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = json.load(f)

        self.agent_name = self.config['name']
        self.port = self.config['runtime']['port']

    async def initialize_hardware(self):
        """初始化硬件接口"""
        logger.info("初始化机器狗硬件接口...")
        # TODO: 实际的硬件初始化代码

    async def start_agent_service(self):
        """启动智能体服务"""
        logger.info(f"启动智能体服务: {self.agent_name}")
        logger.info(f"监听端口: {self.port}")
        # TODO: 实际的服务启动代码

    async def run(self):
        """主运行循环"""
        await self.initialize_hardware()
        await self.start_agent_service()

        # 保持运行
        while True:
            await asyncio.sleep(1)

if __name__ == "__main__":
    agent = RobotDogAgent()
    asyncio.run(agent.run())
`

          additionalFiles.push({
            name: 'robot_dog_agent.py',
            content: robotDogScript
          })

        } else { // 标准部署
          startScript = `#!/bin/bash
echo "启动 ${agentConfig.name} 智能体服务..."
echo "配置文件: config.json"
echo "访问地址: http://localhost:${standaloneConfig.runtime.port}"
echo "API密钥: ${standaloneConfig.runtime.apiKey}"
echo ""
echo "按 Ctrl+C 停止服务"
# 启动智能体服务
node server.js
`
        }

        startBat = `@echo off
echo 启动 ${agentConfig.name} 智能体...
echo 配置文件: config.json
echo 访问地址: http://localhost:${standaloneConfig.runtime.port}
echo API密钥: ${standaloneConfig.runtime.apiKey}
echo.
echo 按 Ctrl+C 停止服务
node server.js
pause
`

        // 根据部署类型创建README文件
        let readme

        if (deploymentType === '2') { // 机器狗部署
          readme = `# ${agentConfig.name} - 机器狗智能体

## 🤖 简介
${agentConfig.description}

这是一个专为机器狗平台优化的智能体，支持硬件交互和实时响应。

## 🚀 快速开始

### 硬件要求
- ARM处理器 (推荐: Raspberry Pi 4 或更高)
- 内存: 至少 2GB RAM
- 存储: 至少 4GB 可用空间
- 传感器: 摄像头、麦克风、扬声器、IMU

### 安装步骤
1. 将所有文件复制到机器狗的控制系统
2. 确保Python 3.8+已安装
3. 安装依赖: \`pip3 install -r requirements.txt\`
4. 运行启动脚本: \`./start.sh\`

### 机器狗专用启动
\`\`\`bash
# 启动智能体服务
python3 robot_dog_agent.py

# 或使用启动脚本
./start.sh
\`\`\`

## 🔧 配置说明
- 主配置: \`config.json\`
- 硬件配置: 自动检测并初始化
- 控制端口: ${standaloneConfig.runtime.port}
- API密钥: ${standaloneConfig.runtime.apiKey}

## 🎯 功能特性
${agentConfig.capabilities.map(cap => `- ${cap.type || cap}: ${cap.config ? '已配置' : '基础功能'}`).join('\n')}

## 🧠 知识领域
${agentConfig.knowledge.map(know => `- ${know}`).join('\n')}

## 🔌 硬件接口
- 📷 摄像头: 视觉感知和人脸识别
- 🎤 麦克风: 语音输入和环境音检测
- 🔊 扬声器: 语音输出和音效播放
- 📡 IMU传感器: 姿态感知和运动控制
- 🦾 电机控制: 运动和动作执行

## 📋 使用说明
1. 启动后智能体会自动初始化硬件
2. 通过语音或API与智能体交互
3. 智能体可以控制机器狗的运动和行为
4. 支持实时学习和适应环境

## 🛠️ 故障排除
- 硬件初始化失败: 检查传感器连接
- 服务启动失败: 检查端口占用情况
- 语音识别问题: 检查麦克风权限

## 📞 技术支持
如有问题，请联系技术支持团队。

---
部署类型: 机器狗嵌入式部署
打包时间: ${new Date().toLocaleString()}
版本: v1.0.0
平台: ${standaloneConfig.runtime.platform}
`
        } else { // 标准部署
          readme = `# ${agentConfig.name} - 智能体服务

## 简介
${agentConfig.description}

## 快速开始

### Windows用户
双击 \`start.bat\` 启动服务

### Linux/Mac用户
运行 \`./start.sh\` 启动服务

## 访问方式
- 本地访问: http://localhost:${standaloneConfig.runtime.port}
- API接口: http://localhost:${standaloneConfig.runtime.port}/api
- API密钥: ${standaloneConfig.runtime.apiKey}

## 配置说明
- 配置文件: \`config.json\`
- 日志文件: \`logs/\`
- 数据文件: \`data/\`

## 功能特性
${agentConfig.capabilities.map(cap => `- ${cap.type || cap}`).join('\n')}

## 知识领域
${agentConfig.knowledge.map(know => `- ${know}`).join('\n')}

## 技术支持
如有问题，请联系技术支持。

---
部署类型: ${deployConfig.mode}
打包时间: ${new Date().toLocaleString()}
版本: v1.0.0
`
        }

        // 创建文件包
        const files = [
          { name: 'config.json', content: JSON.stringify(standaloneConfig, null, 2) },
          { name: 'start.sh', content: startScript },
          { name: 'start.bat', content: startBat },
          { name: 'README.md', content: readme }
        ]

        // 添加额外文件（如机器狗专用脚本）
        files.push(...additionalFiles)

        // 如果是机器狗部署，添加requirements.txt
        if (deploymentType === '2') {
          const requirements = `# 机器狗智能体依赖包
asyncio>=3.4.3
logging>=*******
json5>=0.9.6
numpy>=1.21.0
opencv-python>=4.5.0
pyaudio>=0.2.11
speech-recognition>=3.8.1
pyttsx3>=2.90
RPi.GPIO>=0.7.0  # 树莓派GPIO控制
adafruit-circuitpython-motor>=3.4.0  # 电机控制
`
          files.push({ name: 'requirements.txt', content: requirements })
        }

        // 创建部署说明文件
        const deploymentGuide = deploymentType === '2' ?
          `# 机器狗部署指南

## 硬件连接
1. 摄像头连接到USB端口
2. 麦克风和扬声器连接到音频接口
3. IMU传感器连接到I2C接口
4. 电机控制器连接到GPIO接口

## 软件配置
1. 确保系统为Linux (推荐Ubuntu 20.04 ARM64)
2. 安装Python 3.8+
3. 运行: pip3 install -r requirements.txt
4. 配置硬件权限: sudo usermod -a -G audio,video,gpio $USER

## 启动流程
1. 检查硬件连接
2. 运行: ./start.sh
3. 等待硬件初始化完成
4. 开始与智能体交互

## 注意事项
- 首次启动需要校准传感器
- 确保电源供应充足
- 定期检查硬件连接状态
` : `# 标准部署指南

## 系统要求
- 操作系统: Windows 10+, macOS 10.15+, Ubuntu 18.04+
- 内存: 至少 ${deployConfig.resources.memory}
- 处理器: ${deployConfig.resources.cpu}
- 存储: ${deployConfig.resources.storage}

## 安装步骤
1. 解压所有文件到目标目录
2. 根据操作系统运行对应的启动脚本
3. 访问 http://localhost:${standaloneConfig.runtime.port}

## 配置说明
- 修改 config.json 调整智能体参数
- 查看日志文件排查问题
- 使用API密钥进行安全访问
`

        files.push({ name: 'DEPLOYMENT.md', content: deploymentGuide })

        // 创建一个包含所有文件的压缩包（这里简化为创建一个包含所有内容的文本文件）
        const packageContent = files.map(file =>
          `=== ${file.name} ===\n${file.content}\n\n`
        ).join('')

        // 根据部署类型确定文件名
        const deploymentNames = {
          '1': '服务器版',
          '2': '机器狗版',
          '3': '边缘计算版'
        }
        const packageName = `${agentConfig.name}_${deploymentNames[deploymentType]}_${new Date().toISOString().split('T')[0]}.txt`

        // 下载打包文件
        const blob = new Blob([packageContent], { type: 'text/plain' })
        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = packageName
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url)

        ElMessage.success(`${deploymentNames[deploymentType]}打包完成！请查看下载的文件。`)

      } catch (error) {
        if (error !== 'cancel') {
          console.error('打包失败:', error)
          ElMessage.error('打包失败：' + error.message)
        }
      }
    }

    const deleteDocument = async (doc) => {
      try {
        await ElMessageBox.confirm(`确定要删除文档 "${doc.name}" 吗？`, '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 调用后端API删除文档
        const response = await fetch(`/api/v1/ai-agent/knowledge-documents/${doc.id}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        })

        if (response.ok) {
          const result = await response.json()
          if (result.success) {
            // 从前端列表中删除
            const index = knowledgeDocuments.value.findIndex(d => d.id === doc.id)
            if (index > -1) {
              knowledgeDocuments.value.splice(index, 1)
            }
            ElMessage.success('文档删除成功')
          } else {
            ElMessage.error(result.error || '删除失败')
          }
        } else {
          ElMessage.error('删除请求失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除文档失败:', error)
          ElMessage.error('删除文档失败')
        }
      }
    }

    const previewDocument = async (doc) => {
      try {
        // 调用后端API预览文档
        const response = await fetch(`/api/v1/ai-agent/knowledge-documents/${doc.id}/preview`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        })

        if (response.ok) {
          const result = await response.json()
          if (result.success) {
            const document = result.document

            // 创建预览对话框
            ElMessageBox.alert(
              `<div style="max-height: 400px; overflow-y: auto;">
                <h4>文档信息</h4>
                <p><strong>标题:</strong> ${document.title}</p>
                <p><strong>类型:</strong> ${document.content_type}</p>
                <p><strong>大小:</strong> ${formatFileSize(document.file_size)}</p>
                <p><strong>创建时间:</strong> ${new Date(document.created_at).toLocaleString()}</p>
                ${document.preview_truncated ? '<p style="color: #f56c6c;"><strong>注意:</strong> 内容已截断，完整内容请下载查看</p>' : ''}
                <h4>内容预览</h4>
                <pre style="white-space: pre-wrap; background: #f5f5f5; padding: 12px; border-radius: 4px; font-size: 12px;">${document.content}</pre>
              </div>`,
              `预览文档: ${doc.name}`,
              {
                dangerouslyUseHTMLString: true,
                confirmButtonText: '关闭'
              }
            )
          } else {
            ElMessage.error(result.error || '预览失败')
          }
        } else {
          ElMessage.error('预览请求失败')
        }
      } catch (error) {
        console.error('预览文档失败:', error)
        ElMessage.error('预览文档失败')
      }
    }

    const getDocIcon = (type) => {
      const icons = {
        pdf: '📄',
        docx: '📝',
        txt: '📃',
        md: '📋',
        xlsx: '📊',
        xls: '📊',
        csv: '📈'
      }
      return icons[type] || '📄'
    }

    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return Math.round(bytes / Math.pow(k, i) * 100) / 100 + ' ' + sizes[i]
    }

    const formatDate = (date) => {
      return new Date(date).toLocaleString('zh-CN')
    }

    const getStatusType = (status) => {
      const types = {
        processed: 'success',
        processing: 'warning',
        failed: 'danger'
      }
      return types[status] || 'info'
    }

    const getStatusText = (status) => {
      const texts = {
        processed: '已处理',
        processing: '处理中',
        failed: '处理失败'
      }
      return texts[status] || '未知'
    }

    // 加载智能体配置
    const loadAgentConfig = async () => {
      if (!isEditMode.value) return

      try {
        const agentId = route.query.agent_id
        console.log('加载智能体配置:', agentId)

        if (!agentId) {
          ElMessage.error('未指定智能体ID')
          return
        }

        // 如果是本地智能体，从localStorage加载
        if (agentId.startsWith('local-')) {
          const localAgents = JSON.parse(localStorage.getItem('local_agents') || '[]')
          const agent = localAgents.find(a => a.id === agentId)

          if (agent) {
            // 填充配置数据
            Object.assign(agentConfig, {
              name: agent.name || '',
              description: agent.description || '',
              avatar: agent.avatar || '🤖',
              personality: agent.personality || 'friendly',
              domain: agent.domain || '',
              language: agent.language || 'zh-CN',
              capabilities: agent.capabilities || [],
              knowledge: agent.knowledge || []
            })
            console.log('从本地加载智能体配置:', agent.name)
            return
          }
        }

        // 从API加载
        try {
          const response = await agentService.getAgentDetail(agentId)
          console.log('API响应数据:', response)

          // 检查响应结构：可能是 response.agent 或 response.data
          const agent = response?.agent || response?.data

          if (response && response.success && agent) {
            Object.assign(agentConfig, {
              name: agent.name || '',
              description: agent.description || '',
              avatar: agent.avatar || '🤖',
              personality: agent.personality || 'friendly',
              domain: agent.domain || '',
              language: agent.language || 'zh-CN',
              capabilities: agent.capabilities || agent.tools || [],
              knowledge: agent.knowledge || agent.knowledge_bases || [],
              systemPrompt: agent.system_prompt || '',
              maxTokens: agent.max_tokens || 4000,
              temperature: agent.temperature || 0.7
            })
            console.log('从API成功加载智能体配置:', agent.name)
            ElMessage.success(`已加载智能体 "${agent.name}" 的配置`)
            return
          } else {
            // API返回失败或没有数据
            console.log('API未找到智能体或返回失败，进入新建模式')
            ElMessage.info('未找到现有智能体数据，您可以创建新的智能体配置')
          }
        } catch (error) {
          console.error('API加载出错:', error.message)
          ElMessage.error('加载智能体数据时出错，请稍后重试')
        }

      } catch (error) {
        console.error('加载智能体配置失败:', error)
        ElMessage.error('加载智能体配置失败')
      }
    }

    // 初始化
    onMounted(async () => {
      await loadAgentConfig()
      await loadKnowledgeDocuments()

      // 根据URL参数设置活动标签页
      if (route.query.tab) {
        activeTab.value = route.query.tab
      }
    })

    return {
      saving,
      activeTab,
      isEditMode,
      agentConfig,
      editorTabs,
      avatarOptions,
      personalityOptions,
      domainOptions,
      capabilityCategories,
      memoryTypes,
      knowledgeDocuments,
      uploadUrl,
      uploadHeaders,
      goBack,
      previewAgent,
      testAgent,
      saveAgent,
      importConfig,
      exportConfig,
      resetConfig,
      toggleCategory,
      isCapabilitySelected,
      toggleCapability,
      updateMemoryConfig,
      beforeUpload,
      handleUploadSuccess,
      handleUploadError,
      loadKnowledgeDocuments,
      refreshDocuments,
      previewDocument,
      deleteDocument,
      packageStandalone,
      getDocIcon,
      formatFileSize,
      formatDate,
      getStatusType,
      getStatusText
    }
  }
}
</script>

<style scoped>
.agent-editor-new {
  min-height: 100vh;
  background: #f8fafc;
}

/* 头部导航 */
.editor-header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #64748b;
}

.back-btn:hover {
  background: #e2e8f0;
  color: #475569;
}

.header-info h1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 0.25rem 0;
}

.header-info p {
  color: #64748b;
  margin: 0;
  font-size: 0.9rem;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

/* 主要内容 */
.editor-main {
  display: flex;
  min-height: calc(100vh - 80px);
}

/* 侧边导航 */
.editor-sidebar {
  width: 280px;
  background: white;
  border-right: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
}

.sidebar-nav {
  flex: 1;
  padding: 1.5rem 0;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.nav-item:hover {
  background: #f8fafc;
}

.nav-item.active {
  background: #e0e7ff;
  border-left-color: #667eea;
  color: #667eea;
}

.nav-icon {
  font-size: 1.2rem;
  margin-right: 0.75rem;
}

.nav-label {
  flex: 1;
  font-weight: 500;
}

.nav-badge {
  background: #667eea;
  color: white;
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.quick-actions {
  border-top: 1px solid #e2e8f0;
  padding: 1rem 0;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #64748b;
  font-size: 0.9rem;
}

.action-item:hover {
  background: #f8fafc;
  color: #475569;
}

.action-item .el-icon {
  margin-right: 0.5rem;
}

/* 内容区域 */
.editor-content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
}

.tab-content {
  max-width: 800px;
}

.content-header {
  margin-bottom: 2rem;
}

.content-header h2 {
  font-size: 2rem;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 0.5rem;
}

.content-header p {
  color: #64748b;
  font-size: 1.1rem;
}

/* 表单区域 */
.form-sections {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-section {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
}

.form-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #f1f5f9;
}

/* 头像选择器 */
.avatar-selector {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
  gap: 0.5rem;
  max-width: 400px;
}

.avatar-option {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.avatar-option:hover {
  border-color: #667eea;
}

.avatar-option.selected {
  border-color: #667eea;
  background: #e0e7ff;
}

/* 能力配置 */
.capabilities-grid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.capability-category {
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.category-header {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.category-icon {
  font-size: 1.5rem;
  margin-right: 1rem;
}

.category-info {
  flex: 1;
}

.category-info h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 0.25rem 0;
}

.category-info p {
  color: #64748b;
  margin: 0;
  font-size: 0.9rem;
}

.capability-items {
  padding: 1rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.capability-item {
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.capability-item:hover {
  border-color: #667eea;
}

.capability-item.selected {
  border-color: #667eea;
  background: #f0f4ff;
}

.capability-item.recommended::before {
  content: '推荐';
  position: absolute;
  top: -8px;
  right: 12px;
  background: #ff9800;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.capability-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.capability-icon {
  font-size: 1.2rem;
  margin-right: 0.5rem;
}

.capability-name {
  font-weight: 600;
  color: #1a202c;
  flex: 1;
}

.recommended-badge {
  background: #ff9800;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
}

.capability-desc {
  color: #64748b;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.capability-example {
  font-size: 0.8rem;
  color: #94a3b8;
  font-style: italic;
}

/* 知识库管理 */
.knowledge-sections {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.knowledge-explanation {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
}

.upload-section {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
}

.upload-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 1rem;
}

.documents-section {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.section-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

.documents-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.document-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.doc-icon {
  font-size: 1.5rem;
  margin-right: 1rem;
}

.doc-info {
  flex: 1;
}

.doc-name {
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 0.25rem;
}

.doc-meta {
  color: #64748b;
  font-size: 0.8rem;
}

.doc-status {
  margin-right: 1rem;
}

.doc-actions {
  display: flex;
  gap: 0.5rem;
}

/* 记忆设置 */
.memory-sections {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.memory-section {
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.memory-header {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.memory-icon {
  font-size: 1.5rem;
  margin-right: 1rem;
}

.memory-info {
  flex: 1;
}

.memory-info h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 0.25rem 0;
}

.memory-info p {
  color: #64748b;
  margin: 0;
  font-size: 0.9rem;
}

.memory-config {
  padding: 1.5rem;
}

/* 高级设置 */
.advanced-sections {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.advanced-section {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
}

.advanced-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #f1f5f9;
}

.param-hint {
  font-size: 0.8rem;
  color: #64748b;
  margin-top: 0.25rem;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .editor-main {
    flex-direction: column;
  }

  .editor-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #e2e8f0;
  }

  .sidebar-nav {
    display: flex;
    overflow-x: auto;
    padding: 1rem 0;
  }

  .nav-item {
    flex-shrink: 0;
    border-left: none;
    border-bottom: 3px solid transparent;
  }

  .nav-item.active {
    border-left: none;
    border-bottom-color: #667eea;
  }

  .quick-actions {
    display: none;
  }
}

@media (max-width: 768px) {
  .editor-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }

  .editor-content {
    padding: 1rem;
  }

  .capability-items {
    grid-template-columns: 1fr;
  }

  .avatar-selector {
    grid-template-columns: repeat(6, 1fr);
  }
}
</style>
