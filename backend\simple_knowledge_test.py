#!/usr/bin/env python3
"""
简单的知识库测试
"""

import requests
import json
import time
import os

def test_simple_upload_and_query():
    """简单测试文件上传和查询"""
    
    print("📚 简单知识库测试")
    print("=" * 30)
    
    # 创建测试文件
    timestamp = int(time.time())
    test_content = f"简单测试文档\n时间戳: {timestamp}\n内容: 测试知识库功能"
    test_filename = f"simple_test_{timestamp}.txt"
    
    try:
        # 1. 创建测试文件
        with open(test_filename, 'w', encoding='utf-8') as f:
            f.write(test_content)
        print(f"📁 创建测试文件: {test_filename}")
        
        # 2. 上传文件
        upload_url = "http://localhost:8000/api/v1/ai-agent/upload-file"
        print(f"🚀 上传文件到: {upload_url}")
        
        with open(test_filename, 'rb') as f:
            files = {'file': (test_filename, f, 'text/plain')}
            response = requests.post(upload_url, files=files, timeout=30)
        
        print(f"📊 上传状态: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"📄 上传响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('success'):
                print("✅ 文件上传成功")
            else:
                print(f"❌ 上传失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 上传请求失败: {response.text}")
            return False
        
        # 等待处理
        print("⏳ 等待3秒...")
        time.sleep(3)
        
        # 3. 查询知识库文档
        docs_url = "http://localhost:8000/api/v1/ai-agent/knowledge-documents"
        print(f"🔍 查询文档列表: {docs_url}")
        
        response = requests.get(docs_url, timeout=15)
        
        print(f"📊 查询状态: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"📄 查询响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('success'):
                documents = result.get('documents', [])
                total = result.get('total', 0)
                
                print(f"✅ 查询成功，文档总数: {total}")
                
                if documents:
                    print("📋 文档列表:")
                    for i, doc in enumerate(documents):
                        print(f"  {i+1}. {doc.get('filename')} (ID: {doc.get('id')})")
                    
                    # 检查是否包含我们上传的文件
                    uploaded_found = any(doc.get('filename') == test_filename for doc in documents)
                    print(f"🔍 上传的文件是否在列表中: {'✅ 是' if uploaded_found else '❌ 否'}")
                    
                    return uploaded_found
                else:
                    print("📋 文档列表为空")
                    return False
            else:
                print(f"❌ 查询失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 查询请求失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        return False
    finally:
        # 清理测试文件
        if os.path.exists(test_filename):
            os.remove(test_filename)
            print(f"🗑️ 清理测试文件: {test_filename}")

def test_direct_db_query():
    """直接查询数据库"""
    
    print("\n🗄️ 直接数据库查询测试")
    print("=" * 30)
    
    try:
        from app.core.database import get_db_manager
        
        db_manager = get_db_manager()
        
        # 查询所有知识库文档
        print("🔍 查询数据库中的知识库文档...")
        
        query = "SELECT * FROM knowledge_documents ORDER BY created_at DESC LIMIT 5"
        documents = db_manager.execute_query(query)
        
        if documents:
            print(f"✅ 数据库中找到 {len(documents)} 个文档:")
            for i, doc in enumerate(documents):
                print(f"  {i+1}. ID: {doc.get('id')}")
                print(f"      标题: {doc.get('title', 'N/A')}")
                print(f"      类型: {doc.get('content_type', 'N/A')}")
                print(f"      创建时间: {doc.get('created_at', 'N/A')}")
                print()
            return True
        else:
            print("📋 数据库中没有找到知识库文档")
            return False
            
    except Exception as e:
        print(f"❌ 数据库查询出错: {e}")
        return False

if __name__ == "__main__":
    print("🧪 简单知识库功能测试")
    print("=" * 40)
    
    # 测试上传和API查询
    api_test = test_simple_upload_and_query()
    
    # 测试直接数据库查询
    db_test = test_direct_db_query()
    
    print("\n" + "=" * 40)
    print("📊 测试结果:")
    print(f"  API上传查询: {'✅ 成功' if api_test else '❌ 失败'}")
    print(f"  直接数据库查询: {'✅ 成功' if db_test else '❌ 失败'}")
    
    if api_test and db_test:
        print("\n🎉 知识库功能完全正常！")
    elif db_test and not api_test:
        print("\n⚠️ 数据库有数据，但API查询有问题")
    elif api_test and not db_test:
        print("\n⚠️ API正常，但数据库查询有问题")
    else:
        print("\n❌ 知识库功能存在问题")
