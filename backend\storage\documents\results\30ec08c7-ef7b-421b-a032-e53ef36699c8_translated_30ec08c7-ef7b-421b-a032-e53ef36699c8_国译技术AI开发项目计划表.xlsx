[{"id": 1, "source": "[ROW_0] 文生视频 | 阿里通义万相\nhttps://tongyi.aliyun.com/wanxiang/ | 大规模视频生成模型\nhttps://github.com/Wan-Video/Wan2.1 | 显卡：NVIDIA RTX 3060及以上（推荐RTX 4060 Ti 16GB）‌\n显存：≥8GB（运行1.3B基础模型）/ ≥16GB（运行14B高清模型）‌\n存储：SSD预留50GB空间（模型文件约35GB）‌ | 1、集成到ComfyUI，测试开源库生成视频的效果。（1周）\n2、设计用户操作界面，调用开源库完成视频制作。（1-2周）\n3、测试与部署。（1-2周） | 目前服务器的显卡3060，生成效果不理想", "translated": "[ROW_0] Video Generation by Wen | <PERSON>\nhttps://tongyi.aliyun.com/wanxiang/ | Large-scale video generation model\nhttps://github.com/Wan-Video/Wan2.1 | GPU: NVIDIA RTX 3060 or above (Recommended: RTX 4060 Ti 16GB)\nVRAM: ≥8GB (for running the 1.3B base model) / ≥16GB (for running the 14B HD model)\nStorage: Reserve 50GB of SSD space for model files (approximately 35GB) | \n1. Integrate into ComfyUI to test the effects of video generation by open-source libraries. (1 week)\n2. Design user interface and call open-source libraries to complete video production. (1-2 weeks)\n3. Test and deploy. (1-2 weeks) | The current server's GPU is a 3060, which yields unsatisfactory results."}, {"id": 2, "source": "[ROW_1] 数字人 | 万彩AI\nhttps://ai.kezhan365.com/ | 虚拟主播表情驱动 Audio2Face + UE5 \n用户意图理解 Rasa + spaCy | / | 1、服务器部署deepseek大模型，外部应用调用本地大模型API.(1周）\n2、开源语音、表情等源码集成调试（1-2周）\n3、设计用户操作界面，生成数字人 （2-3周）\n4、测试与部署。（1-2周）", "translated": "[ROW_1] Digital Humans | Wancai AI\nhttps://ai.kezhan365.com/ | Virtual Hostcaster Expression Control Audio2Face + UE5\nUser Intent Understanding Rasa + spaCy | / |\n1. Deploy the deepseek large model on servers, with external applications calling local API (1 week).\n2. Integrate and debug open-source voice, expression, etc., source codes (1-2 weeks).\n3. Design user interface and generate digital humans (2-3 weeks).\n4. Test and deploy (1-2 weeks)."}, {"id": 3, "source": "[ROW_2] AI同传翻译 | 百度AI同传\nhttps://tongchuan.baidu.com/ | 多语种实时翻译 Whisper + Transformers | / | 1、服务器部署deepseek大模型，外部应用调用本地大模型API.(1周）\n2、开源语音、文字等源码集成调试（1-2周）\n3、设计用户操作界面，生成AI同传翻译 （2-3周）\n4、测试与部署。（1-2周）", "translated": "[ROW_2] AI Simultaneous Interpretation | Baidu AI Simultaneous Interpretation\nhttps://tongchuan.baidu.com/ | Real-time translation in multiple languages using Whisper + Transformers | / | \n1. Deploy the deepseek large model on servers, with external applications calling local large model APIs (1 week).\n2. Integrate and debug open-source voice and text code (1-2 weeks).\n3. Design user interface for operation, generating AI simultaneous interpretation (2-3 weeks).\n4. Testing and deployment (1-2 weeks)."}, {"id": 4, "source": "[ROW_3] 多语数据管理 | 开源语言资产管理平台\nhttps://github.com/hanlintao/BiCorpus | / | 1、测试开源系统 （2天）\n2、设计操作界面（4天）\n3、测试与部署 （3天）", "translated": "[ROW_3] Multilingual Data Management | Open Source Language Asset Management Platform\nhttps://github.com/hanlintao/BiCorpus | / | 1. Test the Open Source System (2 days)\n2. Design the User Interface (4 days)\n3. Test and Deploy (3 days)"}]