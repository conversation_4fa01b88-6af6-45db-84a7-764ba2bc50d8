# 文件处理相关依赖包
# 这些包用于处理各种文件格式的上传和解析

# Excel文件处理
openpyxl>=3.0.9              # Excel 2007+ (.xlsx) 文件处理
xlrd>=2.0.1                  # Excel 97-2003 (.xls) 文件处理
pandas>=1.3.0                # 数据分析和Excel处理的备用方案

# PDF文件处理
PyPDF2>=2.10.0               # PDF文件文本提取
pdfplumber>=0.7.0            # PDF文件高级处理（备用）

# Word文档处理
python-docx>=0.8.11          # Word文档 (.docx) 处理
python-doc>=0.1.0            # Word文档 (.doc) 处理（如果需要）

# 文本编码检测
chardet>=4.0.0               # 自动检测文件编码

# CSV处理（Python内置csv模块已足够，但pandas提供更强功能）
# pandas已在上面列出

# 图像处理（如果需要处理图像文件）
Pillow>=8.3.0                # 图像处理

# 其他可能需要的依赖
python-magic>=0.4.24         # 文件类型检测
python-magic-bin>=0.4.14    # Windows上的python-magic支持

# 安装说明：
# pip install -r requirements-file-processing.txt
