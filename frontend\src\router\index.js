import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/auth'
import MainLayout from '../views/layouts/MainLayout.vue'

// 路由组件 - 只导入存在的组件
import Home from '../views/home/<USER>'

// 导入模块化路由
import translationRoutes from '../modules/translation/index.js'
import aiAgentRoutes from '../modules/ai-agent/index.js'
import digitalHumanRoutes from '../modules/digital-human/routes.js'
import videoGenerationRoutes from '../modules/video-generation/index.js'
import terminologyRoutes from '../modules/terminology/index.js'
import utilitiesRoutes from '../modules/utilities/index.js'
import explorationRoutes from '../modules/exploration/index.js'
import resumeRoutes from '../modules/resume/index.js'
import taskRoutes from '../modules/task/index.js'

// 导入聊天组件
const TrueAgentChatNew = () => import('../modules/ai-agent/views/FullScreenChat.vue')

// 创建占位符组件
const PlaceholderComponent = {
  template: `
    <div class="page-container" style="padding: 40px 20px; max-width: 1200px; margin: 0 auto;">
      <el-card>
        <h2>{{ title }}</h2>
        <p>{{ description }}</p>
        <el-alert 
          title="功能开发中" 
          type="info" 
          description="此功能正在开发中，敬请期待！"
          show-icon
        />
      </el-card>
    </div>
  `,
  props: {
    title: { type: String, default: '功能页面' },
    description: { type: String, default: '此功能正在开发中' }
  }
}

// 错误页面
const NotFound = {
  template: `
    <div class="not-found" style="text-align: center; padding: 50px;">
      <el-result
        icon="warning"
        title="404"
        sub-title="抱歉，您访问的页面不存在"
      >
        <template #extra>
          <el-button type="primary" @click="$router.push('/')">返回首页</el-button>
        </template>
      </el-result>
    </div>
  `
}

const routes = [
  // 数字人模块路由（独立布局）- 放在前面以确保优先匹配
  ...digitalHumanRoutes,

  {
    path: '/',
    component: MainLayout,
    children: [
      {
        path: '',
        name: 'Home',
        component: Home,
        meta: { title: '首页' }
      },
      {
        path: 'home',
        redirect: '/',
        meta: { title: '首页' }
      },
      // 使用模块化路由
      ...translationRoutes,
      ...aiAgentRoutes,
      ...videoGenerationRoutes,
      ...terminologyRoutes,
      ...utilitiesRoutes,
      ...explorationRoutes,
      ...resumeRoutes,
      ...taskRoutes,



      {
        path: 'text-to-video',
        name: 'TextToVideo',
        component: () => ({
          ...PlaceholderComponent,
          data() {
            return {
              title: '文生视频',
              description: '将您的文字描述转化为精彩视频内容'
            }
          }
        }),
        meta: { title: '文生视频' }
      },
      {
        path: 'chat',
        name: 'Chat',
        component: () => ({
          ...PlaceholderComponent,
          data() {
            return {
              title: '智能对话',
              description: '与AI进行自然对话交流'
            }
          }
        }),
        meta: { title: '智能对话' }
      },
      {
        path: 'profile',
        name: 'Profile',
        component: () => ({
          ...PlaceholderComponent,
          data() {
            return {
              title: '个人中心',
              description: '管理您的个人信息和设置'
            }
          }
        }),
        meta: { title: '个人中心' }
      },
      // 注意：工具菜单路径、深度探索、任务管理等已移动到对应模块中

      // 便民工具
      {
        path: 'utilities/daily/agent-marketplace',
        name: 'AgentMarketplace',
        component: () => import('../modules/ai-agent/views/AgentMarketplaceNew.vue'),
        meta: { title: 'AI智能体' }
      },
      {
        path: 'utilities/daily/agent-editor',
        name: 'AgentEditor',
        component: () => import('../modules/ai-agent/views/AgentEditorSimple.vue'),
        meta: { title: '编辑智能体' }
      },
      {
        path: 'utilities/daily/ai-agent',
        name: 'AgentDetail',
        component: () => import('../modules/ai-agent/views/AgentDetailPro.vue'),
        meta: { title: '智能体详情' }
      },
      {
        path: 'utilities/daily/unified-chat',
        name: 'UnifiedChat',
        component: () => import('../modules/ai-agent/views/UnifiedAgentChat.vue'),
        meta: { title: '智能体对话' }
      },
      {
        path: 'utilities/daily/language-learning-data',
        name: 'LanguageLearningData',
        component: () => import('../modules/ai-agent/views/LanguageLearningData.vue'),
        meta: { title: '学习数据管理' }
      },
      {
        path: 'utilities/daily/weather',
        name: 'Weather',
        component: () => import('../modules/ai-agent/views/Weather.vue'),
        meta: { title: '气象预警' }
      },
      // 注意：简历制作已移动到 resume 模块中
    ]
  },

  // 全屏智能体聊天路由（无菜单）
  {
    path: '/chat/:id',
    name: 'FullScreenChat',
    component: TrueAgentChatNew,
    meta: {
      title: '智能体对话',
      hideNavigation: true,  // 隐藏导航
      fullscreen: true       // 全屏模式
    }
  },

  // 新的智能体聊天路由（优先级更高）
  {
    path: '/ai-agent/chat/:id',
    name: 'TrueAgentChatNew',
    component: TrueAgentChatNew,
    meta: { title: '智能体对话' }
  },

  {
    path: '/user/login',
    name: 'Login',
    component: () => import('../views/user/Login.vue'),
    meta: { title: '登录' }
  },
  {
    path: '/user/register',
    name: 'Register',
    component: () => import('../views/user/Register.vue'),
    meta: { title: '注册' }
  },
  {
    path: '/user/profile',
    name: 'UserProfile',
    component: () => import('../views/user/UserProfile.vue'),
    meta: { title: '个人资料' }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 多语数据`
  }

  // 检查是否需要登录
  if (to.meta.requiresAuth) {
    const authStore = useAuthStore()

    // 如果当前未认证，尝试刷新token
    if (!authStore.isAuthenticated) {
      console.log('[Router] 用户未认证，尝试刷新token')

      try {
        // 尝试刷新token
        const refreshResult = await authStore.refreshAccessToken()

        if (refreshResult && authStore.isAuthenticated) {
          console.log('[Router] Token刷新成功，允许访问')
          next()
          return
        }
      } catch (error) {
        console.warn('[Router] Token刷新失败:', error)
      }

      // 刷新失败或仍未认证，跳转到登录页面
      console.log('[Router] 需要登录，跳转到登录页面，原路径:', to.fullPath)
      next({
        path: '/user/login',
        query: { redirect: to.fullPath }
      })
      return
    }
  }

  next()
})

export default router
