<template>
  <div class="unified-agent-chat">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container" v-loading="true" element-loading-text="正在加载智能体...">
      <div style="height: 200px;"></div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <div class="error-content">
        <h3>加载失败</h3>
        <p>{{ error }}</p>
        <el-button type="primary" @click="retry">重试</el-button>
      </div>
    </div>
    
    <!-- 动态聊天界面 -->
    <component
      v-else-if="chatComponent"
      :is="chatComponent"
      :agent="agent"
      :agent-config="agentConfig"
      v-bind="chatProps"
      @agent-updated="handleAgentUpdated"
    />

    <!-- 默认聊天界面 -->
    <FullScreenChat
      v-else
      :agent="agent"
      :agent-config="agentConfig"
      v-bind="chatProps"
      @agent-updated="handleAgentUpdated"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getAgentTypeConfig, getChatInterfaceComponent } from '@/config/agentTypes.js'
import agentService from '@/services/agent-service.js'
import LanguageLearningChat from './LanguageLearningChat.vue'
import FullScreenChat from './FullScreenChat.vue'

// 动态导入聊天组件
const chatComponents = {
  LanguageLearningChat: () => import('./LanguageLearningChat.vue'),
  FullScreenChat: () => import('./FullScreenChat.vue')
}

export default {
  name: 'UnifiedAgentChat',
  components: {
    LanguageLearningChat,
    FullScreenChat
  },
  
  setup() {
    const route = useRoute()
    const router = useRouter()
    
    // 状态管理
    const loading = ref(true)
    const error = ref(null)
    const agent = ref(null)
    const agentConfig = ref(null)
    const chatComponent = ref(null)
    
    // 计算属性
    const chatProps = computed(() => {
      if (!agent.value || !agentConfig.value) return {}
      
      return {
        agentId: agent.value.id,
        agentType: agent.value.agent_type || 'general',
        sessionId: route.query.session_id,
        mock: route.query.mock === '1',
        demo: route.query.demo === '1',
        ...route.query
      }
    })
    
    // 加载智能体信息
    const loadAgent = async () => {
      try {
        loading.value = true
        error.value = null

        const agentId = route.query.agent_id || route.params.agentId
        const isTest = route.query.test === 'true'

        if (!agentId) {
          throw new Error('缺少智能体ID参数')
        }

        console.log('[UnifiedAgentChat] 加载智能体:', agentId, '测试模式:', isTest)

        // 如果是测试模式，从sessionStorage加载数据
        if (isTest) {
          const testData = sessionStorage.getItem('test_agent_data')
          if (testData) {
            const testAgent = JSON.parse(testData)
            agent.value = testAgent
            agentConfig.value = getAgentTypeConfig(testAgent.agent_type || 'general')
            console.log('[UnifiedAgentChat] 从测试数据加载智能体:', testAgent.name)
            return
          }
        }
        
        // 获取智能体信息
        const agentData = await agentService.getAgentDetail(agentId)
        if (!agentData) {
          throw new Error('智能体不存在')
        }
        
        agent.value = agentData
        
        // 获取智能体类型配置
        const agentType = agentData.agent_type || 'general'
        agentConfig.value = getAgentTypeConfig(agentType)
        
        // 确定使用的聊天界面组件
        const interfaceComponent = getChatInterfaceComponent(agentType)
        
        console.log('[UnifiedAgentChat] 智能体类型:', agentType)
        console.log('[UnifiedAgentChat] 使用界面:', interfaceComponent)
        
        // 设置对应的聊天组件名称
        if (interfaceComponent && chatComponents[interfaceComponent]) {
          chatComponent.value = interfaceComponent
        } else {
          // 默认使用专业聊天界面
          chatComponent.value = 'AgentChatPro'
        }
        
      } catch (err) {
        console.error('[UnifiedAgentChat] 加载智能体失败:', err)
        error.value = err.message || '加载智能体失败'
      } finally {
        loading.value = false
      }
    }
    
    // 重试加载
    const retry = () => {
      loadAgent()
    }
    
    // 处理智能体更新
    const handleAgentUpdated = (updatedAgent) => {
      agent.value = { ...agent.value, ...updatedAgent }
    }
    
    // 监听路由变化
    watch(() => route.query.agent_id, (newAgentId, oldAgentId) => {
      if (newAgentId && newAgentId !== oldAgentId) {
        loadAgent()
      }
    })
    
    // 组件挂载时加载
    onMounted(() => {
      loadAgent()
    })
    
    return {
      loading,
      error,
      agent,
      agentConfig,
      chatComponent,
      chatProps,
      retry,
      handleAgentUpdated
    }
  }
}
</script>

<style scoped>
.unified-agent-chat {
  height: 100vh;
  width: 100%;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #fafafa;
}

.loading-text {
  margin-top: 16px;
  color: #666;
  font-size: 16px;
}

.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #fafafa;
}

.error-content {
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.error-content h3 {
  margin: 0 0 16px 0;
  color: #ff4d4f;
  font-size: 20px;
}

.error-content p {
  margin: 0 0 24px 0;
  color: #666;
  font-size: 14px;
}
</style>
