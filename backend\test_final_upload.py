#!/usr/bin/env python3
"""
最终测试文件上传和知识库功能
"""

import requests
import json
import time
import os

def test_final_upload():
    """最终测试文件上传"""
    
    print("🎯 最终知识库上传测试")
    print("=" * 40)
    
    # 创建测试文件
    timestamp = int(time.time())
    test_content = f"""最终测试文档
时间戳: {timestamp}
内容: 测试知识库上传和显示功能
目标: 验证文件上传后能在知识库列表中看到

这是一个完整的测试文档，用于验证：
1. 文件上传成功
2. 数据库保存成功
3. 知识库列表显示成功
4. 数据持久化成功"""
    
    test_filename = f"final_test_{timestamp}.txt"
    
    try:
        # 1. 创建测试文件
        with open(test_filename, 'w', encoding='utf-8') as f:
            f.write(test_content)
        print(f"📁 创建测试文件: {test_filename}")
        
        # 2. 上传文件
        upload_url = "http://localhost:8000/api/v1/ai-agent/upload-file"
        print(f"🚀 上传文件到: {upload_url}")
        
        with open(test_filename, 'rb') as f:
            files = {'file': (test_filename, f, 'text/plain')}
            response = requests.post(upload_url, files=files, timeout=30)
        
        print(f"📊 上传状态: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"📄 上传响应:")
            print(f"  成功: {result.get('success')}")
            print(f"  消息: {result.get('message')}")
            print(f"  文件名: {result.get('filename')}")
            print(f"  大小: {result.get('size')} bytes")
            
            # 检查是否有知识库ID
            data = result.get('data', {})
            knowledge_id = data.get('knowledge_id')
            print(f"  知识库ID: {knowledge_id}")
            
            if result.get('success'):
                print("✅ 文件上传成功")
            else:
                print(f"❌ 上传失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 上传请求失败: {response.text}")
            return False
        
        # 等待处理
        print("⏳ 等待5秒确保数据库操作完成...")
        time.sleep(5)
        
        # 3. 查询知识库文档列表
        docs_url = "http://localhost:8000/api/v1/ai-agent/knowledge-documents"
        print(f"🔍 查询文档列表: {docs_url}")
        
        response = requests.get(docs_url, timeout=15)
        
        print(f"📊 查询状态: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                documents = result.get('documents', [])
                total = result.get('total', 0)
                
                print(f"✅ 查询成功，文档总数: {total}")
                
                if documents:
                    print("📋 文档列表:")
                    for i, doc in enumerate(documents):
                        print(f"  {i+1}. 文件名: {doc.get('filename')}")
                        print(f"      ID: {doc.get('id')}")
                        print(f"      类型: {doc.get('file_type')}")
                        print(f"      大小: {doc.get('size')} bytes")
                        print(f"      创建时间: {doc.get('created_at')}")
                        print()
                    
                    # 检查是否包含我们上传的文件
                    uploaded_found = any(doc.get('filename') == test_filename for doc in documents)
                    print(f"🔍 上传的文件是否在列表中: {'✅ 是' if uploaded_found else '❌ 否'}")
                    
                    if uploaded_found:
                        print("🎉 知识库功能完全正常！文件上传后可以在列表中看到！")
                        return True
                    else:
                        print("⚠️ 文件上传成功但未在列表中显示")
                        return False
                else:
                    print("📋 文档列表为空")
                    return False
            else:
                print(f"❌ 查询失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 查询请求失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        return False
    finally:
        # 清理测试文件
        if os.path.exists(test_filename):
            os.remove(test_filename)
            print(f"🗑️ 清理测试文件: {test_filename}")

def test_direct_db_check():
    """直接检查数据库"""
    
    print("\n🗄️ 直接数据库检查")
    print("=" * 30)
    
    try:
        from app.core.database import get_db_manager
        
        db_manager = get_db_manager()
        
        # 查询最新的知识库文档
        print("🔍 查询数据库中的最新文档...")
        
        query = "SELECT id, title, content_type, created_at FROM knowledge_documents ORDER BY created_at DESC LIMIT 3"
        documents = db_manager.execute_query(query)
        
        if documents:
            print(f"✅ 数据库中找到 {len(documents)} 个文档:")
            for i, doc in enumerate(documents):
                print(f"  {i+1}. ID: {doc.get('id')}")
                print(f"      标题: {doc.get('title')}")
                print(f"      类型: {doc.get('content_type')}")
                print(f"      创建时间: {doc.get('created_at')}")
                print()
            return True
        else:
            print("📋 数据库中没有找到知识库文档")
            return False
            
    except Exception as e:
        print(f"❌ 数据库查询出错: {e}")
        return False

if __name__ == "__main__":
    print("🎯 最终知识库功能验证测试")
    print("=" * 50)
    
    # 测试文件上传和列表显示
    upload_test = test_final_upload()
    
    # 测试直接数据库查询
    db_test = test_direct_db_check()
    
    print("\n" + "=" * 50)
    print("📊 最终测试结果:")
    print(f"  文件上传和列表显示: {'✅ 成功' if upload_test else '❌ 失败'}")
    print(f"  数据库数据验证: {'✅ 成功' if db_test else '❌ 失败'}")
    
    if upload_test and db_test:
        print("\n🎉 知识库功能完全正常！")
        print("💡 用户现在可以：")
        print("   1. 上传各种格式的文件")
        print("   2. 在知识库列表中看到上传的文件")
        print("   3. 文件信息持久保存在数据库中")
        print("   4. 再次进入页面时文件仍然存在")
    else:
        print("\n⚠️ 知识库功能仍有问题，需要进一步调试")
