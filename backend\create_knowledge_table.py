#!/usr/bin/env python3
"""
创建知识库文档表
"""

import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)
sys.path.insert(0, current_dir)

from backend.shared.database.db_manager import get_db_manager

def create_knowledge_documents_table():
    """创建知识库文档表"""
    
    print("🗄️ 创建知识库文档表")
    print("=" * 40)
    
    try:
        db_manager = get_db_manager()
        
        # 创建knowledge_documents表
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS knowledge_documents (
            id SERIAL PRIMARY KEY,
            filename VARCHAR(255) NOT NULL,
            file_type VARCHAR(50) NOT NULL,
            content TEXT,
            metadata JSONB,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        
        print("📝 执行创建表SQL...")
        db_manager.execute_query(create_table_sql)
        print("✅ knowledge_documents表创建成功")
        
        # 检查表是否存在
        check_sql = """
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'knowledge_documents'
        """
        
        result = db_manager.execute_query(check_sql)
        if result:
            print("✅ 表存在验证成功")
        else:
            print("❌ 表不存在")
            return False
        
        # 检查表结构
        columns_sql = """
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns
        WHERE table_name = 'knowledge_documents'
        ORDER BY ordinal_position
        """
        
        columns = db_manager.execute_query(columns_sql)
        if columns:
            print("📋 表结构:")
            for col in columns:
                print(f"  - {col['column_name']}: {col['data_type']} ({'NULL' if col['is_nullable'] == 'YES' else 'NOT NULL'})")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建表失败: {e}")
        return False

def test_insert_and_query():
    """测试插入和查询"""
    
    print("\n🧪 测试插入和查询")
    print("=" * 30)
    
    try:
        db_manager = get_db_manager()
        
        # 测试插入
        import json
        from datetime import datetime
        
        test_data = {
            'filename': 'test_document.txt',
            'file_type': 'txt',
            'content': '这是一个测试文档的内容',
            'metadata': json.dumps({
                'summary': '测试文档',
                'type': 'txt',
                'size': 100
            }),
            'created_at': datetime.now().isoformat()
        }
        
        print("📝 插入测试数据...")
        insert_sql = """
        INSERT INTO knowledge_documents 
        (filename, file_type, content, metadata, created_at)
        VALUES (%s, %s, %s, %s, %s) 
        RETURNING id
        """
        
        result = db_manager.execute_query(
            insert_sql,
            [
                test_data['filename'],
                test_data['file_type'],
                test_data['content'],
                test_data['metadata'],
                test_data['created_at']
            ]
        )
        
        if result:
            doc_id = result[0]['id'] if isinstance(result[0], dict) else result[0][0]
            print(f"✅ 插入成功，ID: {doc_id}")
        else:
            print("❌ 插入失败，未返回ID")
            return False
        
        # 测试查询
        print("🔍 查询所有文档...")
        query_sql = "SELECT * FROM knowledge_documents ORDER BY created_at DESC"
        documents = db_manager.execute_query(query_sql)
        
        if documents:
            print(f"✅ 查询成功，找到 {len(documents)} 个文档:")
            for i, doc in enumerate(documents[:3]):  # 显示前3个
                print(f"  {i+1}. {doc.get('filename')} ({doc.get('file_type')}) - ID: {doc.get('id')}")
        else:
            print("❌ 查询失败或无数据")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🗄️ 知识库表创建和测试工具")
    print("=" * 50)
    
    # 创建表
    create_success = create_knowledge_documents_table()
    
    if create_success:
        # 测试插入和查询
        test_success = test_insert_and_query()
        
        print("\n" + "=" * 50)
        print("📊 结果汇总:")
        print(f"  表创建: {'✅ 成功' if create_success else '❌ 失败'}")
        print(f"  插入查询测试: {'✅ 成功' if test_success else '❌ 失败'}")
        
        if create_success and test_success:
            print("\n🎉 知识库表创建和测试完成！")
            print("💡 现在可以正常使用知识库功能了")
        else:
            print("\n⚠️ 部分操作失败，请检查数据库配置")
    else:
        print("\n❌ 表创建失败，无法继续测试")
