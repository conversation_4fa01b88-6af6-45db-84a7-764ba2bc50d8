<template>
  <el-dialog
    v-model="visible"
    title="模型配置"
    width="800px"
    :before-close="handleClose"
  >
    <div class="model-config-dialog">
      <!-- 基础模型选择 -->
      <div class="config-section">
        <h3>🤖 基础模型选择</h3>
        <div class="model-selection">
          <el-radio-group v-model="config.use_custom_model" @change="onModelTypeChange">
            <el-radio :value="false">使用标准模型</el-radio>
            <el-radio :value="true">使用自定义训练模型</el-radio>
          </el-radio-group>
          
          <!-- 标准模型选择 -->
          <div v-if="!config.use_custom_model" class="standard-models">
            <div class="models-grid">
              <div 
                v-for="model in availableModels" 
                :key="model.name"
                :class="['model-card', { selected: config.model_name === model.name }]"
                @click="selectModel(model)"
              >
                <div class="model-header">
                  <span class="model-name">{{ model.display_name }}</span>
                  <span v-if="model.recommended" class="recommended-badge">推荐</span>
                </div>
                <div class="model-info">
                  <p class="model-desc">{{ model.description }}</p>
                  <div class="model-details">
                    <span class="detail-item">大小: {{ formatSize(model.size) }}</span>
                    <span class="detail-item">语言: {{ model.language_support.join(', ') }}</span>
                  </div>
                  <div class="model-use-cases">
                    <span v-for="useCase in model.use_cases" :key="useCase" class="use-case-tag">
                      {{ useCase }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 自定义模型选择 -->
          <div v-else class="custom-models">
            <el-select 
              v-model="config.custom_model_id" 
              placeholder="选择自定义训练的模型"
              style="width: 100%"
            >
              <el-option
                v-for="model in customModels"
                :key="model.id"
                :label="model.name"
                :value="model.id"
              >
                <span>{{ model.name }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ model.profession_type }}</span>
              </el-option>
            </el-select>
          </div>
        </div>
      </div>
      
      <!-- 模型参数配置 -->
      <div class="config-section">
        <h3>⚙️ 模型参数</h3>
        <el-form :model="config" label-width="120px">
          <el-form-item label="创造性">
            <el-slider 
              v-model="config.temperature"
              :min="0"
              :max="2"
              :step="0.1"
              show-tooltip
            />
            <div class="param-hint">控制回答的创造性和随机性 (0-2)</div>
          </el-form-item>
          
          <el-form-item label="最大长度">
            <el-slider 
              v-model="config.max_tokens"
              :min="100"
              :max="4000"
              :step="100"
              show-tooltip
            />
            <div class="param-hint">控制回答的最大长度 (100-4000)</div>
          </el-form-item>
          
          <el-form-item label="核心采样">
            <el-slider 
              v-model="config.top_p"
              :min="0.1"
              :max="1"
              :step="0.1"
              show-tooltip
            />
            <div class="param-hint">控制词汇选择的多样性 (0.1-1.0)</div>
          </el-form-item>
          
          <el-form-item label="重复惩罚">
            <el-slider 
              v-model="config.repeat_penalty"
              :min="1"
              :max="2"
              :step="0.1"
              show-tooltip
            />
            <div class="param-hint">避免重复内容 (1.0-2.0)</div>
          </el-form-item>
        </el-form>
      </div>
      
      <!-- 自定义系统提示词 -->
      <div class="config-section">
        <h3>💬 自定义系统提示词</h3>
        <el-input
          v-model="config.system_prompt"
          type="textarea"
          :rows="6"
          placeholder="输入自定义的系统提示词，留空则使用智能体默认提示词"
        />
        <div class="param-hint">
          自定义系统提示词将覆盖智能体的默认行为设置
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="saveConfig" :loading="saving">
          保存配置
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import agentService from '../services/agentService'
import modelTrainingService from '../services/modelTrainingService'

export default {
  name: 'ModelConfigDialog',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    agentId: {
      type: String,
      required: true
    }
  },
  emits: ['update:modelValue', 'config-updated'],
  setup(props, { emit }) {
    const visible = ref(false)
    const saving = ref(false)
    const availableModels = ref([])
    const customModels = ref([])
    
    const config = reactive({
      model_name: 'qwen2.5:7b',
      temperature: 0.7,
      max_tokens: 2048,
      top_p: 0.9,
      top_k: 40,
      repeat_penalty: 1.1,
      system_prompt: '',
      use_custom_model: false,
      custom_model_id: ''
    })
    
    // 监听对话框显示状态
    watch(() => props.modelValue, (newVal) => {
      visible.value = newVal
      if (newVal) {
        loadData()
      }
    })
    
    watch(visible, (newVal) => {
      emit('update:modelValue', newVal)
    })
    
    // 加载数据
    const loadData = async () => {
      await Promise.all([
        loadAvailableModels(),
        loadCustomModels(),
        loadCurrentConfig()
      ])
    }
    
    // 加载可用模型
    const loadAvailableModels = async () => {
      try {
        const response = await agentService.getAvailableModels()
        if (response.success) {
          availableModels.value = response.data.models || []
        }
      } catch (error) {
        console.error('加载可用模型失败:', error)
      }
    }
    
    // 加载自定义模型
    const loadCustomModels = async () => {
      try {
        const response = await modelTrainingService.getTrainedModels()
        if (response.success) {
          customModels.value = response.data.items || []
        }
      } catch (error) {
        console.error('加载自定义模型失败:', error)
      }
    }
    
    // 加载当前配置
    const loadCurrentConfig = async () => {
      try {
        const response = await agentService.getModelConfig(props.agentId)
        if (response.success) {
          Object.assign(config, response.data.model_config)
        }
      } catch (error) {
        console.error('加载模型配置失败:', error)
      }
    }
    
    // 选择模型
    const selectModel = (model) => {
      config.model_name = model.name
    }
    
    // 模型类型改变
    const onModelTypeChange = (useCustom) => {
      if (!useCustom) {
        config.custom_model_id = ''
      } else {
        config.model_name = ''
      }
    }
    
    // 保存配置
    const saveConfig = async () => {
      try {
        saving.value = true
        
        const response = await agentService.updateModelConfig(props.agentId, config)
        
        if (response.success) {
          ElMessage.success('模型配置保存成功')
          emit('config-updated', config)
          handleClose()
        } else {
          ElMessage.error('保存失败: ' + response.error)
        }
      } catch (error) {
        console.error('保存模型配置失败:', error)
        ElMessage.error('保存失败，请重试')
      } finally {
        saving.value = false
      }
    }
    
    // 关闭对话框
    const handleClose = () => {
      visible.value = false
    }
    
    // 格式化文件大小
    const formatSize = (bytes) => {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }
    
    return {
      visible,
      saving,
      availableModels,
      customModels,
      config,
      selectModel,
      onModelTypeChange,
      saveConfig,
      handleClose,
      formatSize
    }
  }
}
</script>

<style scoped>
.model-config-dialog {
  max-height: 70vh;
  overflow-y: auto;
}

.config-section {
  margin-bottom: 2rem;
  padding: 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.config-section h3 {
  margin: 0 0 1rem 0;
  color: #1a202c;
  font-size: 1.1rem;
}

.models-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.model-card {
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.model-card:hover {
  border-color: #667eea;
  transform: translateY(-2px);
}

.model-card.selected {
  border-color: #667eea;
  background: #f0f4ff;
}

.model-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.model-name {
  font-weight: 600;
  color: #1a202c;
}

.recommended-badge {
  background: #10b981;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.7rem;
}

.model-desc {
  color: #64748b;
  font-size: 0.9rem;
  margin: 0.5rem 0;
}

.model-details {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.detail-item {
  color: #94a3b8;
  font-size: 0.8rem;
}

.model-use-cases {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.use-case-tag {
  background: #e0e7ff;
  color: #3730a3;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.7rem;
}

.param-hint {
  font-size: 0.8rem;
  color: #64748b;
  margin-top: 0.25rem;
}

.custom-models {
  margin-top: 1rem;
}
</style>
