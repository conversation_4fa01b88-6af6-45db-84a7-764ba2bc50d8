Text-to-Video | <PERSON>
https://tongyi.aliyun.com/wanxiang/ | Large-scale video generation model
https://github.com/Wan-Video/Wan2.1 | GPU: NVIDIA RTX 3060 or above (Recommended RTX 4060 Ti 16GB)
VRAM: ≥8GB (Running the base 1.3B model) / ≥16GB (Running the high-definition 14B model)
Storage: Reserve 50GB of SSD space for model files (Approximately 35GB) | 
1. Integrate into ComfyUI to test the effects of video generation using open-source libraries.
(1 week)
2. Design user interface and call open-source libraries to complete video production.
(1-2 weeks)
3. Test and deploy.
(1-2 weeks)

Digital Human | Wancai AI
https://ai.kezhan365.com/ | Virtual broadcaster facial animation Audio2Face + UE5 
User intent understanding Rasa + spaCy | / |
1. Deploy the deepseek large model on the server, with external applications calling local API.
(1 week)
2. Integrate and debug open-source voice, expression, etc., source codes.
(1-2 weeks)
3. Design user interface to generate digital humans (2-3 weeks)
4. Test and deploy.
(1-2 weeks)

AI Simultaneous Translation | Baidu AI Simultaneous Translation
https://tongchuan.baidu.com/ | Multilingual real-time translation Whisper + Transformers | / |
1. Deploy the deepseek large model on the server, with external applications calling local API.
(1 week)
2. Integrate and debug open-source voice, text, etc., source codes.
(1-2 weeks)
3. Design user interface to generate AI simultaneous translation.
(2-3 weeks)
4. Test and deploy.
(1-2 weeks)

Multilingual Data Management | Open-source Language Asset Management Platform
https://github.com/hanlintao/BiCorpus | / |
1. Test the open-source system (2 days)
2. Design user interface (4 days)
3. Test and deploy (3 days)