// ai-agent 模块统一导出

// 保留的核心组件
const DigitalHumanChat = () => import('./views/DigitalHumanChat.vue');

// AI智能体模块路由 - 清理后的核心路由
const aiAgentRoutes = [
  // 主要市场页面
  {
    path: 'agents',
    name: 'Agents',
    component: () => import('./views/AgentMarketplaceNew.vue'),
    meta: { title: 'AI智能体市场', module: 'ai-agent' }
  },

  // 创建智能体 - 向导模式
  {
    path: 'agents/create',
    name: 'AgentCreate',
    component: () => import('./views/AgentStudioNew.vue'),
    meta: { title: '创建智能体', module: 'ai-agent' }
  },

  // 智能体工作室 - 高级编辑器
  {
    path: 'agents/studio',
    name: 'AgentStudio',
    component: () => import('./views/AgentEditorNew.vue'),
    meta: { title: '智能体工作室', module: 'ai-agent' }
  },

  // 智能体对话 - 全屏模式
  {
    path: 'agents/chat/:id',
    name: 'AgentChat',
    component: () => import('./views/FullScreenChat.vue'),
    meta: { title: '智能体对话', module: 'ai-agent' }
  },

  // 模型训练中心
  {
    path: 'model-training',
    name: 'ModelTraining',
    component: () => import('./views/ModelTrainingNew.vue'),
    meta: { title: '模型训练中心', module: 'ai-agent' }
  },

  // 数字人对话
  {
    path: 'digital-human/chat',
    name: 'AIAgentDigitalHumanChat',
    component: DigitalHumanChat,
    meta: { title: '数字人对话', module: 'ai-agent' }
  }
];

export default aiAgentRoutes;
