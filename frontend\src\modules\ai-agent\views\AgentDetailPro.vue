<template>
  <div class="agent-detail-pro">
    <!-- 头部导航 -->
    <div class="detail-header">
      <button @click="goBack" class="back-btn">
        ← 返回市场
      </button>
      <div class="header-actions">
        <button v-if="isMyAgent" @click="editAgent" class="btn-edit">
          ✏️ 编辑智能体
        </button>
        <button v-if="isLanguageLearning" @click="manageData" class="btn-data">
          📚 管理数据
        </button>
      </div>
    </div>

    <!-- 智能体信息卡片 -->
    <div v-if="agent" class="agent-card">
      <div class="agent-hero">
        <div class="agent-avatar">
          {{ getAgentIcon(agent.agent_type) }}
        </div>
        <div class="agent-info">
          <h1>{{ agent.name }}</h1>
          <div class="agent-meta">
            <span class="category-tag">{{ getAgentCategory(agent.agent_type) }}</span>
            <span class="type-tag">{{ getAgentTypeName(agent.agent_type) }}</span>
            <span v-if="agent.created_at" class="date-tag">
              📅 {{ formatDate(agent.created_at) }}
            </span>
          </div>
          <p class="agent-description">{{ agent.description }}</p>
        </div>
      </div>

      <!-- 功能特性 -->
      <div class="agent-features">
        <h3>🌟 功能特性</h3>
        <div class="features-grid">
          <div 
            v-for="feature in getAgentFeatures(agent.agent_type)" 
            :key="feature"
            class="feature-item"
          >
            <span class="feature-icon">✨</span>
            <span class="feature-text">{{ feature }}</span>
          </div>
        </div>
      </div>

      <!-- 系统提示词 -->
      <div v-if="agent.system_prompt" class="agent-prompt">
        <h3>🤖 系统提示词</h3>
        <div class="prompt-content">
          {{ agent.system_prompt }}
        </div>
      </div>

      <!-- 语言学习设置 -->
      <div v-if="isLanguageLearning && agent.learning_settings" class="learning-settings">
        <h3>🌍 学习设置</h3>
        <div class="settings-grid">
          <div class="setting-item">
            <span class="setting-label">学习语言:</span>
            <span class="setting-value">{{ getLanguageName(agent.learning_settings.language) }}</span>
          </div>
          <div class="setting-item">
            <span class="setting-label">术语数量:</span>
            <span class="setting-value">{{ getTermCount(agent.id) }} 个</span>
          </div>
          <div class="setting-item">
            <span class="setting-label">文档数量:</span>
            <span class="setting-value">{{ getDocumentCount(agent.id) }} 个</span>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <button @click="tryAgent" class="btn-secondary">
          🎯 试用智能体
        </button>
        <button @click="openModelConfig" class="btn-edit">
          🤖 模型配置
        </button>
        <button @click="useAgent" class="btn-primary">
          🚀 开始使用
        </button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-else-if="loading" class="loading-state">
      <div class="spinner"></div>
      <p>正在加载智能体信息...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-state">
      <div class="error-icon">❌</div>
      <h3>智能体不存在</h3>
      <p>未找到指定的智能体，可能已被删除或ID错误</p>
      <button @click="goBack" class="btn-primary">返回市场</button>
    </div>

    <!-- 模型配置对话框 -->
    <ModelConfigDialog
      v-model="showModelConfig"
      :agent-id="agentId"
      @config-updated="onModelConfigUpdated"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import { getAgentTypeConfig, getAllAgentTypes } from '@/config/agentTypes.js'
import agentService from '../services/agentService.js'
import ModelConfigDialog from '../components/ModelConfigDialog.vue'

export default {
  name: 'AgentDetailPro',
  components: {
    ModelConfigDialog
  },

  setup() {
    const router = useRouter()
    const route = useRoute()
    
    // 状态
    const loading = ref(true)
    const agent = ref(null)
    const showModelConfig = ref(false)
    
    // 计算属性
    const isMyAgent = computed(() => {
      return agent.value && agent.value.id && agent.value.id.startsWith('local-')
    })
    
    const isLanguageLearning = computed(() => {
      return agent.value && (
        agent.value.agent_type === 'language-learning' || 
        agent.value.agent_type === 'language_learning'
      )
    })
    
    // 方法
    const loadAgent = async () => {
      try {
        loading.value = true
        const agentId = route.query.id
        const isPreview = route.query.preview === 'true'

        if (!agentId) {
          message.error('未指定智能体ID')
          return
        }

        console.log('加载智能体详情:', agentId, '预览模式:', isPreview)

        // 如果是预览模式，从sessionStorage加载数据
        if (isPreview) {
          const previewData = sessionStorage.getItem('preview_agent_data')
          if (previewData) {
            agent.value = JSON.parse(previewData)
            console.log('从预览数据加载智能体:', agent.value.name)
            return
          }
        }
        
        // 如果是本地智能体
        if (agentId.startsWith('local-')) {
          const localAgents = JSON.parse(localStorage.getItem('local_agents') || '[]')
          const foundAgent = localAgents.find(a => a.id === agentId)
          
          if (foundAgent) {
            agent.value = foundAgent
            console.log('从本地加载智能体:', foundAgent.name)
            return
          }
        }
        
        // 从API加载
        const response = await agentService.getAgentDetail(agentId)
        console.log('API响应数据:', response)

        // 检查响应结构：可能是 response.agent 或 response.data
        const agentData = response?.agent || response?.data

        if (response && response.success && agentData) {
          agent.value = agentData
          console.log('从API加载智能体:', agentData.name)
        } else {
          console.warn('未找到智能体或API返回失败')
        }
      } catch (error) {
        console.error('加载智能体失败:', error)
        message.error('加载智能体失败')
      } finally {
        loading.value = false
      }
    }
    
    const getAgentIcon = (agentType) => {
      const typeConfig = getAgentTypeConfig(agentType)
      return typeConfig.icon || '🤖'
    }
    
    const getAgentCategory = (agentType) => {
      const typeConfig = getAgentTypeConfig(agentType)
      return typeConfig.category || '通用工具'
    }
    
    const getAgentTypeName = (agentType) => {
      const typeConfig = getAgentTypeConfig(agentType)
      return typeConfig.name || '通用助手'
    }
    
    const getAgentFeatures = (agentType) => {
      const typeConfig = getAgentTypeConfig(agentType)
      return typeConfig.features || []
    }
    
    const getLanguageName = (languageCode) => {
      const languages = {
        'english': '英语',
        'japanese': '日语',
        'french': '法语',
        'german': '德语',
        'spanish': '西班牙语'
      }
      return languages[languageCode] || languageCode
    }
    
    const getTermCount = (agentId) => {
      const terms = localStorage.getItem(`agent_terms_${agentId}`)
      return terms ? JSON.parse(terms).length : 0
    }
    
    const getDocumentCount = (agentId) => {
      const documents = localStorage.getItem(`agent_documents_${agentId}`)
      return documents ? JSON.parse(documents).length : 0
    }
    
    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('zh-CN')
    }
    
    const editAgent = () => {
      router.push(`/utilities/daily/agent-editor?id=${agent.value.id}`)
    }
    
    const manageData = () => {
      router.push(`/utilities/daily/language-learning-data?agent_id=${agent.value.id}`)
    }
    
    const tryAgent = () => {
      router.push(`/utilities/daily/unified-chat?agent_id=${agent.value.id}&demo=1`)
    }
    
    const useAgent = () => {
      router.push(`/utilities/daily/unified-chat?agent_id=${agent.value.id}`)
    }
    
    const goBack = () => {
      router.push('/utilities/daily/agent-marketplace')
    }

    // 模型配置相关方法
    const openModelConfig = () => {
      showModelConfig.value = true
    }

    const onModelConfigUpdated = (config) => {
      message.success('模型配置已更新')
      console.log('模型配置已更新:', config)
    }
    
    // 生命周期
    onMounted(() => {
      loadAgent()
    })
    
    return {
      loading,
      agent,
      showModelConfig,
      isMyAgent,
      isLanguageLearning,
      agentId: computed(() => route.query.id),
      getAgentIcon,
      getAgentCategory,
      getAgentTypeName,
      getAgentFeatures,
      getLanguageName,
      getTermCount,
      getDocumentCount,
      formatDate,
      editAgent,
      manageData,
      tryAgent,
      useAgent,
      goBack,
      openModelConfig,
      onModelConfigUpdated
    }
  }
}
</script>

<style scoped>
.agent-detail-pro {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.back-btn {
  padding: 10px 20px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
}

.back-btn:hover {
  background: #e2e8f0;
  color: #475569;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.agent-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.agent-hero {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 32px;
  display: flex;
  align-items: center;
  gap: 24px;
}

.agent-avatar {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  backdrop-filter: blur(10px);
}

.agent-info h1 {
  margin: 0 0 12px 0;
  font-size: 28px;
  font-weight: 600;
}

.agent-meta {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.category-tag, .type-tag, .date-tag {
  padding: 4px 12px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.agent-description {
  margin: 0;
  font-size: 16px;
  line-height: 1.6;
  opacity: 0.9;
}

.agent-features, .agent-prompt, .learning-settings {
  padding: 24px 32px;
  border-bottom: 1px solid #f1f5f9;
}

.agent-features h3, .agent-prompt h3, .learning-settings h3 {
  margin: 0 0 16px 0;
  color: #1e293b;
  font-size: 18px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.feature-icon {
  font-size: 16px;
}

.feature-text {
  font-size: 14px;
  color: #374151;
}

.prompt-content {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  font-family: monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #374151;
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.setting-label {
  font-size: 14px;
  color: #64748b;
}

.setting-value {
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
}

.action-buttons {
  padding: 32px;
  display: flex;
  gap: 16px;
  justify-content: center;
}

.btn-primary, .btn-secondary, .btn-edit, .btn-data {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
  background: white;
  color: #667eea;
  border: 1px solid #667eea;
}

.btn-secondary:hover {
  background: #667eea;
  color: white;
}

.btn-edit {
  background: #f59e0b;
  color: white;
}

.btn-data {
  background: #8b5cf6;
  color: white;
}

.loading-state, .error-state {
  text-align: center;
  padding: 60px 20px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-state h3 {
  margin: 0 0 8px 0;
  color: #1e293b;
}

.error-state p {
  margin: 0 0 24px 0;
  color: #64748b;
}

@media (max-width: 768px) {
  .agent-detail-pro {
    padding: 15px;
  }
  
  .detail-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .agent-hero {
    flex-direction: column;
    text-align: center;
    padding: 24px;
  }
  
  .agent-meta {
    justify-content: center;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .features-grid, .settings-grid {
    grid-template-columns: 1fr;
  }
}
</style>
