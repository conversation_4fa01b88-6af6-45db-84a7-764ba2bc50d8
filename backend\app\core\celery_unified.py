"""
统一的Celery配置
支持所有模块的异步任务需求
"""

import os
import logging
from celery import Celery
from app.core.config import settings

logger = logging.getLogger(__name__)

# 检查Redis是否可用
def check_redis_available():
    """检查Redis连接是否可用"""
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        return True
    except Exception as e:
        logger.warning(f"Redis连接失败: {e}")
        return False

# 获取broker和backend URL
def get_broker_urls():
    """获取broker和backend URL"""
    redis_available = check_redis_available()
    
    if redis_available:
        # 使用Redis
        redis_url = settings.REDIS_URL or "redis://localhost:6379/0"
        logger.info(f"使用Redis作为broker: {redis_url}")
        return redis_url, redis_url
    else:
        # 使用内存broker（仅用于开发和测试）
        logger.warning("Redis不可用，使用内存broker（仅用于开发）")
        return "memory://", "cache+memory://"

# 获取broker和backend URL
broker_url, backend_url = get_broker_urls()

# 创建Celery应用
celery_app = Celery(
    "ai_platform",
    broker=broker_url,
    backend=backend_url
)

# 手动导入任务模块以确保注册
try:
    from app.tasks import digital_human
    from app.tasks import translation
    from app.tasks import document_translation_tasks
    from app.tasks import video_generation
    from app.tasks import image_processing
    from app.tasks import audio_processing
    from app.tasks import monitoring
    from app.tasks import opinion_analysis_tasks
    logger.info("统一任务模块导入成功")
except Exception as e:
    logger.error(f"任务模块导入失败: {e}")

# 可选：导入兼容的旧任务模块（如果需要）
try:
    # 只导入已修复的任务模块
    # from app.tasks import wanx_video_tasks_real  # 如果需要
    pass
except Exception as e:
    logger.warning(f"旧任务模块导入失败（可忽略）: {e}")

# 设置include列表（作为备用）
celery_app.conf.include = [
    "app.tasks.digital_human",
    "app.tasks.musetalk_task",
    "app.tasks.translation",
    "app.tasks.translation_tasks",  # 添加翻译任务模块
    "app.tasks.document_translation_tasks",  # 添加文档翻译任务模块
    "app.tasks.video_generation",
    "app.tasks.image_processing",
    "app.tasks.audio_processing",
    "app.tasks.monitoring",
    "app.tasks.opinion_analysis_tasks",
    # 兼容旧任务模块（如果需要）
    # "app.tasks.wanx_video_tasks",
    # "app.tasks.video_tasks",
    # "app.tasks.image_tasks",
]

# Celery配置
celery_app.conf.update(
    # 基础配置
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    result_accept_content=["json"],
    timezone="UTC",
    enable_utc=True,
    
    # 任务配置
    task_track_started=True,
    task_ignore_result=False,
    task_time_limit=1800,  # 30分钟硬超时
    task_soft_time_limit=1500,  # 25分钟软超时
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    
    # Windows兼容性
    worker_pool='solo' if os.name == 'nt' else 'prefork',
    worker_concurrency=1 if os.name == 'nt' else 4,
    
    # 队列路由配置
    task_routes={
        'app.tasks.digital_human.*': {'queue': 'digital_human'},
        'app.tasks.translation.*': {'queue': 'translation'},
        'app.tasks.video_generation.*': {'queue': 'video'},
        'app.tasks.image_processing.*': {'queue': 'image'},
        'app.tasks.audio_processing.*': {'queue': 'audio'},
        'app.tasks.monitoring.*': {'queue': 'monitoring'},
    },
    
    # 队列配置
    task_default_queue='default',
    task_default_exchange='default',
    task_default_exchange_type='direct',
    task_default_routing_key='default',
    
    # 结果过期时间
    result_expires=3600,  # 1小时
    
    # 错误处理
    task_reject_on_worker_lost=True,

    # 监控配置
    worker_send_task_events=True,
    task_send_sent_event=True,
)

# 日志配置
logger.info("Celery应用配置完成")
logger.info(f"Broker: {broker_url}")
logger.info(f"Backend: {backend_url}")
logger.info(f"Worker pool: {'solo' if os.name == 'nt' else 'prefork'}")

# 导入任务基类
from ..tasks.base import BaseTask

# 为了兼容性，创建 DatabaseTask 别名
DatabaseTask = BaseTask

# 导出celery应用
__all__ = ['celery_app', 'DatabaseTask', 'BaseTask']
