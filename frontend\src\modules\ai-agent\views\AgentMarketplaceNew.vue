<template>
  <div class="agent-marketplace-new">
    <!-- 英雄区域 -->
    <div class="hero-section">
      <div class="hero-background">
        <div class="hero-overlay"></div>
        <div class="hero-content">
          <div class="hero-text">
            <h1 class="hero-title">🤖 智能体市场</h1>
            <p class="hero-subtitle">发现、创建和管理您的专属AI智能体</p>
            <div class="hero-stats">
              <div class="stat-item">
                <div class="stat-number">{{ totalAgents }}</div>
                <div class="stat-label">智能体总数</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">{{ activeUsers }}</div>
                <div class="stat-label">活跃用户</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">{{ totalChats }}</div>
                <div class="stat-label">对话次数</div>
              </div>
            </div>
          </div>
          
          <div class="hero-actions">
            <el-dropdown @command="handleCreateCommand" trigger="click" class="create-dropdown">
              <el-button type="primary" size="large" class="primary-action">
                <el-icon><plus /></el-icon>
                创建智能体
                <el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="new">
                    <span class="dropdown-icon">🧙‍♂️</span>
                    向导模式（推荐）
                  </el-dropdown-item>
                  <el-dropdown-item command="advanced">
                    <span class="dropdown-icon">⚙️</span>
                    高级模式
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <el-button size="large" class="secondary-action" @click="scrollToAgents">
              <el-icon><view /></el-icon>
              浏览智能体
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作区域 -->
    <div class="quick-actions-section">
      <div class="container">
        <div class="quick-actions-grid">
          <div class="action-card" @click="handleCreateCommand('new')">
            <div class="action-icon">🚀</div>
            <div class="action-title">快速创建</div>
            <div class="action-desc">使用向导模式快速创建智能体</div>
          </div>
          
          <div class="action-card" @click="openModelTraining">
            <div class="action-icon">🎓</div>
            <div class="action-title">模型训练</div>
            <div class="action-desc">训练专业的智能体模型</div>
          </div>
          
          <div class="action-card" @click="viewTemplates">
            <div class="action-icon">📋</div>
            <div class="action-title">模板库</div>
            <div class="action-desc">从预设模板快速开始</div>
          </div>
          
          <div class="action-card" @click="viewDocumentation">
            <div class="action-icon">📚</div>
            <div class="action-title">使用指南</div>
            <div class="action-desc">学习如何创建和使用智能体</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 智能体分类 -->
    <div class="agents-section" ref="agentsSection">
      <div class="container">
        <div class="section-header">
          <h2>智能体分类</h2>
          <p>按类型浏览不同功能的智能体</p>
        </div>

        <!-- 分类标签 -->
        <div class="category-tabs">
          <div
            :class="['category-tab special-tab', { active: activeCategory === 'my' }]"
            @click="activeCategory = 'my'"
          >
            <span class="tab-icon">👤</span>
            <span class="tab-name">我的智能体</span>
            <span class="tab-count">({{ myAgents.length }})</span>
          </div>

          <div
            v-for="category in categories"
            :key="category.id"
            :class="['category-tab', { active: activeCategory === category.id }]"
            @click="activeCategory = category.id"
          >
            <span class="tab-icon">{{ category.icon }}</span>
            <span class="tab-name">{{ category.name }}</span>
            <span class="tab-count">({{ category.count }})</span>
          </div>
        </div>

        <!-- 智能体网格 -->
        <div class="agents-grid">
          <div
            v-for="agent in filteredAgents"
            :key="agent.id"
            class="agent-card-new"
          >
            <div class="agent-header">
              <div class="agent-avatar">{{ agent.avatar }}</div>
              <div class="agent-status" :class="agent.status">
                <span class="status-dot"></span>
                {{ getStatusText(agent.status) }}
              </div>
            </div>
            
            <div class="agent-info" @click="viewAgentDetail(agent)">
              <h3 class="agent-name">{{ agent.name }}</h3>
              <p class="agent-description">{{ agent.description }}</p>

              <div class="agent-tags">
                <span v-for="tag in agent.tags" :key="tag" class="agent-tag">
                  {{ tag }}
                </span>
              </div>

              <div class="agent-stats">
                <div class="stat">
                  <el-icon><chat-line-round /></el-icon>
                  {{ agent.chatCount || 0 }}
                </div>
                <div class="stat">
                  <el-icon><star /></el-icon>
                  {{ agent.rating || 0 }}
                </div>
                <div class="stat">
                  <el-icon><user /></el-icon>
                  {{ agent.userCount || 0 }}
                </div>
              </div>
            </div>
            
            <div class="agent-actions">
              <el-dropdown @command="(cmd) => handleAgentAction(cmd, agent)" trigger="click">
                <el-button type="primary" size="small">
                  开始对话
                  <el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="chat">💬 文字对话</el-dropdown-item>
                    <el-dropdown-item command="digital-human">🤖 数字人对话</el-dropdown-item>
                    <el-dropdown-item divided command="edit">✏️ 编辑智能体</el-dropdown-item>
                    <el-dropdown-item command="knowledge">📚 管理知识库</el-dropdown-item>
                    <el-dropdown-item divided command="clone">📋 克隆智能体</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>

        <!-- 加载更多 -->
        <div class="load-more-section" v-if="hasMore">
          <el-button @click="loadMore" :loading="loading" size="large">
            加载更多智能体
          </el-button>
        </div>
      </div>
    </div>

    <!-- 最近使用 -->
    <div class="recent-section" v-if="recentAgents.length > 0">
      <div class="container">
        <div class="section-header">
          <h2>最近使用</h2>
          <p>您最近互动过的智能体</p>
        </div>
        
        <div class="recent-agents-grid">
          <div 
            v-for="agent in recentAgents" 
            :key="agent.id"
            class="recent-agent-card"
            @click="continueChat(agent)"
          >
            <div class="recent-avatar">{{ agent.avatar }}</div>
            <div class="recent-info">
              <div class="recent-name">{{ agent.name }}</div>
              <div class="recent-time">{{ formatTime(agent.lastUsed) }}</div>
            </div>
            <div class="recent-action">
              <el-icon><right /></el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 帮助和文档 -->
    <div class="help-section">
      <div class="container">
        <div class="help-content">
          <div class="help-text">
            <h2>需要帮助？</h2>
            <p>查看我们的使用指南，学习如何创建和使用智能体</p>
          </div>
          <div class="help-actions">
            <el-button @click="viewDocumentation" type="primary">查看文档</el-button>
            <el-button @click="contactSupport">联系支持</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, ArrowDown, View, ChatLineRound, Star, User, Right } from '@element-plus/icons-vue'
import trueAgentService from '../services/trueAgentService.js'

export default {
  name: 'AgentMarketplaceNew',
  components: {
    Plus,
    ArrowDown,
    View,
    ChatLineRound,
    Star,
    User,
    Right
  },
  
  setup() {
    const router = useRouter()
    const route = useRoute()
    const agentsSection = ref(null)
    const loading = ref(false)
    const hasMore = ref(true)
    const activeCategory = ref('all')

    // 统计数据
    const totalAgents = ref(156)
    const activeUsers = ref(2340)
    const totalChats = ref(15600)

    // 分类数据
    const categories = ref([])

    // 智能体数据
    const agents = ref([])

    // 我的智能体数据
    const myAgents = ref([])

    // 最近使用的智能体
    const recentAgents = computed(() => {
      return agents.value
        .filter(agent => agent.lastUsed)
        .sort((a, b) => new Date(b.lastUsed) - new Date(a.lastUsed))
        .slice(0, 4)
    })

    // 过滤后的智能体
    const filteredAgents = computed(() => {
      if (activeCategory.value === 'my') {
        return myAgents.value
      }
      if (activeCategory.value === 'all') {
        return agents.value
      }
      return agents.value.filter(agent => agent.category === activeCategory.value)
    })

    // 数据加载方法
    const loadCategories = async () => {
      try {
        // 使用固定的分类数据
        categories.value = [
          { id: 'all', name: '全部', icon: '🌟', count: 0 },
          { id: 'assistant', name: '助手', icon: '🤖', count: 0 },
          { id: 'teacher', name: '教师', icon: '👨‍🏫', count: 0 },
          { id: 'customer-service', name: '客服', icon: '🎧', count: 0 },
          { id: 'creative', name: '创意', icon: '🎨', count: 0 },
          { id: 'analyst', name: '分析师', icon: '📊', count: 0 }
        ]
      } catch (error) {
        console.error('加载分类失败:', error)
      }
    }

    const loadAgents = async () => {
      try {
        loading.value = true
        const params = {
          page: 1,
          size: 20,
          agent_type: activeCategory.value === 'all' ? undefined : activeCategory.value
        }

        const response = await trueAgentService.getAgents(params)

        if (response.success && response.agents) {
          agents.value = response.agents.map(agent => ({
            ...agent,
            avatar: getAgentIcon(agent.agent_type || 'assistant'),
            category: agent.agent_type || 'assistant',
            status: 'online',
            tags: agent.tags || [agent.agent_type || '通用'],
            chatCount: agent.usage_count || 0,
            rating: agent.rating || 4.5,
            userCount: Math.floor((agent.usage_count || 0) / 10),
            lastUsed: agent.last_used ? new Date(agent.last_used) : null
          }))
          totalAgents.value = response.pagination?.total || response.agents.length

          // 更新分类计数
          updateCategoryCounts()
        }
      } catch (error) {
        console.error('加载智能体失败:', error)
        // 使用模拟数据作为备用
        loadMockAgents()
      } finally {
        loading.value = false
      }
    }

    const loadMyAgents = async () => {
      try {
        // 获取当前用户创建的智能体
        const params = {
          page: 1,
          size: 50,
          created_by_me: true
        }

        const response = await trueAgentService.getAgents(params)

        if (response.success && response.agents) {
          myAgents.value = response.agents.map(agent => ({
            ...agent,
            avatar: getAgentIcon(agent.agent_type || 'assistant'),
            category: agent.agent_type || 'assistant',
            status: 'online',
            tags: agent.tags || [agent.agent_type || '通用'],
            chatCount: agent.usage_count || 0,
            rating: agent.rating || 5.0,
            userCount: Math.floor((agent.usage_count || 0) / 10),
            lastUsed: agent.last_used ? new Date(agent.last_used) : null
          }))
        }
      } catch (error) {
        console.error('加载我的智能体失败:', error)
      }
    }

    // 辅助方法
    const getAgentIcon = (agentType) => {
      const iconMap = {
        'assistant': '🤖',
        'teacher': '👨‍🏫',
        'customer-service': '🎧',
        'creative': '✍️',
        'analyst': '📊',
        'programming': '💻',
        'writing': '📝',
        'education': '📚',
        'business': '💼'
      }
      return iconMap[agentType] || '🤖'
    }

    const updateCategoryCounts = () => {
      categories.value.forEach(category => {
        if (category.id === 'all') {
          category.count = agents.value.length
        } else {
          category.count = agents.value.filter(agent => agent.category === category.id).length
        }
      })
    }

    const loadMockAgents = () => {
      // 合并本地智能体和模拟数据
      const localAgents = JSON.parse(localStorage.getItem('local_agents') || '[]')

      const mockAgents = [
        {
          id: 'e55f5e84-6d8b-4265-8e55-728bdb0d2455',
          name: '语言学习助手',
          description: '专业的语言学习和教学助手，提供个性化的语言学习指导',
          avatar: '�',
          category: 'education',
          status: 'online',
          tags: ['语言学习', '英语', '教学'],
          chatCount: 1850,
          rating: 4.5,
          userCount: 420,
          lastUsed: new Date(Date.now() - 1 * 60 * 60 * 1000),
          agent_type: 'language_tutor',
          capabilities: ['英语对话', '语法检查', '发音指导', '词汇教学'],
          knowledge: ['英语语法', '日常对话', '商务英语', '学习方法']
        },
        {
          id: '1',
          name: '数学老师小明',
          description: '专业的数学教师，擅长解答各种数学问题，提供个性化学习指导',
          avatar: '👨‍🏫',
          category: 'teacher',
          status: 'online',
          tags: ['数学', '教育', '个性化'],
          chatCount: 1250,
          rating: 4.8,
          userCount: 320,
          lastUsed: new Date(Date.now() - 2 * 60 * 60 * 1000),
          agent_type: 'teacher',
          capabilities: ['数学教学', '问题解答', '学习指导'],
          knowledge: ['数学基础', '高等数学', '应用数学']
        },
        {
          id: '2',
          name: '客服小助手',
          description: '24小时在线客服，快速解决您的问题，提供专业的售后支持',
          avatar: '🎧',
          category: 'customer-service',
          status: 'online',
          tags: ['客服', '24小时', '专业'],
          chatCount: 2100,
          rating: 4.9,
          userCount: 580,
          lastUsed: new Date(Date.now() - 30 * 60 * 1000),
          agent_type: 'customer-service',
          capabilities: ['问题解答', '售后支持', '用户引导'],
          knowledge: ['产品知识', '服务流程', '常见问题']
        }
      ]

      // 合并本地智能体和模拟数据
      agents.value = [...localAgents, ...mockAgents]
      totalAgents.value = agents.value.length
      updateCategoryCounts()
    }

    // 方法
    const handleCreateCommand = (command) => {
      if (command === 'new') {
        router.push('/agents/create')
      } else if (command === 'advanced') {
        router.push('/agents/studio')
      }
    }

    const openModelTraining = () => {
      window.open('/model-training', '_blank')
    }

    const viewTemplates = () => {
      ElMessage.info('模板库功能开发中...')
    }

    const viewDocumentation = () => {
      ElMessage.info('文档功能开发中...')
    }

    const contactSupport = () => {
      ElMessage.info('支持功能开发中...')
    }

    const scrollToAgents = () => {
      agentsSection.value?.scrollIntoView({ behavior: 'smooth' })
    }

    const viewAgent = (agent) => {
      // 直接开始对话
      chatWithAgent(agent)
    }

    const viewAgentDetail = (agent) => {
      // 显示智能体详情或直接开始对话
      chatWithAgent(agent)
    }

    const chatWithAgent = (agent) => {
      const chatUrl = `/agents/chat/${agent.id}`
      router.push(chatUrl)
    }

    const chatWithDigitalHuman = (agent) => {
      const chatUrl = `/digital-human/chat?agent_id=${agent.id}`
      router.push(chatUrl)
    }

    const editAgent = (agent) => {
      router.push({
        path: '/agents/studio',
        query: {
          mode: 'edit',
          agent_id: agent.id
        }
      })
    }

    const manageKnowledge = (agent) => {
      router.push({
        path: '/agents/studio',
        query: {
          mode: 'edit',
          agent_id: agent.id,
          tab: 'knowledge'
        }
      })
    }

    const cloneAgent = async (agent) => {
      try {
        // 创建克隆的智能体数据
        const clonedAgent = {
          id: 'local-' + Date.now(),
          name: `${agent.name} (副本)`,
          description: agent.description,
          avatar: agent.avatar,
          category: agent.category,
          tags: [...(agent.tags || [])],
          agent_type: agent.agent_type,
          system_prompt: agent.system_prompt,
          capabilities: [...(agent.capabilities || [])],
          knowledge: [...(agent.knowledge || [])],
          created_at: new Date().toISOString(),
          author: '本地用户',
          status: 'draft'
        }

        // 保存到本地存储
        const localAgents = JSON.parse(localStorage.getItem('local_agents') || '[]')
        localAgents.push(clonedAgent)
        localStorage.setItem('local_agents', JSON.stringify(localAgents))

        ElMessage.success(`智能体 "${agent.name}" 已克隆成功！`)

        // 刷新智能体列表
        loadMockAgents()

        // 可选：跳转到编辑页面
        const shouldEdit = await ElMessageBox.confirm(
          '是否立即编辑克隆的智能体？',
          '克隆成功',
          {
            confirmButtonText: '立即编辑',
            cancelButtonText: '稍后编辑',
            type: 'success'
          }
        ).catch(() => false)

        if (shouldEdit) {
          router.push({
            path: '/agents/studio',
            query: {
              mode: 'edit',
              agent_id: clonedAgent.id
            }
          })
        }

      } catch (error) {
        console.error('克隆智能体失败:', error)
        ElMessage.error('克隆智能体失败，请重试')
      }
    }

    const handleAgentAction = (command, agent) => {
      switch (command) {
        case 'chat':
          chatWithAgent(agent)
          break
        case 'voice':
          ElMessage.info('语音对话功能开发中...')
          break
        case 'digital-human':
          chatWithDigitalHuman(agent)
          break
        case 'edit':
          editAgent(agent)
          break
        case 'knowledge':
          manageKnowledge(agent)
          break
        case 'clone':
          cloneAgent(agent)
          break
      }
    }

    const continueChat = (agent) => {
      chatWithAgent(agent)
    }

    const loadMore = () => {
      loading.value = true
      // 模拟加载更多
      setTimeout(() => {
        loading.value = false
        hasMore.value = false
        ElMessage.success('已加载全部智能体')
      }, 1000)
    }

    const getStatusText = (status) => {
      const statusMap = {
        online: '在线',
        busy: '忙碌',
        offline: '离线'
      }
      return statusMap[status] || '未知'
    }

    const formatTime = (time) => {
      const now = new Date()
      const diff = now - new Date(time)
      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))

      if (minutes < 60) {
        return `${minutes}分钟前`
      } else if (hours < 24) {
        return `${hours}小时前`
      } else {
        return `${days}天前`
      }
    }

    // 监听路由参数变化
    watch(() => route.query.tab, (newTab) => {
      if (newTab === 'my') {
        activeCategory.value = 'my'
      }
    }, { immediate: true })

    onMounted(async () => {
      // 检查URL参数
      if (route.query.tab === 'my') {
        activeCategory.value = 'my'
      }

      // 加载数据
      await Promise.all([
        loadCategories(),
        loadAgents(),
        loadMyAgents()
      ])
      console.log('智能体市场加载完成')
    })

    return {
      agentsSection,
      loading,
      hasMore,
      activeCategory,
      totalAgents,
      activeUsers,
      totalChats,
      categories,
      agents,
      myAgents,
      recentAgents,
      filteredAgents,
      loadCategories,
      loadAgents,
      loadMyAgents,
      getAgentIcon,
      updateCategoryCounts,
      loadMockAgents,
      chatWithAgent,
      chatWithDigitalHuman,
      editAgent,
      manageKnowledge,
      cloneAgent,
      handleCreateCommand,
      openModelTraining,
      viewTemplates,
      viewDocumentation,
      contactSupport,
      scrollToAgents,
      viewAgent,
      viewAgentDetail,
      handleAgentAction,
      continueChat,
      loadMore,
      getStatusText,
      formatTime
    }
  }
}
</script>

<style scoped>
.agent-marketplace-new {
  min-height: 100vh;
  background: #f8fafc;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 英雄区域 */
.hero-section {
  position: relative;
  height: 60vh;
  min-height: 500px;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.1);
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 0 20px;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  font-weight: 300;
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  margin-bottom: 3rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.8;
  font-weight: 400;
}

.hero-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.primary-action {
  height: 56px;
  padding: 0 32px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.primary-action:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.secondary-action {
  height: 56px;
  padding: 0 32px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: white;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.secondary-action:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.dropdown-icon {
  margin-right: 8px;
  font-size: 16px;
}

/* 快速操作区域 */
.quick-actions-section {
  padding: 4rem 0;
  background: white;
  margin-top: -2rem;
  position: relative;
  z-index: 3;
  border-radius: 2rem 2rem 0 0;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.action-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.action-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.action-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.action-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 0.5rem;
}

.action-desc {
  color: #64748b;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* 智能体区域 */
.agents-section {
  padding: 4rem 0;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 1rem;
}

.section-header p {
  font-size: 1.1rem;
  color: #64748b;
  max-width: 600px;
  margin: 0 auto;
}

/* 分类标签 */
.category-tabs {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.category-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  background: white;
  border: 2px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  color: #64748b;
}

.category-tab:hover {
  border-color: #667eea;
  color: #667eea;
}

.category-tab.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.category-tab.special-tab {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: #667eea;
  font-weight: 600;
}

.category-tab.special-tab:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  color: white;
}

.category-tab.special-tab.active {
  background: linear-gradient(135deg, #4c63d2 0%, #5e3a7e 100%);
  color: white;
}

.tab-icon {
  font-size: 1.2rem;
}

.tab-count {
  font-size: 0.8rem;
  opacity: 0.7;
}

/* 智能体网格 */
.agents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.agent-card-new {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.agent-card-new:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.agent-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.agent-avatar {
  font-size: 2.5rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border-radius: 12px;
}

.agent-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.agent-status.online {
  background: #dcfce7;
  color: #166534;
}

.agent-status.busy {
  background: #fef3c7;
  color: #92400e;
}

.agent-status.offline {
  background: #f1f5f9;
  color: #475569;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
}

.agent-info {
  margin-bottom: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.agent-info:hover {
  background: #f8fafc;
}

.agent-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 0.5rem;
}

.agent-description {
  color: #64748b;
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.agent-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.agent-tag {
  background: #e0e7ff;
  color: #3730a3;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.agent-stats {
  display: flex;
  gap: 1rem;
}

.stat {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #64748b;
  font-size: 0.8rem;
}

.agent-actions {
  display: flex;
  justify-content: center;
}

/* 加载更多 */
.load-more-section {
  text-align: center;
  margin-top: 2rem;
}

/* 最近使用 */
.recent-section {
  padding: 4rem 0;
  background: white;
}

.recent-agents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
}

.recent-agent-card {
  display: flex;
  align-items: center;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.recent-agent-card:hover {
  background: #e2e8f0;
  transform: translateX(4px);
}

.recent-avatar {
  font-size: 2rem;
  margin-right: 1rem;
}

.recent-info {
  flex: 1;
}

.recent-name {
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 0.25rem;
}

.recent-time {
  font-size: 0.8rem;
  color: #64748b;
}

.recent-action {
  color: #667eea;
}

/* 帮助区域 */
.help-section {
  padding: 4rem 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.help-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
}

.help-text h2 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.help-text p {
  font-size: 1.1rem;
  opacity: 0.9;
}

.help-actions {
  display: flex;
  gap: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-stats {
    gap: 1.5rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .quick-actions-grid {
    grid-template-columns: 1fr;
  }

  .category-tabs {
    justify-content: flex-start;
    overflow-x: auto;
    padding-bottom: 0.5rem;
  }

  .agents-grid {
    grid-template-columns: 1fr;
  }

  .help-content {
    text-align: center;
  }
}
</style>
