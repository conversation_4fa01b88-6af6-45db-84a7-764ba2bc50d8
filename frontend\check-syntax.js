#!/usr/bin/env node

/**
 * 简单的语法检查脚本
 */

const fs = require('fs');
const path = require('path');

function checkBrackets(content) {
  const brackets = {
    '(': ')',
    '[': ']',
    '{': '}'
  };
  
  const stack = [];
  const lines = content.split('\n');
  
  for (let lineNum = 0; lineNum < lines.length; lineNum++) {
    const line = lines[lineNum];
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      
      // 跳过字符串内容
      if (char === '"' || char === "'" || char === '`') {
        let quote = char;
        i++;
        while (i < line.length && line[i] !== quote) {
          if (line[i] === '\\') i++; // 跳过转义字符
          i++;
        }
        continue;
      }
      
      // 跳过注释
      if (char === '/' && i + 1 < line.length) {
        if (line[i + 1] === '/') break; // 单行注释
        if (line[i + 1] === '*') {
          // 多行注释开始
          i += 2;
          while (i < line.length - 1) {
            if (line[i] === '*' && line[i + 1] === '/') {
              i++;
              break;
            }
            i++;
          }
          continue;
        }
      }
      
      if (brackets[char]) {
        stack.push({ char, line: lineNum + 1, col: i + 1 });
      } else if (Object.values(brackets).includes(char)) {
        if (stack.length === 0) {
          console.log(`❌ 第 ${lineNum + 1} 行第 ${i + 1} 列: 多余的 '${char}'`);
          return false;
        }
        
        const last = stack.pop();
        if (brackets[last.char] !== char) {
          console.log(`❌ 第 ${lineNum + 1} 行第 ${i + 1} 列: 期望 '${brackets[last.char]}' 但找到 '${char}'`);
          console.log(`   对应的开括号在第 ${last.line} 行第 ${last.col} 列`);
          return false;
        }
      }
    }
  }
  
  if (stack.length > 0) {
    console.log(`❌ 未闭合的括号:`);
    stack.forEach(item => {
      console.log(`   '${item.char}' 在第 ${item.line} 行第 ${item.col} 列`);
    });
    return false;
  }
  
  return true;
}

function checkSyntax() {
  const filePath = path.join(__dirname, 'src/modules/ai-agent/views/AgentEditorNew.vue');
  
  console.log('🔍 检查语法错误...');
  console.log(`📁 文件: ${filePath}`);
  
  if (!fs.existsSync(filePath)) {
    console.log('❌ 文件不存在');
    return;
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  
  // 提取 script 部分
  const scriptMatch = content.match(/<script[^>]*>([\s\S]*?)<\/script>/);
  if (!scriptMatch) {
    console.log('❌ 未找到 script 标签');
    return;
  }
  
  const scriptContent = scriptMatch[1];
  
  console.log('🔧 检查括号匹配...');
  if (checkBrackets(scriptContent)) {
    console.log('✅ 括号匹配正确');
  }
  
  // 检查常见的语法问题
  console.log('🔧 检查常见语法问题...');
  
  const lines = scriptContent.split('\n');
  let hasErrors = false;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    const lineNum = i + 1;
    
    // 检查未闭合的函数
    if (line.includes('const ') && line.includes(' = ') && line.includes('(') && !line.includes(')')) {
      if (i + 1 < lines.length && !lines[i + 1].trim().startsWith(')')) {
        console.log(`⚠️  第 ${lineNum} 行: 可能的未闭合函数定义`);
      }
    }
    
    // 检查重复的 catch 块
    if (line.includes('} catch (') && i > 0) {
      for (let j = i - 5; j < i; j++) {
        if (j >= 0 && lines[j].trim().includes('} catch (')) {
          console.log(`⚠️  第 ${lineNum} 行: 可能的重复 catch 块`);
          hasErrors = true;
          break;
        }
      }
    }
    
    // 检查多余的大括号
    if (line === '}' && i > 0 && lines[i - 1].trim() === '}') {
      console.log(`⚠️  第 ${lineNum} 行: 可能的多余大括号`);
    }
  }
  
  if (!hasErrors) {
    console.log('✅ 未发现明显的语法问题');
  }
  
  console.log('\n📊 文件统计:');
  console.log(`   总行数: ${lines.length}`);
  console.log(`   非空行数: ${lines.filter(line => line.trim()).length}`);
  
  // 统计括号
  const openBrackets = (scriptContent.match(/\{/g) || []).length;
  const closeBrackets = (scriptContent.match(/\}/g) || []).length;
  console.log(`   大括号: ${openBrackets} 开, ${closeBrackets} 闭`);
  
  const openParens = (scriptContent.match(/\(/g) || []).length;
  const closeParens = (scriptContent.match(/\)/g) || []).length;
  console.log(`   小括号: ${openParens} 开, ${closeParens} 闭`);
}

if (require.main === module) {
  checkSyntax();
}

module.exports = { checkBrackets, checkSyntax };
