#!/usr/bin/env python3
"""
直接测试数据库插入
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.database import get_db_manager
import json
import uuid
from datetime import datetime

def test_direct_insert():
    """直接测试数据库插入"""
    
    print("🗄️ 直接数据库插入测试")
    print("=" * 40)
    
    try:
        db_manager = get_db_manager()
        
        # 生成测试数据
        doc_id = str(uuid.uuid4())
        timestamp = datetime.now().isoformat()
        
        test_data = {
            'id': doc_id,
            'knowledge_base_id': 'default',
            'title': 'direct_test_document.txt',
            'content': '这是直接插入数据库的测试文档',
            'content_type': 'txt',
            'doc_metadata': json.dumps({
                'summary': '直接插入测试',
                'type': 'txt',
                'size': 100
            }),
            'created_at': timestamp
        }
        
        print(f"📝 准备插入数据:")
        print(f"  ID: {test_data['id']}")
        print(f"  标题: {test_data['title']}")
        print(f"  类型: {test_data['content_type']}")
        
        # 尝试插入
        insert_sql = """
        INSERT INTO knowledge_documents 
        (id, knowledge_base_id, title, content, content_type, doc_metadata, created_at)
        VALUES (%s, %s, %s, %s, %s, %s, %s) 
        RETURNING id
        """
        
        print("🚀 执行插入操作...")
        result = db_manager.execute_query(
            insert_sql,
            [
                test_data['id'],
                test_data['knowledge_base_id'],
                test_data['title'],
                test_data['content'],
                test_data['content_type'],
                test_data['doc_metadata'],
                test_data['created_at']
            ]
        )
        
        if result:
            inserted_id = result[0]['id'] if isinstance(result[0], dict) else result[0][0]
            print(f"✅ 插入成功，返回ID: {inserted_id}")
            
            # 验证插入
            print("🔍 验证插入结果...")
            verify_sql = "SELECT * FROM knowledge_documents WHERE id = %s"
            verify_result = db_manager.execute_query(verify_sql, [inserted_id])
            
            if verify_result:
                doc = verify_result[0]
                print(f"✅ 验证成功:")
                print(f"  ID: {doc.get('id')}")
                print(f"  标题: {doc.get('title')}")
                print(f"  类型: {doc.get('content_type')}")
                print(f"  创建时间: {doc.get('created_at')}")
                return True
            else:
                print("❌ 验证失败，未找到插入的记录")
                return False
        else:
            print("❌ 插入失败，未返回ID")
            return False
            
    except Exception as e:
        print(f"❌ 直接插入测试失败: {e}")
        return False

def test_table_structure():
    """测试表结构"""
    
    print("\n📋 检查表结构")
    print("=" * 30)
    
    try:
        db_manager = get_db_manager()
        
        # 检查表是否存在
        table_check_sql = """
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'knowledge_documents'
        """
        
        table_result = db_manager.execute_query(table_check_sql)
        
        if table_result:
            print("✅ knowledge_documents表存在")
            
            # 检查表结构
            columns_sql = """
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns
            WHERE table_name = 'knowledge_documents'
            ORDER BY ordinal_position
            """
            
            columns = db_manager.execute_query(columns_sql)
            if columns:
                print("📋 表结构:")
                for col in columns:
                    nullable = "NULL" if col['is_nullable'] == 'YES' else "NOT NULL"
                    default = f" DEFAULT {col['column_default']}" if col['column_default'] else ""
                    print(f"  - {col['column_name']}: {col['data_type']} ({nullable}){default}")
                return True
            else:
                print("❌ 无法获取表结构")
                return False
        else:
            print("❌ knowledge_documents表不存在")
            return False
            
    except Exception as e:
        print(f"❌ 表结构检查失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 数据库直接操作测试")
    print("=" * 50)
    
    # 检查表结构
    structure_test = test_table_structure()
    
    if structure_test:
        # 测试直接插入
        insert_test = test_direct_insert()
        
        print("\n" + "=" * 50)
        print("📊 测试结果:")
        print(f"  表结构检查: {'✅ 成功' if structure_test else '❌ 失败'}")
        print(f"  直接插入测试: {'✅ 成功' if insert_test else '❌ 失败'}")
        
        if structure_test and insert_test:
            print("\n🎉 数据库操作完全正常！")
            print("💡 问题可能在于API层面的逻辑")
        else:
            print("\n⚠️ 数据库操作存在问题")
    else:
        print("\n❌ 表结构有问题，无法继续测试")
