#!/usr/bin/env python3
"""
简化的智能体知识库测试
"""

import requests
import json
import io

def test_simple_agent_knowledge():
    base_url = "http://localhost:8000/api/v1"
    agent_id = "e55f5e84-6d8b-4265-8e55-728bdb0d2455"
    
    print("🧪 测试智能体专属知识库功能")
    print("=" * 50)
    
    # 1. 测试获取智能体知识库文档
    print(f"\n1. 测试获取智能体知识库文档")
    try:
        response = requests.get(f"{base_url}/agents/{agent_id}/knowledge-documents")
        print(f"   URL: {base_url}/agents/{agent_id}/knowledge-documents")
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   响应数据: {json.dumps(data, indent=2, ensure_ascii=False)[:200]}...")
            
            if data.get('success'):
                docs = data.get('documents', [])
                print(f"   ✅ 获取成功，文档数量: {len(docs)}")
                for i, doc in enumerate(docs[:2]):
                    print(f"   📄 文档{i+1}: {doc.get('title', 'N/A')}")
            else:
                print(f"   ❌ 获取失败: {data.get('error', 'Unknown error')}")
        else:
            print(f"   ❌ 请求失败: {response.status_code}")
            print(f"   响应内容: {response.text[:200]}")
            
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
    
    # 2. 测试上传文件到智能体
    print(f"\n2. 测试上传文件到智能体")
    try:
        # 创建简单的测试文件
        test_content = "这是一个测试文档，用于验证智能体专属知识库功能。"
        test_file = io.BytesIO(test_content.encode('utf-8'))
        
        files = {
            'file': ('test_document.txt', test_file, 'text/plain')
        }
        
        response = requests.post(f"{base_url}/agents/{agent_id}/upload-file", files=files)
        print(f"   URL: {base_url}/agents/{agent_id}/upload-file")
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   响应数据: {json.dumps(data, indent=2, ensure_ascii=False)[:200]}...")
            
            if data.get('success'):
                doc = data.get('document', {})
                print(f"   ✅ 上传成功")
                print(f"   📄 文档ID: {doc.get('id', 'N/A')}")
                print(f"   📄 文档标题: {doc.get('title', 'N/A')}")
                print(f"   📄 所属智能体: {doc.get('agent_id', 'N/A')}")
                return doc.get('id')
            else:
                print(f"   ❌ 上传失败: {data.get('error', 'Unknown error')}")
        else:
            print(f"   ❌ 上传请求失败: {response.status_code}")
            print(f"   响应内容: {response.text[:200]}")
            
    except Exception as e:
        print(f"   ❌ 上传异常: {e}")
    
    return None

def test_global_vs_agent_knowledge():
    """测试全局知识库 vs 智能体知识库的区别"""
    base_url = "http://localhost:8000/api/v1"
    agent_id = "e55f5e84-6d8b-4265-8e55-728bdb0d2455"
    
    print(f"\n3. 对比全局知识库 vs 智能体知识库")
    
    # 获取全局知识库
    try:
        global_response = requests.get(f"{base_url}/ai-agent/knowledge-documents")
        print(f"   全局知识库URL: {base_url}/ai-agent/knowledge-documents")
        print(f"   全局知识库状态码: {global_response.status_code}")
        
        if global_response.status_code == 200:
            global_data = global_response.json()
            global_docs = global_data.get('documents', []) if global_data.get('success') else []
            print(f"   📚 全局知识库文档数量: {len(global_docs)}")
        else:
            print(f"   ❌ 全局知识库请求失败: {global_response.status_code}")
            global_docs = []
    except Exception as e:
        print(f"   ❌ 全局知识库异常: {e}")
        global_docs = []
    
    # 获取智能体知识库
    try:
        agent_response = requests.get(f"{base_url}/agents/{agent_id}/knowledge-documents")
        print(f"   智能体知识库URL: {base_url}/agents/{agent_id}/knowledge-documents")
        print(f"   智能体知识库状态码: {agent_response.status_code}")
        
        if agent_response.status_code == 200:
            agent_data = agent_response.json()
            agent_docs = agent_data.get('documents', []) if agent_data.get('success') else []
            print(f"   🤖 智能体知识库文档数量: {len(agent_docs)}")
        else:
            print(f"   ❌ 智能体知识库请求失败: {agent_response.status_code}")
            agent_docs = []
    except Exception as e:
        print(f"   ❌ 智能体知识库异常: {e}")
        agent_docs = []
    
    # 分析差异
    if len(global_docs) != len(agent_docs):
        print(f"   ✅ 知识库已隔离: 全局({len(global_docs)}) vs 智能体({len(agent_docs)})")
    else:
        print(f"   ⚠️  知识库可能未隔离: 数量相同({len(global_docs)})")

if __name__ == "__main__":
    # 测试基本功能
    uploaded_doc_id = test_simple_agent_knowledge()
    
    # 测试隔离效果
    test_global_vs_agent_knowledge()
    
    print("\n" + "=" * 50)
    print("📊 测试完成")
    print("🔧 如果测试失败，请检查:")
    print("1. 后端服务是否正常运行")
    print("2. 数据库表是否包含 agent_id 字段")
    print("3. API路由是否正确配置")
    
    print(f"\n🌐 前端测试:")
    print(f"智能体编辑器: http://192.168.1.143:3000/utilities/daily/ai-agent/editor?id=e55f5e84-6d8b-4265-8e55-728bdb0d2455")
    print("在编辑器中上传文件，检查是否只显示该智能体的文档")
