"""
文档翻译API路由
提供完整的异步文档翻译功能
"""

from fastapi import APIRouter, HTTPException, UploadFile, File, Form, BackgroundTasks
from typing import Optional, Dict, Any, List
from pydantic import BaseModel
import uuid
import logging
import os
import asyncio
import re
from datetime import datetime
from pathlib import Path

from ...core.database import get_db_manager
from ...services.translation_service import TranslationService

router = APIRouter()
translation_service = TranslationService()
logger = logging.getLogger(__name__)


def parse_translation_content(content: str) -> list:
    """
    从翻译内容中解析出双语对照数据
    """
    try:
        bilingual_data = []

        # 按段落分割
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]

        for i, paragraph in enumerate(paragraphs):
            # 检查是否是Excel行格式 [ROW_X]
            if paragraph.startswith('[ROW_') and '] ' in paragraph:
                # 提取行号和内容
                match = re.match(r'\[ROW_(\d+)\] (.+)', paragraph, re.DOTALL)
                if match:
                    row_num = int(match.group(1))
                    translated_content = match.group(2)

                    # 生成原文占位符（实际应该从原文件提取）
                    source_content = f"Excel行 {row_num + 1} 原文内容"

                    bilingual_data.append({
                        "id": row_num + 1,
                        "source": source_content,
                        "translated": translated_content
                    })
                else:
                    # 如果格式不匹配，使用段落索引
                    bilingual_data.append({
                        "id": i + 1,
                        "source": f"原文段落 {i + 1}",
                        "translated": paragraph
                    })
            else:
                # 普通段落
                bilingual_data.append({
                    "id": i + 1,
                    "source": f"原文段落 {i + 1}",
                    "translated": paragraph
                })

        return bilingual_data

    except Exception as e:
        logger.error(f"解析翻译内容失败: {e}")
        return None

# 支持的文件类型
SUPPORTED_FORMATS = {
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": ".docx",
    "application/msword": ".doc", 
    "application/pdf": ".pdf",
    "text/plain": ".txt",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": ".xlsx",
    "application/vnd.ms-excel": ".xls",
    "application/vnd.openxmlformats-officedocument.presentationml.presentation": ".pptx",
    "application/vnd.ms-powerpoint": ".ppt"
}

class DocumentTranslationRequest(BaseModel):
    """文档翻译请求模型"""
    source_language: str = "auto"
    target_language: str = "zh"
    domain: str = "general"
    style: str = "standard"
    advanced_options: List[str] = []


@router.post("/upload")
async def upload_document(
    file: UploadFile = File(...),
    source_language: str = Form("auto"),
    target_language: str = Form("zh"),
    domain: str = Form("general"),
    style: str = Form("standard")
):
    """
    上传文档并启动异步翻译任务
    """
    try:
        # 验证文件类型
        if file.content_type not in SUPPORTED_FORMATS:
            return {
                "success": False, 
                "error": f"不支持的文件格式: {file.content_type}。支持的格式: {', '.join(SUPPORTED_FORMATS.values())}"
            }
        
        # 验证文件大小 (50MB限制)
        content = await file.read()
        if len(content) > 50 * 1024 * 1024:
            return {"success": False, "error": "文件大小不能超过50MB"}
        
        # 生成任务ID和文件路径
        task_id = str(uuid.uuid4())
        file_ext = SUPPORTED_FORMATS[file.content_type]
        filename = f"{task_id}_{file.filename}"
        
        # 创建存储目录
        storage_dir = Path("storage/documents")
        storage_dir.mkdir(parents=True, exist_ok=True)
        file_path = storage_dir / filename
        
        # 保存文件
        with open(file_path, "wb") as f:
            f.write(content)
        
        # 记录任务信息到数据库
        db_manager = get_db_manager()
        
        # 创建文档翻译任务表
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS document_translation_tasks (
            task_id VARCHAR(36) PRIMARY KEY,
            filename VARCHAR(255) NOT NULL,
            original_filename VARCHAR(255) NOT NULL,
            file_path VARCHAR(500) NOT NULL,
            file_size INTEGER NOT NULL,
            source_language VARCHAR(10) NOT NULL,
            target_language VARCHAR(10) NOT NULL,
            domain VARCHAR(50) DEFAULT 'general',
            style VARCHAR(50) DEFAULT 'standard',
            status VARCHAR(20) DEFAULT 'pending',
            progress INTEGER DEFAULT 0,
            message TEXT,
            result_path VARCHAR(500),
            word_count INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        db_manager.execute_query(create_table_sql)

        # 添加 word_count 字段（如果不存在）
        try:
            if hasattr(db_manager, 'use_postgres') and db_manager.use_postgres:
                add_column_sql = """
                ALTER TABLE document_translation_tasks
                ADD COLUMN IF NOT EXISTS word_count INTEGER DEFAULT 0
                """
            else:
                # SQLite 不支持 IF NOT EXISTS，需要检查字段是否存在
                add_column_sql = """
                ALTER TABLE document_translation_tasks
                ADD COLUMN word_count INTEGER DEFAULT 0
                """

            db_manager.execute_query(add_column_sql)
            logger.info("word_count 字段添加成功")
        except Exception as e:
            # 字段可能已经存在，忽略错误
            logger.info(f"word_count 字段可能已存在: {e}")
        
        # 插入任务记录
        if hasattr(db_manager, 'use_postgres') and db_manager.use_postgres:
            insert_sql = """
            INSERT INTO document_translation_tasks 
            (task_id, filename, original_filename, file_path, file_size, source_language, target_language, domain, style, status, progress, message, created_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
        else:
            insert_sql = """
            INSERT INTO document_translation_tasks 
            (task_id, filename, original_filename, file_path, file_size, source_language, target_language, domain, style, status, progress, message, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
        
        db_manager.execute_query(
            insert_sql,
            (
                task_id, filename, file.filename, str(file_path), len(content),
                source_language, target_language, domain, style,
                "pending", 0, "文档已上传，等待处理", datetime.now().isoformat()
            )
        )
        
        # 启动异步翻译任务
        from ...tasks.document_translation_tasks import translate_document_v2
        celery_task = translate_document_v2.apply_async(
            args=[task_id, str(file_path), source_language, target_language, domain, style],
            task_id=task_id
        )
        
        logger.info(f"文档翻译任务已启动: {task_id}, 文件: {file.filename}")
        
        return {
            "success": True,
            "task_id": task_id,
            "message": "文档上传成功，翻译任务已启动",
            "filename": file.filename,
            "file_size": len(content)
        }
        
    except Exception as e:
        logger.error(f"文档上传失败: {e}")
        return {"success": False, "error": f"文档上传失败: {str(e)}"}


@router.get("/status/{task_id}")
async def get_translation_status(task_id: str):
    """
    获取文档翻译状态
    """
    try:
        db_manager = get_db_manager()
        
        # 查询任务状态
        if hasattr(db_manager, 'use_postgres') and db_manager.use_postgres:
            query_sql = "SELECT * FROM document_translation_tasks WHERE task_id = %s"
        else:
            query_sql = "SELECT * FROM document_translation_tasks WHERE task_id = ?"
        
        result = db_manager.execute_query(query_sql, (task_id,))
        
        if not result:
            return {"success": False, "error": "任务不存在"}
        
        task_info = result[0]
        
        # 处理不同数据库返回格式
        if hasattr(task_info, 'keys'):  # PostgreSQL
            task_data = dict(task_info)
        else:  # SQLite
            columns = ['task_id', 'filename', 'original_filename', 'file_path', 'file_size',
                      'source_language', 'target_language', 'domain', 'style', 'status',
                      'progress', 'message', 'result_path', 'word_count', 'created_at', 'updated_at']
            task_data = dict(zip(columns, task_info))
        
        return {
            "success": True,
            "task_id": task_id,
            "status": task_data["status"],
            "progress": task_data["progress"],
            "message": task_data["message"],
            "filename": task_data["original_filename"],
            "source_language": task_data["source_language"],
            "target_language": task_data["target_language"],
            "domain": task_data["domain"],
            "style": task_data["style"],
            "result_path": task_data.get("result_path"),
            "word_count": task_data.get("word_count", 0),
            "created_at": task_data["created_at"]
        }
        
    except Exception as e:
        logger.error(f"获取翻译状态失败: {e}")
        return {"success": False, "error": f"获取状态失败: {str(e)}"}


@router.get("/download/{task_id}")
async def download_translated_document(task_id: str):
    """
    下载翻译后的文档
    """
    try:
        db_manager = get_db_manager()
        
        # 查询任务信息
        if hasattr(db_manager, 'use_postgres') and db_manager.use_postgres:
            query_sql = "SELECT result_path, original_filename, status FROM document_translation_tasks WHERE task_id = %s"
        else:
            query_sql = "SELECT result_path, original_filename, status FROM document_translation_tasks WHERE task_id = ?"
        
        result = db_manager.execute_query(query_sql, (task_id,))
        
        if not result:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        task_info = result[0]
        
        if hasattr(task_info, 'keys'):  # PostgreSQL
            result_path = task_info['result_path']
            original_filename = task_info['original_filename']
            status = task_info['status']
        else:  # SQLite
            result_path, original_filename, status = task_info
        
        if status != "completed":
            raise HTTPException(status_code=400, detail="翻译尚未完成")
        
        if not result_path or not os.path.exists(result_path):
            raise HTTPException(status_code=404, detail="翻译文件不存在")
        
        from fastapi.responses import FileResponse
        
        # 生成下载文件名
        name_parts = original_filename.rsplit('.', 1)
        if len(name_parts) == 2:
            download_filename = f"{name_parts[0]}_translated.{name_parts[1]}"
        else:
            download_filename = f"{original_filename}_translated"
        
        return FileResponse(
            path=result_path,
            filename=download_filename,
            media_type='application/octet-stream'
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载文档失败: {e}")
        raise HTTPException(status_code=500, detail=f"下载失败: {str(e)}")


@router.get("/formats")
async def get_supported_formats():
    """
    获取支持的文件格式
    """
    return {
        "success": True,
        "formats": list(SUPPORTED_FORMATS.values()),
        "mime_types": list(SUPPORTED_FORMATS.keys()),
        "description": {
            ".docx": "Microsoft Word 文档",
            ".doc": "Microsoft Word 文档 (旧版)",
            ".pdf": "PDF 文档",
            ".txt": "纯文本文件",
            ".xlsx": "Microsoft Excel 表格",
            ".xls": "Microsoft Excel 表格 (旧版)",
            ".pptx": "Microsoft PowerPoint 演示文稿",
            ".ppt": "Microsoft PowerPoint 演示文稿 (旧版)"
        }
    }


@router.get("/preview/{task_id}")
async def get_translation_preview(task_id: str):
    """
    获取翻译预览（前几段内容）
    """
    try:
        # 获取任务状态
        status_result = await get_translation_status(task_id)

        if not status_result["success"]:
            return status_result

        # 如果翻译未完成，返回状态信息
        if status_result["status"] != "completed":
            return {
                "success": True,
                "task_id": task_id,
                "preview": f"翻译进行中... ({status_result['progress']}%)",
                "status": status_result
            }

        # 如果翻译完成，尝试读取结果文件和原文件作为预览
        result_path = status_result.get("result_path")

        logger.info(f"预览任务 {task_id}，结果文件路径: {result_path}")

        if result_path:
            # 确保路径是绝对路径
            if not os.path.isabs(result_path):
                # 如果是相对路径，转换为绝对路径
                result_path = os.path.abspath(result_path)

            logger.info(f"检查文件是否存在: {result_path}")
            logger.info(f"文件存在: {os.path.exists(result_path)}")

            if os.path.exists(result_path):
                try:
                    # 读取翻译结果
                    logger.info(f"开始读取翻译结果文件: {result_path}")

                    # 尝试不同的编码方式
                    translated_content = None
                    for encoding in ['utf-8', 'gbk', 'utf-8-sig']:
                        try:
                            with open(result_path, 'r', encoding=encoding) as f:
                                translated_content = f.read()
                            logger.info(f"使用 {encoding} 编码成功读取文件")
                            break
                        except UnicodeDecodeError:
                            logger.warning(f"使用 {encoding} 编码读取失败，尝试下一种编码")
                            continue

                    if translated_content is None:
                        raise Exception("无法使用任何编码读取文件")

                    logger.info(f"成功读取翻译内容，长度: {len(translated_content)} 字符")

                    # 尝试读取双语对照文件
                    bilingual_path = result_path.replace('.txt', '_bilingual.json').replace('.xlsx', '_bilingual.json')
                    bilingual_data = None

                    logger.info(f"查找双语对照文件: {bilingual_path}")
                    logger.info(f"文件是否存在: {os.path.exists(bilingual_path)}")

                    if os.path.exists(bilingual_path):
                        try:
                            import json
                            with open(bilingual_path, 'r', encoding='utf-8') as f:
                                bilingual_data = json.load(f)
                            logger.info(f"成功读取双语对照数据: {len(bilingual_data)} 段")
                        except Exception as e:
                            logger.warning(f"读取双语对照文件失败: {e}")
                    else:
                        # 尝试其他可能的路径
                        alternative_paths = [
                            result_path.replace('.txt', '_bilingual.json'),
                            result_path.replace('.xlsx', '_bilingual.json'),
                            result_path.rsplit('.', 1)[0] + '_bilingual.json'
                        ]

                        for alt_path in alternative_paths:
                            logger.info(f"尝试备用路径: {alt_path}")
                            if os.path.exists(alt_path):
                                try:
                                    import json
                                    with open(alt_path, 'r', encoding='utf-8') as f:
                                        bilingual_data = json.load(f)
                                    logger.info(f"从备用路径成功读取双语对照数据: {len(bilingual_data)} 段")
                                    break
                                except Exception as e:
                                    logger.warning(f"从备用路径读取双语对照文件失败: {e}")

                        if not bilingual_data:
                            logger.warning("未找到双语对照文件")

                    if bilingual_data:
                        # 使用双语对照数据
                        preview_data = bilingual_data[:3]  # 取前3段
                        if len(bilingual_data) > 3:
                            preview_data.append({
                                "id": "more",
                                "source": f"... (还有 {len(bilingual_data) - 3} 段)",
                                "translated": f"... (还有 {len(bilingual_data) - 3} 段)"
                            })

                        # 返回结构化的双语数据
                        return {
                            "success": True,
                            "task_id": task_id,
                            "preview": preview_data,  # 返回结构化数据而不是字符串
                            "preview_type": "bilingual",
                            "total_paragraphs": len(bilingual_data),
                            "preview_paragraphs": len(preview_data),
                            "status": status_result
                        }
                    else:
                        # 尝试从翻译内容中解析双语数据
                        logger.info("未找到双语对照文件，尝试从翻译内容解析")
                        parsed_bilingual = parse_translation_content(translated_content)

                        if parsed_bilingual:
                            logger.info(f"从翻译内容解析出双语数据: {len(parsed_bilingual)} 段")
                            preview_data = parsed_bilingual[:3]  # 取前3段
                            if len(parsed_bilingual) > 3:
                                preview_data.append({
                                    "id": "more",
                                    "source": f"... (还有 {len(parsed_bilingual) - 3} 段)",
                                    "translated": f"... (还有 {len(parsed_bilingual) - 3} 段)"
                                })

                            return {
                                "success": True,
                                "task_id": task_id,
                                "preview": preview_data,
                                "preview_type": "bilingual",
                                "total_paragraphs": len(parsed_bilingual),
                                "preview_paragraphs": len(preview_data),
                                "status": status_result
                            }
                        else:
                            # 降级为普通文本预览
                            translated_paragraphs = [p.strip() for p in translated_content.split('\n\n') if p.strip()]
                            preview_translated = translated_paragraphs[:3]  # 只取前3段

                            if len(translated_paragraphs) > 3:
                                preview_translated.append(f"... (还有 {len(translated_paragraphs) - 3} 段)")

                    # 尝试获取原文（从任务信息中获取原文件路径）
                    db_manager = get_db_manager()
                    if hasattr(db_manager, 'use_postgres') and db_manager.use_postgres:
                        query_sql = "SELECT file_path FROM document_translation_tasks WHERE task_id = %s"
                    else:
                        query_sql = "SELECT file_path FROM document_translation_tasks WHERE task_id = ?"

                    result = db_manager.execute_query(query_sql, (task_id,))
                    original_file_path = None
                    if result and len(result) > 0:
                        if hasattr(result[0], 'keys'):  # PostgreSQL
                            original_file_path = result[0]['file_path']
                        else:  # SQLite
                            original_file_path = result[0][0] if len(result[0]) > 0 else None

                    source_preview = "原文内容（已处理）"
                    if original_file_path and os.path.exists(original_file_path):
                        try:
                            # 这里可以添加原文提取逻辑，目前简化处理
                            source_preview = f"原文件: {os.path.basename(original_file_path)}"
                        except:
                            pass

                    preview_content = '\n\n'.join(preview_translated)

                    logger.info(f"预览内容生成成功，长度: {len(preview_content)} 字符")

                    return {
                        "success": True,
                        "task_id": task_id,
                        "preview": preview_content,
                        "source_preview": source_preview,
                        "total_paragraphs": len(translated_paragraphs),
                        "preview_paragraphs": len(preview_translated),
                        "status": status_result
                    }

                except Exception as e:
                    logger.error(f"读取预览文件失败: {e}")
                    logger.error(f"文件路径: {result_path}")
                    logger.error(f"错误类型: {type(e).__name__}")
                    logger.error(f"错误详情: {str(e)}")

                    # 尝试读取文件的基本信息
                    try:
                        file_size = os.path.getsize(result_path)
                        logger.error(f"文件大小: {file_size} 字节")

                        # 尝试以二进制模式读取前100字节
                        with open(result_path, 'rb') as f:
                            first_bytes = f.read(100)
                            logger.error(f"文件前100字节: {first_bytes}")
                    except Exception as debug_e:
                        logger.error(f"无法读取文件基本信息: {debug_e}")

                    return {
                        "success": True,
                        "task_id": task_id,
                        "preview": f"读取预览文件失败: {type(e).__name__}: {str(e)}",
                        "status": status_result
                    }
            else:
                logger.warning(f"翻译结果文件不存在: {result_path}")
                # 列出目录内容以便调试
                try:
                    parent_dir = os.path.dirname(result_path)
                    if os.path.exists(parent_dir):
                        files = os.listdir(parent_dir)
                        logger.info(f"目录 {parent_dir} 中的文件: {files}")
                    else:
                        logger.warning(f"父目录不存在: {parent_dir}")
                except Exception as e:
                    logger.error(f"列出目录内容失败: {e}")

                return {
                    "success": True,
                    "task_id": task_id,
                    "preview": f"翻译结果文件不存在: {result_path}",
                    "status": status_result
                }
        else:
            logger.warning(f"任务 {task_id} 没有结果文件路径")
            return {
                "success": True,
                "task_id": task_id,
                "preview": "任务没有结果文件路径",
                "status": status_result
            }

    except Exception as e:
        logger.error(f"获取预览失败: {e}")
        return {"success": False, "error": f"获取预览失败: {str(e)}"}
