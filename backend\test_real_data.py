#!/usr/bin/env python3
"""
测试真实数据加载和显示
"""

import requests
import json

def test_agent_update_and_load():
    """测试智能体更新和加载"""
    import time

    agent_id = "e55f5e84-6d8b-4265-8e55-728bdb0d2455"

    print("🔄 测试智能体更新和数据加载")
    print("=" * 50)

    # 1. 更新智能体
    timestamp = int(time.time())
    update_data = {
        "name": f"测试更新智能体-{timestamp}",
        "description": f"这是一个测试更新的描述，时间戳：{timestamp}",
        "systemPrompt": f"你是一个测试助手，当前时间：{timestamp}"
    }
    
    print(f"📝 更新数据: {json.dumps(update_data, indent=2, ensure_ascii=False)}")
    
    try:
        # 更新智能体
        update_url = f"http://localhost:8000/api/v1/agents/{agent_id}"
        response = requests.put(update_url, json=update_data, timeout=10)
        
        print(f"📊 更新响应状态: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 更新成功: {result.get('message')}")
        else:
            print(f"❌ 更新失败: {response.text}")
            return False
            
        # 等待一下确保数据库更新完成
        import time
        time.sleep(1)
        
        # 2. 重新加载智能体数据
        get_url = f"http://localhost:8000/api/v1/agents/{agent_id}"
        response = requests.get(get_url, timeout=10)
        
        print(f"📊 加载响应状态: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                agent = result.get('agent', {})
                print(f"✅ 加载成功!")
                print(f"📄 智能体名称: {agent.get('name')}")
                print(f"📄 智能体描述: {agent.get('description')}")
                print(f"📄 系统提示: {agent.get('system_prompt', '')[:100]}...")
                
                # 检查数据是否真的更新了
                if agent.get('name') == update_data['name']:
                    print("🎉 数据更新验证成功！名称匹配")
                    return True
                else:
                    print(f"❌ 数据更新验证失败！期望: {update_data['name']}, 实际: {agent.get('name')}")
                    return False
            else:
                print(f"❌ 加载失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 加载失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        return False

def test_knowledge_documents():
    """测试知识库文档列表"""
    
    print("\n📚 测试知识库文档列表")
    print("=" * 50)
    
    try:
        # 获取知识库文档列表
        url = "http://localhost:8000/api/v1/ai-agent/knowledge-documents"
        response = requests.get(url, timeout=10)
        
        print(f"📊 响应状态: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                documents = result.get('documents', [])
                total = result.get('total', 0)
                
                print(f"✅ 获取成功!")
                print(f"📄 文档总数: {total}")
                
                if documents:
                    print("📋 文档列表:")
                    for i, doc in enumerate(documents[:5]):  # 只显示前5个
                        print(f"  {i+1}. {doc.get('filename')} ({doc.get('file_type')}) - {doc.get('created_at')}")
                else:
                    print("📋 暂无文档")
                    
                return True
            else:
                print(f"❌ 获取失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 请求失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        return False

def test_file_upload_and_list():
    """测试文件上传和列表更新"""
    
    print("\n📁 测试文件上传和列表更新")
    print("=" * 50)
    
    # 创建测试文件
    import time
    timestamp = int(time.time())
    test_content = f"""测试文档,内容,备注
测试数据1,内容1,备注1
测试数据2,内容2,备注2
时间戳,{timestamp},测试上传"""
    
    test_filename = f"test_knowledge_{timestamp}.csv"
    
    try:
        # 保存测试文件
        with open(test_filename, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        print(f"📁 创建测试文件: {test_filename}")
        
        # 上传文件
        url = "http://localhost:8000/api/v1/ai-agent/upload-file"
        with open(test_filename, 'rb') as f:
            files = {'file': (test_filename, f, 'text/csv')}
            response = requests.post(url, files=files, timeout=30)
        
        print(f"📊 上传响应状态: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 上传成功: {result.get('message')}")
                print(f"📄 文件信息: {result.get('filename')} ({result.get('size')} bytes)")
                
                # 等待处理完成
                time.sleep(2)
                
                # 重新获取文档列表
                print("\n🔄 重新获取文档列表...")
                return test_knowledge_documents()
            else:
                print(f"❌ 上传失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 上传失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        return False
    finally:
        # 清理测试文件
        import os
        if os.path.exists(test_filename):
            os.remove(test_filename)
            print(f"🗑️ 清理测试文件: {test_filename}")

if __name__ == "__main__":
    import time
    
    print("🧪 开始测试真实数据功能")
    print("=" * 60)
    
    # 测试智能体更新和加载
    agent_test = test_agent_update_and_load()
    
    # 测试知识库文档
    docs_test = test_knowledge_documents()
    
    # 测试文件上传
    upload_test = test_file_upload_and_list()
    
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print(f"  智能体更新加载: {'✅ 成功' if agent_test else '❌ 失败'}")
    print(f"  知识库文档列表: {'✅ 成功' if docs_test else '❌ 失败'}")
    print(f"  文件上传更新: {'✅ 成功' if upload_test else '❌ 失败'}")
    
    if agent_test and docs_test and upload_test:
        print("\n🎉 所有测试通过！数据更新和显示功能正常！")
    else:
        print("\n⚠️ 部分测试失败，请检查相关功能")
