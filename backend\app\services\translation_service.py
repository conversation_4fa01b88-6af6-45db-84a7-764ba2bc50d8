"""
翻译业务服务
"""

import os
import uuid
import json
import asyncio
import httpx
import logging
from typing import Optional, Dict, Any
from fastapi import UploadFile
from ..core.config import settings

# 配置日志
logger = logging.getLogger(__name__)


class TranslationService:
    """翻译服务类"""

    def __init__(self):
        self.storage_path = os.path.join(settings.STORAGE_PATH, "translations")
        os.makedirs(self.storage_path, exist_ok=True)

        # Ollama配置
        self.ollama_base_url = os.environ.get("OLLAMA_API_URL", "http://localhost:11434")
        self.default_model = os.environ.get("OLLAMA_MODEL", "qwen2.5:7b")  # 更新默认模型

        # 异步任务存储
        self.async_tasks = {}

        # 模型管理服务
        from .translation_model_service import translation_model_service
        self.model_service = translation_model_service

        logger.info(f"翻译服务初始化完成，Ollama URL: {self.ollama_base_url}, 默认模型: {self.default_model}")

    async def check_ollama_available(self) -> bool:
        """检查Ollama服务是否可用"""
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(f"{self.ollama_base_url}/api/tags")
                return response.status_code == 200
        except Exception as e:
            logger.warning(f"Ollama服务不可用: {e}")
            return False

    async def get_available_models(self) -> list:
        """获取可用的Ollama模型列表"""
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(f"{self.ollama_base_url}/api/tags")
                if response.status_code == 200:
                    data = response.json()
                    return [model["name"] for model in data.get("models", [])]
        except Exception as e:
            logger.warning(f"获取模型列表失败: {e}")

        return [self.default_model]
    
    async def translate_text(self, text: str, source_lang: str = "auto", target_lang: str = "zh",
                           domain: str = "general", style: str = "standard", model: str = None):
        """文本翻译 - 智能模型选择版"""
        try:
            if not text.strip():
                return {"success": False, "error": "文本不能为空"}

            # 如果源语言和目标语言相同，直接返回原文
            if source_lang == target_lang and source_lang != "auto":
                return {
                    "success": True,
                    "translated_text": text,
                    "source_language": source_lang,
                    "target_language": target_lang,
                    "detected_language": source_lang
                }

            # 智能选择最佳模型
            selected_model, model_params = self.model_service.select_best_model(
                source_lang=source_lang,
                target_lang=target_lang,
                text_length=len(text),
                domain=domain,
                quality_preference=style,
                user_model=model
            )

            logger.info(f"智能选择模型: {selected_model} (文本长度: {len(text)}, 领域: {domain})")

            # 优先使用Ollama进行翻译
            if await self.check_ollama_available():
                result = await self._translate_with_ollama(
                    text, source_lang, target_lang, domain, style, selected_model, model_params
                )
                if result["success"]:
                    return result
                logger.warning("Ollama翻译失败，尝试备用方案")

            # 备用翻译方案
            return await self._translate_fallback(text, source_lang, target_lang)

        except Exception as e:
            logger.error(f"翻译失败: {e}")
            return {"success": False, "error": str(e)}

    async def _translate_with_ollama(self, text: str, source_lang: str, target_lang: str,
                                   domain: str, style: str, model: str = None, model_params: dict = None):
        """使用Ollama进行翻译 - 支持智能参数配置"""
        try:
            # 选择模型
            if not model:
                model = self.default_model

            # 使用智能参数或默认参数
            if not model_params:
                model_params = {
                    "temperature": 0.05,
                    "top_p": 0.9,
                    "num_predict": 1024
                }

            # 构建翻译提示词
            prompt = self._build_translation_prompt(text, source_lang, target_lang, domain, style)

            # 准备请求参数
            request_data = {
                "model": model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": model_params.get("temperature", 0.05),
                    "top_p": model_params.get("top_p", 0.9),
                    "num_predict": model_params.get("num_predict", 1024)
                }
            }

            # 添加停止词
            if "stop" in model_params:
                request_data["options"]["stop"] = model_params["stop"]

            logger.info(f"使用模型 {model} 翻译，参数: {request_data['options']}")

            # 根据文本长度动态设置超时时间
            text_length = len(text)
            if text_length > 5000:
                timeout = 180.0  # 3分钟
            elif text_length > 2000:
                timeout = 120.0  # 2分钟
            else:
                timeout = 60.0   # 1分钟

            logger.info(f"文本长度: {text_length} 字符，设置超时时间: {timeout} 秒")

            async with httpx.AsyncClient(timeout=timeout) as client:
                response = await client.post(
                    f"{self.ollama_base_url}/api/generate",
                    json=request_data
                )

                if response.status_code == 200:
                    data = response.json()
                    translated_text = data.get("response", "").strip()

                    # 清理翻译结果
                    translated_text = self._clean_translation_result(translated_text)

                    # 如果源语言是auto，尝试检测实际语言
                    detected_source_lang = source_lang
                    if source_lang == "auto":
                        detected_source_lang = self._detect_language(text)
                        logger.info(f"检测到源语言: {detected_source_lang}")

                    return {
                        "success": True,
                        "translated_text": translated_text,
                        "source_language": detected_source_lang,
                        "target_language": target_lang,
                        "model_used": model,
                        "method": "ollama"
                    }
                else:
                    error_msg = f"Ollama API错误: {response.status_code}"
                    try:
                        error_detail = response.text
                        if error_detail:
                            error_msg += f", 详情: {error_detail}"
                    except:
                        pass
                    logger.error(error_msg)
                    return {"success": False, "error": error_msg}

        except httpx.TimeoutException as e:
            error_msg = f"Ollama翻译超时: {str(e)}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
        except httpx.ConnectError as e:
            error_msg = f"无法连接到Ollama服务: {str(e)}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
        except Exception as e:
            error_msg = f"Ollama翻译失败: {str(e)}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}

    def _build_translation_prompt(self, text: str, source_lang: str, target_lang: str,
                                domain: str, style: str) -> str:
        """构建翻译提示词"""
        # 语言映射
        lang_map = {
            "auto": "自动检测",
            "zh": "中文",
            "en": "英语",
            "ja": "日语",
            "ko": "韩语",
            "fr": "法语",
            "de": "德语",
            "es": "西班牙语",
            "ru": "俄语"
        }

        source_name = lang_map.get(source_lang, source_lang)
        target_name = lang_map.get(target_lang, target_lang)

        # 领域和风格描述
        domain_desc = {
            "general": "通用",
            "technology": "科技",
            "medical": "医学",
            "legal": "法律",
            "finance": "金融"
        }.get(domain, "通用")

        style_desc = {
            "standard": "标准",
            "formal": "正式",
            "casual": "口语化",
            "academic": "学术"
        }.get(style, "标准")

        if source_lang == "auto":
            prompt = f"""请将以下文本翻译成{target_name}。

翻译要求：
1. 保持原文的意思和语调
2. 使用{style_desc}的语言风格
3. 适合{domain_desc}领域的表达习惯
4. 直接输出翻译结果，不要添加任何解释或标记

原文：
{text}

翻译："""
        else:
            prompt = f"""请将以下{source_name}文本翻译成{target_name}。

翻译要求：
1. 保持原文的意思和语调
2. 使用{style_desc}的语言风格
3. 适合{domain_desc}领域的表达习惯
4. 直接输出翻译结果，不要添加任何解释或标记

{source_name}原文：
{text}

{target_name}翻译："""

        return prompt

    def _clean_translation_result(self, text: str) -> str:
        """清理翻译结果"""
        # 移除常见的前缀和后缀
        prefixes_to_remove = [
            "翻译：", "Translation:", "译文：", "结果：", "答案：",
            "翻译结果：", "Translation result:", "Translated text:"
        ]

        for prefix in prefixes_to_remove:
            if text.startswith(prefix):
                text = text[len(prefix):].strip()

        # 移除引号
        if (text.startswith('"') and text.endswith('"')) or (text.startswith("'") and text.endswith("'")):
            text = text[1:-1]

        return text.strip()

    def _detect_language(self, text: str) -> str:
        """简单的语言检测"""
        # 简单的启发式语言检测
        chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
        total_chars = len(text.replace(' ', '').replace('\n', '').replace('\t', ''))

        if total_chars == 0:
            return "auto"

        chinese_ratio = chinese_chars / total_chars

        if chinese_ratio > 0.3:
            return "zh"
        elif any(char in text for char in "あいうえおかきくけこ"):
            return "ja"
        elif any(char in text for char in "가나다라마바사"):
            return "ko"
        else:
            # 检查是否主要是英文
            english_chars = sum(1 for char in text if char.isalpha() and ord(char) < 128)
            if english_chars / total_chars > 0.5:
                return "en"

        return "auto"  # 无法确定

    async def _translate_fallback(self, text: str, source_lang: str, target_lang: str):
        """备用翻译方案"""
        # 目前没有可用的备用翻译服务，返回失败
        # 实际项目中可以集成其他翻译API，如Google Translate、百度翻译等

        logger.error("所有翻译服务都不可用，没有可用的备用翻译方案")

        return {
            "success": False,
            "error": "翻译服务不可用：Ollama服务连接失败，且没有配置备用翻译服务",
            "source_language": source_lang,
            "target_language": target_lang,
            "method": "fallback_failed"
        }

    async def translate_text_async(self, task_id: str, text: str, source_lang: str = "auto",
                                 target_lang: str = "zh", domain: str = "general",
                                 style: str = "standard", model: str = None):
        """异步文本翻译 - 使用Celery"""
        try:
            from ..tasks.translation_tasks import translate_text_async

            # 启动Celery任务
            celery_task = translate_text_async.apply_async(
                args=[text, source_lang, target_lang, domain, style, model],
                task_id=task_id
            )

            return {
                "success": True,
                "task_id": task_id,
                "status": "processing",
                "message": "翻译任务已启动"
            }

        except Exception as e:
            logger.error(f"启动异步翻译任务失败: {e}")
            return {"success": False, "error": str(e)}

    async def get_translation_result(self, task_id: str):
        """获取异步翻译结果 - 从Celery获取"""
        try:
            from ..core.celery_unified import celery_app

            # 获取Celery任务结果
            celery_task = celery_app.AsyncResult(task_id)

            if celery_task.state == "PENDING":
                return {
                    "success": True,
                    "status": "pending",
                    "progress": 0,
                    "message": "任务等待中"
                }
            elif celery_task.state == "PROGRESS":
                meta = celery_task.info or {}
                return {
                    "success": True,
                    "status": "processing",
                    "progress": meta.get("progress", 0),
                    "message": meta.get("message", "处理中...")
                }
            elif celery_task.state == "SUCCESS":
                result = celery_task.result
                return {
                    "success": True,
                    "status": "completed",
                    "progress": 100,
                    "result": result
                }
            elif celery_task.state == "FAILURE":
                return {
                    "success": False,
                    "status": "failed",
                    "error": str(celery_task.info)
                }
            else:
                return {
                    "success": True,
                    "status": celery_task.state.lower(),
                    "progress": 0
                }

        except Exception as e:
            logger.error(f"获取翻译结果失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def translate_document(self, file: UploadFile, from_lang: str, to_lang: str):
        """文档翻译"""
        try:
            # 保存上传的文件
            filename = f"{uuid.uuid4()}_{file.filename}"
            file_path = os.path.join(self.storage_path, filename)
            
            content = await file.read()
            with open(file_path, "wb") as f:
                f.write(content)
            
            # 这里可以集成实际的文档翻译逻辑
            # 目前返回模拟结果
            
            # 生成翻译后的文件名
            translated_filename = f"translated_{filename}"
            translated_path = os.path.join(self.storage_path, translated_filename)
            
            # 模拟翻译处理
            with open(translated_path, "w", encoding="utf-8") as f:
                f.write(f"这是翻译后的文档内容 ({from_lang} -> {to_lang})")
            
            return {
                "success": True,
                "download_url": f"/storage/translations/{translated_filename}",
                "message": "文档翻译完成"
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def translate_audio(self, file: UploadFile, from_lang: str, to_lang: str):
        """音频翻译"""
        try:
            # 保存上传的音频文件
            filename = f"{uuid.uuid4()}_{file.filename}"
            file_path = os.path.join(self.storage_path, filename)
            
            content = await file.read()
            with open(file_path, "wb") as f:
                f.write(content)
            
            # 这里可以集成实际的音频翻译逻辑
            # 1. 语音识别 (ASR)
            # 2. 文本翻译
            # 3. 语音合成 (TTS)
            
            # 目前返回模拟结果
            translated_audio_filename = f"translated_{filename}"
            
            return {
                "success": True,
                "audio_url": f"/storage/translations/{translated_audio_filename}",
                "transcript": "这是识别出的文本内容",
                "translated_text": "这是翻译后的文本内容",
                "message": "音频翻译完成"
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def get_supported_languages(self):
        """获取支持的语言列表"""
        return {
            "auto": "自动检测",
            "zh": "中文",
            "en": "English",
            "ja": "日本語",
            "ko": "한국어",
            "fr": "Français",
            "de": "Deutsch",
            "es": "Español",
            "ru": "Русский"
        }
