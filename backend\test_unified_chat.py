#!/usr/bin/env python3
"""
测试统一聊天页面的后端API
"""

import requests
import json

def test_unified_chat_apis():
    base_url = "http://localhost:8000/api/v1"
    agent_id = "e55f5e84-6d8b-4265-8e55-728bdb0d2455"
    
    print("🧪 测试统一聊天页面相关API")
    print("=" * 60)
    
    # 1. 测试智能体详情API
    print("\n1. 测试智能体详情API")
    try:
        response = requests.get(f"{base_url}/agents/{agent_id}")
        print(f"   URL: {base_url}/agents/{agent_id}")
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 智能体名称: {data.get('name', 'N/A')}")
            print(f"   ✅ 智能体类型: {data.get('agent_type', 'N/A')}")
            print(f"   ✅ 智能体描述: {data.get('description', 'N/A')[:50]}...")
            print("   ✅ 智能体详情API正常")
        else:
            print(f"   ❌ 智能体详情API失败: {response.status_code}")
            print(f"   响应内容: {response.text[:200]}")
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
    
    # 2. 测试可用模型API
    print("\n2. 测试可用模型API")
    try:
        response = requests.get(f"{base_url}/agents/available-models")
        print(f"   URL: {base_url}/agents/available-models")
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                models = data.get('models', [])
                print(f"   ✅ 可用模型数量: {len(models)}")
                for i, model in enumerate(models[:3]):
                    print(f"   ✅ 模型{i+1}: {model.get('name')} ({model.get('provider')})")
                print("   ✅ 可用模型API正常")
            else:
                print(f"   ❌ API返回失败: {data.get('error', 'Unknown error')}")
        else:
            print(f"   ❌ 可用模型API失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
    
    # 3. 测试模型配置API
    print("\n3. 测试模型配置API")
    try:
        response = requests.get(f"{base_url}/agents/{agent_id}/model-config")
        print(f"   URL: {base_url}/agents/{agent_id}/model-config")
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                config = data.get('model_config', {})
                print(f"   ✅ 模型ID: {config.get('model_id', 'N/A')}")
                print(f"   ✅ 温度: {config.get('temperature', 'N/A')}")
                print(f"   ✅ 最大令牌: {config.get('max_tokens', 'N/A')}")
                print("   ✅ 模型配置API正常")
            else:
                print(f"   ❌ API返回失败: {data.get('error', 'Unknown error')}")
        else:
            print(f"   ❌ 模型配置API失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
    
    # 4. 测试知识库文档API
    print("\n4. 测试知识库文档API")
    try:
        response = requests.get(f"{base_url}/ai-agent/knowledge-documents")
        print(f"   URL: {base_url}/ai-agent/knowledge-documents")
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            documents = data.get('documents', [])
            print(f"   ✅ 文档数量: {len(documents)}")
            for i, doc in enumerate(documents[:2]):
                print(f"   ✅ 文档{i+1}: {doc.get('title', 'N/A')}")
            print("   ✅ 知识库文档API正常")
        else:
            print(f"   ❌ 知识库文档API失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
    
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("如果所有API都返回200状态码，说明后端正常")
    print("如果前端页面仍然空白，问题可能在于:")
    print("1. 前端路由配置")
    print("2. 组件加载问题") 
    print("3. JavaScript错误")
    print("4. 网络请求被阻止")
    
    print("\n🔧 调试建议:")
    print("1. 打开浏览器开发者工具")
    print("2. 查看Console标签页的错误信息")
    print("3. 查看Network标签页的网络请求")
    print("4. 检查页面右上角是否有调试信息显示")
    
    print(f"\n🌐 测试页面:")
    print(f"调试页面: file:///E:/workspace/AI_system/frontend/debug-unified-chat.html")
    print(f"实际页面: http://*************:3000/utilities/daily/unified-chat?agent_id={agent_id}")

if __name__ == "__main__":
    test_unified_chat_apis()
