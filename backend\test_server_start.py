#!/usr/bin/env python3
"""
测试服务器启动脚本
用于验证所有模块是否能正常导入和启动
"""

import sys
import os
import traceback

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_imports():
    """测试所有关键模块的导入"""
    print("🔍 测试模块导入...")
    
    try:
        # 测试主应用导入
        print("  ✓ 导入主应用模块...")
        from main import app
        print("  ✅ 主应用模块导入成功")
        
        # 测试API路由导入
        print("  ✓ 导入API路由...")
        from app.api import api_router
        print("  ✅ API路由导入成功")
        
        # 测试智能体API导入
        print("  ✓ 导入智能体API...")
        from app.api.v1.ai_agent import router as ai_agent_router
        print("  ✅ 智能体API导入成功")
        
        # 测试数据库连接
        print("  ✓ 测试数据库连接...")
        from app.core.database import get_db_manager
        db_manager = get_db_manager()
        print(f"  ✅ 数据库连接成功 ({'PostgreSQL' if db_manager.use_postgres else 'SQLite'})")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 导入失败: {str(e)}")
        print(f"  📋 错误详情:")
        traceback.print_exc()
        return False

def test_file_processing_deps():
    """测试文件处理依赖"""
    print("\n📁 测试文件处理依赖...")
    
    deps = [
        ("openpyxl", "Excel处理"),
        ("pandas", "数据分析"),
        ("PyPDF2", "PDF处理"),
        ("docx", "Word处理"),
        ("chardet", "编码检测")
    ]
    
    missing_deps = []
    
    for dep, desc in deps:
        try:
            __import__(dep)
            print(f"  ✅ {desc} ({dep}) - 已安装")
        except ImportError:
            print(f"  ⚠️ {desc} ({dep}) - 未安装")
            missing_deps.append(dep)
    
    if missing_deps:
        print(f"\n📦 缺失依赖: {', '.join(missing_deps)}")
        print("💡 安装命令: pip install " + " ".join(missing_deps))
        return False
    
    return True

def test_database_operations():
    """测试数据库基本操作"""
    print("\n🗄️ 测试数据库操作...")
    
    try:
        from app.core.database import get_db_manager
        db_manager = get_db_manager()
        
        # 测试简单查询
        result = db_manager.execute_query("SELECT 1 as test")
        if result:
            print("  ✅ 数据库查询测试成功")
            return True
        else:
            print("  ❌ 数据库查询返回空结果")
            return False
            
    except Exception as e:
        print(f"  ❌ 数据库操作失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 AI系统服务器启动测试")
    print("=" * 50)
    
    # 测试模块导入
    import_success = test_imports()
    
    # 测试文件处理依赖
    deps_success = test_file_processing_deps()
    
    # 测试数据库操作
    db_success = test_database_operations()
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    print(f"  模块导入: {'✅ 成功' if import_success else '❌ 失败'}")
    print(f"  文件处理依赖: {'✅ 完整' if deps_success else '⚠️ 缺失'}")
    print(f"  数据库操作: {'✅ 正常' if db_success else '❌ 异常'}")
    
    if import_success and db_success:
        print("\n🎉 服务器可以正常启动！")
        print("💡 启动命令: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload")
        return True
    else:
        print("\n⚠️ 服务器启动可能有问题，请检查上述错误")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
