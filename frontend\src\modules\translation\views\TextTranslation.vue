<template>
  <div class="text-translation-container">
    <page-header-gradient
      title="文本翻译"
      subtitle="快速翻译文本内容，支持70+种语言和专业领域 (v2.0)"
      :actions="[]"
    />

    <el-card class="translation-card">
      <el-row :gutter="24">
        <!-- 翻译控制区域 -->
        <el-col :span="24" class="control-area">
          <div class="language-controls">
            <div class="source-language">
              <el-select
                v-model="sourceLanguage"
                style="width: 160px"
                @change="handleSourceLanguageChange"
                size="large"
              >
                <el-option
                  v-for="lang in languages"
                  :key="lang.code || lang"
                  :value="lang.code || lang"
                  :label="lang.name || lang"
                >
                  {{ lang.name || lang }}
                </el-option>
              </el-select>
              <span v-if="sourceLanguage === 'auto' && detectedLanguage" style="margin-left:8px;color:#888;">
                (检测到: {{ getLanguageName(detectedLanguage) }})
              </span>
            </div>
            
            <div class="swap-button">
              <el-button circle @click="swapLanguages">
                <el-icon><Switch /></el-icon>
              </el-button>
            </div>

            <div class="target-language">
              <el-select
                v-model="targetLanguage"
                style="width: 160px"
                @change="handleTargetLanguageChange"
                size="large"
              >
                <el-option
                  v-for="lang in languages"
                  :key="lang.code || lang"
                  :value="lang.code || lang"
                  :label="lang.name || lang"
                >
                  {{ lang.name || lang }}
                </el-option>
              </el-select>
            </div>

            <div class="domain-control">
              <el-select
                v-model="domain"
                style="width: 120px"
                placeholder="翻译领域"
                @change="handleDomainChange"
                size="large"
              >
                <el-option value="general" label="通用">通用</el-option>
                <el-option value="technology" label="科技">科技</el-option>
                <el-option value="medical" label="医学">医学</el-option>
                <el-option value="legal" label="法律">法律</el-option>
                <el-option value="finance" label="金融">金融</el-option>
              </el-select>
            </div>
            
            <!-- 添加Ollama模型选择器 -->
            <div class="model-control">
              <el-select
                v-model="selectedModel"
                style="width: 180px"
                placeholder="选择Ollama模型"
                @change="handleModelChange"
                size="large"
                :loading="loadingModels"
              >
                <template #suffix>
                  <el-icon v-if="loadingModels"><Loading /></el-icon>
                  <el-icon v-else-if="ollamaStatus === 'connected'" style="color: #52c41a"><CircleCheck /></el-icon>
                  <el-icon v-else-if="ollamaStatus === 'disconnected'" style="color: #f5222d"><CircleClose /></el-icon>
                  <el-icon v-else><QuestionFilled /></el-icon>
                </template>
                <el-option
                  v-for="model in availableModels"
                  :key="model"
                  :value="model"
                  :label="String(getModelDisplayName(model))"
                >
                  {{ getModelDisplayName(model) }}
                </el-option>
              </el-select>
              <el-tooltip content="模型设置">
                <el-button text @click="showModelSettings = true">
                  <el-icon><Setting /></el-icon>
                </el-button>
              </el-tooltip>
            </div>
            
            <!-- 添加异步翻译开关 -->
            <div class="async-control">
              <el-tooltip content="长文本使用异步翻译">
                <el-switch
                  v-model="useAsyncTranslation"
                  active-text="异步"
                  inactive-text="同步"
                />
              </el-tooltip>
            </div>

            <!-- 快捷语言对选择 -->
            <div class="quick-language-pairs">
              <span class="quick-pairs-label">快捷选择:</span>
              <el-button-group size="small">
                <el-button
                  v-for="pair in commonLanguagePairs.slice(0, 5)"
                  :key="`${pair.source}-${pair.target}`"
                  @click="setLanguagePair(pair.source, pair.target)"
                  :type="isCurrentPair(pair.source, pair.target) ? 'primary' : 'default'"
                >
                  {{ pair.label }}
                </el-button>
              </el-button-group>
              <el-button size="small" type="info" @click="forceRefreshLanguages" style="margin-left: 12px;">
                刷新语言列表 ({{ languages.length }})
              </el-button>
              <el-tooltip :content="`当前语言列表: ${languages.slice(0, 5).map(l => l.name).join(', ')}...`">
                <el-button size="small" type="success" style="margin-left: 8px;">
                  语言状态
                </el-button>
              </el-tooltip>
            </div>
          </div>
        </el-col>
        
        <!-- 源文本区域 -->
        <el-col :span="12">
          <div class="text-panel source-panel">
            <div class="panel-header">
              <span class="panel-title">
                {{ sourceLanguage === 'auto' ? '自动检测' : getLanguageName(sourceLanguage) }}
              </span>
              <div class="panel-actions">
                <el-tooltip content="清空">
                  <el-button text @click="clearSourceText">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="粘贴">
                  <el-button text @click="pasteFromClipboard">
                    <el-icon><DocumentCopy /></el-icon>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="语音输入" v-if="isSpeechRecognitionSupported">
                  <el-button
                    text
                    @click="toggleVoiceInput('source')"
                    :class="{ 'active-voice': voiceInputActive && voiceInputTarget === 'source' }"
                  >
                    <el-icon><Microphone /></el-icon>
                  </el-button>
                </el-tooltip>
              </div>
            </div>

            <div class="voice-input-indicator" v-if="voiceInputActive && voiceInputTarget === 'source'">
              <el-badge value="正在聆听..." type="primary" />
            </div>

            <el-input
              v-model="sourceText"
              type="textarea"
              :placeholder="`请输入要翻译的文本...`"
              :rows="15"
              clearable
              class="translation-textarea"
              :autosize="{ minRows: 15, maxRows: 25 }"
              @input="handleSourceTextChange"
            />

            <div class="panel-footer">
              <div class="footer-left">
                <span class="character-count">{{ sourceText.length }} 字符</span>
                <div class="quick-actions" v-if="sourceText.trim()">
                  <el-tooltip content="清空文本">
                    <el-button text size="small" @click="clearSourceText">
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="粘贴剪贴板">
                    <el-button text size="small" @click="pasteFromClipboard">
                      <el-icon><DocumentCopy /></el-icon>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="示例文本">
                    <el-button text size="small" @click="insertSampleText">
                      示例
                    </el-button>
                  </el-tooltip>
                </div>
              </div>
              <div class="footer-right">
                <el-button
                  type="primary"
                  @click="translate"
                  :loading="isTranslating"
                  :disabled="!sourceText.trim()"
                  size="default"
                >
                  {{ isTranslating ? '翻译中...' : '翻译' }}
                </el-button>
              </div>
            </div>
          </div>
        </el-col>
        
        <!-- 翻译结果区域 -->
        <el-col :span="12">
          <div class="text-panel result-panel">
            <div class="panel-header">
              <span class="panel-title">{{ getLanguageName(targetLanguage) }}</span>
              <div class="panel-actions">
                <el-tooltip content="复制">
                  <el-button text @click="copyTranslatedText" :disabled="!translatedText">
                    <el-icon><CopyDocument /></el-icon>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="朗读" v-if="isSpeechSynthesisSupported">
                  <el-button
                    text
                    @click="speakTranslatedText"
                    :disabled="!translatedText.trim()"
                  >
                    <el-icon><VideoPlay /></el-icon>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="下载">
                  <el-button
                    text
                    @click="downloadTranslation"
                    :disabled="!translatedText.trim()"
                  >
                    <el-icon><Download /></el-icon>
                  </el-button>
                </el-tooltip>
              </div>
            </div>

            <div class="translation-result-area">
              <!-- 翻译进度指示器 -->
              <div v-if="isTranslating" class="translation-progress">
                <div class="progress-header">
                  <el-icon class="spinning"><Loading /></el-icon>
                  <span>{{ useAsyncTranslation ? '异步翻译进行中...' : '正在翻译...' }}</span>
                </div>
                <div class="progress-details">
                  <div class="progress-item">
                    <span>模型:</span>
                    <el-tag size="small">{{ getModelDisplayName(selectedModel) }}</el-tag>
                  </div>
                  <div class="progress-item">
                    <span>文本长度:</span>
                    <span>{{ sourceText.length }} 字符</span>
                  </div>
                  <div class="progress-item" v-if="translationTaskId">
                    <span>任务ID:</span>
                    <span class="task-id">{{ translationTaskId.substring(0, 8) }}...</span>
                  </div>
                </div>
                <el-progress
                  :percentage="50"
                  :indeterminate="true"
                  :duration="3"
                  status="success"
                />
              </div>

              <el-input
                v-model="translatedText"
                type="textarea"
                placeholder="翻译结果将显示在这里..."
                :rows="15"
                readonly
                class="translation-textarea result-textarea"
                :class="{ 'has-content': translatedText.trim() }"
                :autosize="{ minRows: 15, maxRows: 25 }"
              />

              <!-- 翻译质量指示器 -->
              <div v-if="translatedText.trim()" class="translation-quality">
                <div class="quality-indicator">
                  <el-icon><CircleCheck /></el-icon>
                  <span>翻译完成</span>
                </div>
                <div class="quality-score">
                  <el-rate :model-value="4" disabled size="small" show-text />
                </div>
              </div>
            </div>

            <div class="panel-footer">
              <span class="character-count">{{ translatedText.length }} 字符</span>
              <div v-if="stats.translationTime > 0" class="translation-stats">
                翻译用时: {{ stats.translationTime }}秒
              </div>
              <el-button
                link
                @click="showFeedbackModal"
                :disabled="!translatedText.trim()"
              >
                报告翻译问题
              </el-button>
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 快捷键提示 -->
      <el-divider content-position="left">
        <el-icon><InfoFilled /></el-icon>
        快捷键提示
      </el-divider>

      <el-row>
        <el-col :span="24">
          <div class="keyboard-shortcuts">
            <div class="shortcuts-grid">
              <div class="shortcut-item">
                <kbd>Ctrl</kbd> + <kbd>Enter</kbd>
                <span>执行翻译</span>
              </div>
              <div class="shortcut-item">
                <kbd>Ctrl</kbd> + <kbd>Shift</kbd> + <kbd>C</kbd>
                <span>复制结果</span>
              </div>
              <div class="shortcut-item">
                <kbd>Ctrl</kbd> + <kbd>Shift</kbd> + <kbd>V</kbd>
                <span>粘贴并翻译</span>
              </div>
              <div class="shortcut-item">
                <kbd>Ctrl</kbd> + <kbd>Shift</kbd> + <kbd>S</kbd>
                <span>交换语言</span>
              </div>
              <div class="shortcut-item">
                <kbd>Ctrl</kbd> + <kbd>Shift</kbd> + <kbd>D</kbd>
                <span>清空文本</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 翻译历史记录 -->
      <el-divider content-position="left">翻译历史</el-divider>

      <el-row>
        <el-col :span="24">
          <el-empty v-if="translationHistory.length === 0" description="暂无翻译历史记录" />

          <div v-else class="translation-history">
            <el-card
              v-for="item in translationHistory.slice(0, 5)"
              :key="item.id"
              class="history-card"
              shadow="hover"
            >
              <template #header>
                <div class="history-header">
                  <div class="language-pair">
                    <el-tag size="small" type="primary">{{ getLanguageName(item.source_language) }}</el-tag>
                    <el-icon class="arrow-icon"><ArrowRight /></el-icon>
                    <el-tag size="small" type="success">{{ getLanguageName(item.target_language) }}</el-tag>
                  </div>
                  <div class="history-time">
                    <el-icon><Clock /></el-icon>
                    {{ formatTime(item.timestamp || item.created_at) }}
                  </div>
                </div>
              </template>

              <div class="history-content">
                <div class="text-section">
                  <div class="text-label">原文</div>
                  <div class="text-content source-text">{{ item.source_text || item.original_text }}</div>
                </div>

                <div class="text-section">
                  <div class="text-label">译文</div>
                  <div class="text-content translated-text">{{ item.translated_text }}</div>
                </div>
              </div>

              <template #footer>
                <div class="history-actions">
                  <el-button size="small" text @click="loadHistoryItem(item)">
                    <el-icon><Refresh /></el-icon>
                    重新翻译
                  </el-button>
                  <el-button size="small" text @click="copyHistoryText(item.translated_text)">
                    <el-icon><CopyDocument /></el-icon>
                    复制译文
                  </el-button>
                </div>
              </template>
            </el-card>
          </div>
        </el-col>
      </el-row>
    </el-card>
    
    <!-- 翻译反馈弹窗 -->
    <el-dialog
      v-model="feedbackVisible"
      title="翻译反馈"
      width="600px"
    >
      <p>请指出翻译问题，帮助我们改进翻译质量：</p>
      <el-input v-model="feedbackText" type="textarea" :rows="4" placeholder="请描述翻译问题..." />

      <div class="feedback-options" style="margin-top: 16px;">
        <el-checkbox-group v-model="feedbackOptions">
          <el-checkbox value="inaccurate">翻译不准确</el-checkbox>
          <el-checkbox value="grammar">语法错误</el-checkbox>
          <el-checkbox value="terminology">术语翻译错误</el-checkbox>
          <el-checkbox value="style">风格不适合</el-checkbox>
          <el-checkbox value="other">其他问题</el-checkbox>
        </el-checkbox-group>
      </div>

      <template #footer>
        <el-button @click="feedbackVisible = false">取消</el-button>
        <el-button type="primary" @click="submitFeedback">确定</el-button>
      </template>
    </el-dialog>
    
    <!-- 模型设置弹窗 -->
    <el-dialog
      v-model="showModelSettings"
      title="翻译模型设置"
      width="600px"
    >
      <div class="settings-form">
        <div class="settings-item">
          <div class="settings-label">当前模型状态:</div>
          <el-tag :type="ollamaStatus === 'connected' ? 'success' : ollamaStatus === 'loading' ? 'warning' : 'danger'">
            {{ ollamaStatus === 'connected' ? '已连接' : ollamaStatus === 'loading' ? '连接中...' : '未连接' }}
          </el-tag>
        </div>

        <div class="settings-item">
          <div class="settings-label">可用模型:</div>
          <el-select v-model="selectedModel" placeholder="选择翻译模型" style="width: 100%;">
            <el-option
              v-for="model in availableModels"
              :key="model"
              :label="getModelDisplayName(model)"
              :value="model"
            />
          </el-select>
          <div class="settings-hint">选择用于翻译的AI模型</div>
        </div>

        <div class="settings-item">
          <div style="display: flex; gap: 10px;">
            <el-button @click="fetchOllamaModels" type="primary" :loading="loadingModels">
              刷新模型列表
            </el-button>
            <el-button @click="testModelConnection" :loading="loadingModels">
              测试模型
            </el-button>
          </div>
        </div>

        <div class="settings-item" v-if="availableModels.length === 0">
          <el-alert
            title="未找到可用模型"
            description="请确保后端服务正常运行，并且已安装Ollama模型"
            type="warning"
            show-icon
            :closable="false"
          />
        </div>

        <div class="settings-item">
          <div class="settings-hint">
            <span v-if="ollamaStatus === 'connected'" style="color: #52c41a">
              <el-icon><CircleCheck /></el-icon> 后端服务连接正常
            </span>
            <span v-else-if="ollamaStatus === 'disconnected'" style="color: #f5222d">
              <el-icon><CircleClose /></el-icon> 后端服务连接失败
            </span>
            <span v-else>
              <el-icon v-if="loadingModels"><Loading /></el-icon> 正在检查服务状态...
            </span>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="showModelSettings = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { defineComponent, ref, computed, watch, onMounted, reactive, onBeforeUnmount } from 'vue';
import { ElMessage, ElNotification } from 'element-plus';
import { useLanguages } from '@/composables/useLanguages';
import {
  Switch,
  Microphone,
  Delete,
  VideoPlay,
  CopyDocument,
  Download,
  Clock,
  Refresh,
  DocumentCopy,
  ArrowRight,
  Loading,
  CircleCheck,
  CircleClose,
  QuestionFilled,
  Setting,
  InfoFilled
} from '@element-plus/icons-vue';
import PageHeaderGradient from '@/components/PageHeaderGradient.vue';
import {
  translateText,
  translateTextAsync,
  getTextTranslationResult,
  getSupportedLanguages,
  getTextTranslationHistory
} from '@/api/translation';

export default defineComponent({
  name: 'TextTranslation',
  components: {
    // Element Plus 图标组件
    Switch,
    Microphone,
    Delete,
    VideoPlay,
    CopyDocument,
    Download,
    DocumentCopy,
    ArrowRight,
    Loading,
    CircleCheck,
    CircleClose,
    QuestionFilled,
    Setting,
    InfoFilled,
    // 自定义组件
    PageHeaderGradient
  },
  setup() {
    // 使用全局语言配置
    const {
      languages,
      languageCategories,
      popularLanguages,
      commonLanguagePairs,
      filteredLanguages,
      popularLanguageList,
      languagesByCategory,
      languageSearchQuery,
      setSearchQuery,
      clearSearch,
      getLanguageName,
      normalizeLanguageCode,
      isValidLanguageCode,
      validateLanguagePair,
      getRecommendedPairs,
      updateLanguagesFromAPI,
      resetToDefault
    } = useLanguages();

    // 翻译历史记录（本地实现）
    const translationHistory = ref([]);

    // 格式化时间
    const formatTime = (date) => {
      if (!date) return '';
      const d = new Date(date);
      return d.toLocaleString('zh-CN');
    };

    // 加载翻译历史
    const loadTranslationHistory = async () => {
      try {
        const response = await getTextTranslationHistory();
        if (response && response.history) {
          translationHistory.value = response.history;
        }
      } catch (error) {
        console.error('加载翻译历史失败:', error);
      }
    };

    // 添加翻译记录
    const addTranslationRecord = (record) => {
      translationHistory.value.unshift({
        ...record,
        id: Date.now(),
        timestamp: new Date().toISOString()
      });
      // 限制历史记录数量
      if (translationHistory.value.length > 100) {
        translationHistory.value = translationHistory.value.slice(0, 100);
      }
    };

    // 语言处理函数现在从 useLanguages 中获取

    // 语言列表现在从全局配置中获取
    console.log('使用全局语言配置，共', languages.value.length, '种语言');
    console.log('前5种语言:', languages.value.slice(0, 5));

    // 语言分类、搜索等功能现在从全局配置中获取
    
    // 源语言和目标语言
    const sourceLanguage = ref('auto');
    const targetLanguage = ref('en-US');
    
    // 文本内容
    const sourceText = ref('');
    const translatedText = ref('');
    
    // 翻译状态
    const isTranslating = ref(false);
    
    // 语音输入状态
    const voiceInputActive = ref(false);
    const voiceInputTarget = ref('');
    
    // 专业领域
    const domain = ref('general');
    
    // Ollama模型相关
    const availableModels = ref([]);
    const selectedModel = ref('');
    const ollamaStatus = ref('loading'); // 'loading', 'connected', 'disconnected'
    const loadingModels = ref(false);
    const showModelSettings = ref(false); // 模型设置弹窗显示状态
    
    // 翻译统计
    const stats = reactive({
      characters: 0,
      translationTime: 0
    });
    
    // 反馈相关
    const feedbackVisible = ref(false);
    const feedbackText = ref('');
    const feedbackOptions = ref([]);
    
    // 浏览器能力检测
    const isSpeechRecognitionSupported = ref(false);
    const isSpeechSynthesisSupported = ref(false);
    
    // 检测到的语言
    const detectedLanguage = ref('');
    
    // 添加异步翻译相关的状态
    const useAsyncTranslation = ref(true); // 默认使用异步翻译
    const translationTaskId = ref(null); // 翻译任务ID
    const translationPollingInterval = ref(null); // 轮询间隔ID
    
    // 加载支持的语言列表（使用全局配置）
    const loadLanguages = async () => {
      try {
        console.log('使用全局语言配置，共', languages.value.length, '种语言');

        // 可选：从后端获取额外的语言配置
        try {
          const response = await getSupportedLanguages();
          if (response && response.languages) {
            // 使用全局配置的更新方法
            await updateLanguagesFromAPI(response.languages);
          }
        } catch (apiError) {
          console.log('后端语言API不可用，使用本地配置:', apiError.message);
        }
      } catch (error) {
        console.error('加载语言配置失败:', error);
        // 使用默认配置，不影响语言列表
      }
    };
    
    // 添加到历史记录
    const addToHistory = (record) => {
      addTranslationRecord(record);
    };
    
    // 加载Ollama模型列表
    const fetchOllamaModels = async () => {
      loadingModels.value = true;
      ollamaStatus.value = 'loading';
      
      try {
        console.log('开始获取Ollama模型列表...');
        
        // 使用后端API获取模型列表
        const apiUrl = '/api/v1/ollama/models';
        console.log(`尝试连接到后端API: ${apiUrl}`);

        // 设置超时
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);

        const response = await fetch(apiUrl, {
          signal: controller.signal,
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        });
        
        clearTimeout(timeoutId);
        
        if (!response.ok) {
          console.error(`响应不成功: ${response.status}`);
          throw new Error(`API响应错误: ${response.status}`);
        }
        
        const data = await response.json();

        if (!data.success || !data.models || !Array.isArray(data.models) || data.models.length === 0) {
          console.error(`后端API返回了空的模型列表:`, data);
          throw new Error(data.error || '未找到可用模型');
        }
        
        // 成功获取到模型列表
        const models = data.models.map((model) => model.name);
        
        console.log(`成功获取到 ${models.length} 个模型`);
        console.log('模型列表:', models.join(', '));
        
        // 保存当前选择的模型
        const currentModel = selectedModel.value;
        
        availableModels.value = models;
        
        // 如果当前模型在新列表中存在，则保持选择，否则选择第一个
        if (currentModel && models.includes(currentModel)) {
          // 保持当前选择
          console.log(`保持当前选择的模型: ${currentModel}`);
        } else if (models.length > 0) {
          selectedModel.value = models[0];
          console.log(`选择新的模型: ${models[0]}`);
        }
        
        ollamaStatus.value = 'connected';
        ElMessage.success('成功连接到Ollama服务');
      } catch (error) {
        console.error('获取Ollama模型列表失败:', error);
        ollamaStatus.value = 'disconnected';
        
        // 创建测试模型列表
        console.log('使用测试模型列表');
        const testModels = [
          'deepseek-r1:8b',
          'llama2:13b',
          'gemma:7b',
          'mistral'
        ];
        
        // 保存当前选择的模型
        const currentModel = selectedModel.value;
        
        availableModels.value = testModels;
        
        // 如果当前模型在测试列表中存在，则保持选择，否则选择第一个
        if (currentModel && testModels.includes(currentModel)) {
          // 保持当前选择
          console.log(`保持当前选择的模型: ${currentModel}`);
        } else {
          selectedModel.value = testModels[0];
          console.log(`选择新的模型: ${testModels[0]}`);
        }
        
        ElMessage.warning('无法连接到Ollama服务，请确保Ollama已安装并运行');
      } finally {
        loadingModels.value = false;
      }
    };
    
    // 轮询翻译结果
    const pollTranslationResult = (taskId) => {
      if (!taskId) {
        console.error('轮询翻译结果失败: 无效的任务ID');
        ElMessage.error('翻译失败，请重试');
        isTranslating.value = false;
        return;
      }
      
      console.log(`开始轮询翻译结果，任务ID: ${taskId}`);
      translationTaskId.value = taskId;

      // 清除现有的轮询
      if (translationPollingInterval.value) {
        clearInterval(translationPollingInterval.value);
      }

      // 轮询计数器和超时设置
      let pollCount = 0;
      const maxPolls = 60; // 最多轮询60次 (60 * 2秒 = 2分钟)

      // 设置轮询间隔
      translationPollingInterval.value = setInterval(async () => {
        pollCount++;
        console.log(`轮询翻译结果: ${taskId} (第${pollCount}次)`);

        // 检查超时
        if (pollCount > maxPolls) {
          console.error('轮询超时，停止轮询');
          clearInterval(translationPollingInterval.value);
          translationPollingInterval.value = null;
          isTranslating.value = false;
          ElMessage.error('翻译超时，请重试');
          return;
        }
        try {
          console.log(`轮询翻译结果: ${taskId}`);
          // 添加错误捕获
          let rawResponse;
          
          try {
            rawResponse = await getTextTranslationResult(taskId);
            console.log('原始API响应:', rawResponse);
            
            // 直接检查控制台输出的原始响应数据
            const consoleOutput = JSON.stringify(rawResponse);
            console.log('响应数据JSON字符串:', consoleOutput);
          } catch (fetchError) {
            console.error('获取翻译结果API调用异常:', fetchError);
            rawResponse = { success: false, error: '请求失败' };
          }
          
          // 使用处理后的响应作为结果
          const result = rawResponse;
          console.log('翻译结果数据:', result);
          
          // 检查响应是否有效
          if (!result) {
            console.warn('翻译结果响应完全为空');
            return; // 继续轮询
          }
          
          // 特殊处理未完成状态
          if (result.status === 'pending' || result.status === 'processing') {
            console.log(`翻译仍在处理中，状态: ${result.status}, 进度: ${result.progress || 0}%`);
            return; // 继续轮询
          }

          // 清除轮询
          console.log('停止轮询，最终状态:', result.status);
          clearInterval(translationPollingInterval.value);
          translationPollingInterval.value = null;
          
          // 检查是否有明确的成功标志
          if (result.success === true) {
            console.log('翻译成功:', result);
            console.log('result.status:', result.status);
            console.log('result.result:', result.result);

            // 检查翻译结果，支持不同的响应格式
            let translatedTextResult = null;
            let detectedLang = null;

            if (result.status === 'completed' && result.result) {
              // 异步翻译完成的格式
              console.log('使用异步翻译完成格式');
              translatedTextResult = result.result.translated_text;
              // 优先使用 detected_language，如果没有则使用 source_language（但排除 'auto'）
              detectedLang = result.result.detected_language ||
                           (result.result.source_language !== 'auto' ? result.result.source_language : null);
              console.log('异步翻译结果:', translatedTextResult);
              console.log('检测到的语言:', detectedLang);
            } else if (result.translated_text) {
              // 直接格式
              console.log('使用直接格式');
              translatedTextResult = result.translated_text;
              detectedLang = result.detected_language;
              console.log('直接翻译结果:', translatedTextResult);
            } else {
              console.warn('未找到翻译结果，result.status:', result.status, 'result.result:', result.result);
            }

            if (translatedTextResult) {
              translatedText.value = translatedTextResult;
              
              // 如果是自动检测语言，更新检测到的语言
              if (sourceLanguage.value === 'auto' && detectedLang) {
                detectedLanguage.value = normalizeLanguageCode(detectedLang);
                console.log(`检测到语言: ${detectedLanguage.value}`);
              }

              // 将结果添加到历史记录
              addToHistory({
                original_text: sourceText.value,
                translated_text: translatedTextResult,
                source_language: detectedLang || sourceLanguage.value,
                target_language: targetLanguage.value,
                domain: domain.value,
                created_at: new Date().toISOString()
              });
            } else {
              console.error('翻译成功但没有翻译结果');
              ElMessage.error('翻译结果不完整，请重试');
            }
          } else if (result.success === false) {
            // 翻译失败
            console.error('翻译失败:', result.error);
            ElMessage.error(`翻译失败: ${result.error || '未知错误'}`);
          } else if (result.translated_text || (result.result && result.result.translated_text)) {
            // 没有明确的成功标志，但有翻译结果，视为成功
            console.log('翻译完成，没有明确的成功标志但有结果:', result);

            // 支持不同的响应格式
            let translatedTextResult = result.translated_text || result.result.translated_text;
            let detectedLang = result.detected_language || (result.result && result.result.source_language);

            translatedText.value = translatedTextResult;

            // 如果是自动检测语言，更新检测到的语言
            if (sourceLanguage.value === 'auto' && detectedLang) {
              detectedLanguage.value = normalizeLanguageCode(detectedLang);
              console.log(`检测到语言: ${detectedLanguage.value}`);
            }

            // 将结果添加到历史记录
            addToHistory({
              original_text: sourceText.value,
              translated_text: translatedTextResult,
              source_language: detectedLang || sourceLanguage.value,
              target_language: targetLanguage.value,
              domain: domain.value,
              created_at: new Date().toISOString()
            });
          } else if (result.error) {
            // 有错误信息
            console.error('翻译失败:', result.error);
            ElMessage.error(`翻译失败: ${result.error || '未知错误'}`);
          } else {
            // 无法确定结果状态
            console.error('无法确定翻译结果状态:', result);
            ElMessage.error('无法获取翻译结果，请重试');
          }
        } catch (error) {
          console.error('轮询翻译结果失败:', error);
          ElMessage.error('获取翻译结果失败，请重试');
          clearInterval(translationPollingInterval.value);
          translationPollingInterval.value = null;
        } finally {
          if (!translationPollingInterval.value) {
            isTranslating.value = false;
          }
        }
      }, 2000); // 每2秒轮询一次
    };
    
    // 根据需要自动翻译
    const autoTranslateIfNeeded = () => {
      if (sourceText.value.trim()) {
        translate();
      }
    };
    
    // 切换代理服务器
    const toggleProxyServer = (checked) => {
      useProxyServer.value = checked;
      localStorage.setItem('use_proxy_server', checked.toString());
      
      // 重新加载模型
      fetchOllamaModels();
    };
    
    // 测试模型连接
    const testModelConnection = async () => {
      if (!selectedModel.value) {
        ElMessage.warning('请先选择一个模型');
        return;
      }

      loadingModels.value = true;

      try {
        // 使用后端API测试模型
        const response = await fetch('/api/v1/ollama/generate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model: selectedModel.value,
            prompt: 'Hello',
            system: '请用中文回复"测试成功"',
            temperature: 0.1,
            max_tokens: 50
          }),
        });

        if (!response.ok) {
          throw new Error(`测试请求失败: ${response.status}`);
        }

        const data = await response.json();

        if (data.success) {
          ElMessage.success(`模型 ${selectedModel.value} 测试成功`);
          ollamaStatus.value = 'connected';
        } else {
          throw new Error(data.error || '模型测试失败');
        }
      } catch (error) {
        console.error('模型测试失败:', error);
        ElMessage.error(`模型测试失败: ${error.message}`);
        ollamaStatus.value = 'disconnected';
      } finally {
        loadingModels.value = false;
      }
    };
    

    
    // 键盘快捷键处理
    const handleKeyboardShortcuts = (event) => {
      // Ctrl/Cmd + Enter: 翻译
      if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
        event.preventDefault();
        if (sourceText.value.trim()) {
          translate();
        }
      }

      // Ctrl/Cmd + Shift + C: 复制翻译结果
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'C') {
        event.preventDefault();
        copyTranslatedText();
      }

      // Ctrl/Cmd + Shift + V: 粘贴并翻译
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'V') {
        event.preventDefault();
        pasteFromClipboard();
      }

      // Ctrl/Cmd + Shift + S: 交换语言
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'S') {
        event.preventDefault();
        swapLanguages();
      }

      // Ctrl/Cmd + Shift + D: 清空文本
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'D') {
        event.preventDefault();
        clearSourceText();
      }
    };

    // 强制刷新语言列表
    const forceRefreshLanguages = () => {
      // 重置为默认配置
      resetToDefault();
      console.log('强制刷新语言列表完成，共', languages.value.length, '种语言');
      console.log('前10种语言:', languages.value.slice(0, 10).map(l => `${l.code}: ${l.name}`));
      ElMessage.success(`语言列表已刷新，共 ${languages.value.length} 种语言`);
    };

    // 浏览器能力检测
    onMounted(async () => {
      // 初始化时，标准化默认语言代码
      sourceLanguage.value = normalizeLanguageCode('auto');
      targetLanguage.value = normalizeLanguageCode('en');

      // 强制刷新语言列表以确保显示正确
      forceRefreshLanguages();

      // 加载语言列表
      try {
        await loadLanguages();
        console.log('当前语言列表:', languages.value.slice(0, 10)); // 显示前10个语言用于调试
      } catch (error) {
        console.error('加载语言列表失败:', error);
        ElMessage.error('无法加载支持的语言列表');
      }

      // 加载模型列表
      try {
        await fetchOllamaModels();
      } catch (error) {
        console.error('加载模型列表失败:', error);
        ElMessage.error('无法加载模型列表，请检查后端服务');
      }

      // 加载翻译历史
      await loadTranslationHistory();

      // 检查浏览器支持
      checkBrowserSupport();

      // 添加键盘快捷键监听
      document.addEventListener('keydown', handleKeyboardShortcuts);

      // 检查Ollama状态
      // 注释掉未定义的函数调用
      // checkOllamaStatus();
    });

    // 清理事件监听器
    onBeforeUnmount(() => {
      document.removeEventListener('keydown', handleKeyboardShortcuts);

      // 清理轮询
      if (translationPollingInterval.value) {
        clearInterval(translationPollingInterval.value);
      }
    });
    
    // 监听选择的模型变化，保存到本地存储
    watch(selectedModel, (newModel) => {
      if (newModel) {
        localStorage.setItem('selected_model', newModel);
      }
    });
    

    
    // 实时翻译延迟
    let translationTimeout = null;
    
    // 修改翻译函数，支持异步翻译
    const translate = async () => {
      if (!sourceText.value.trim()) return;
      
      isTranslating.value = true;
      translatedText.value = '';
      
      try {
        let result;
        
        // 使用标准化的语言代码
        const source = sourceLanguage.value;
        const target = targetLanguage.value;
        
        console.log(`翻译: ${source} -> ${target}, 使用模式: ${useAsyncTranslation.value ? '异步' : '同步'}`);
        
        if (useAsyncTranslation.value) {
          // 异步翻译
          try {
            console.log('准备发送异步翻译请求，参数:', {
              text: sourceText.value,
              source_lang: source,
              target_lang: target,
              model: selectedModel.value
            });
            
            // 移除domain参数，根据后端日志显示它不被支持
            const response = await translateTextAsync(
              sourceText.value,
              source,
              target,
              undefined, // 不传递domain参数
              'standard',
              selectedModel.value
            );
            
            console.log('异步翻译响应:', response);
            
            // 增强检查逻辑，处理不同的响应结构
            let taskId = null;
            
            // 尝试从不同位置获取task_id
            if (response && response.task_id) {
              // 直接在响应对象中
              taskId = response.task_id;
              console.log(`从response.task_id获取到任务ID: ${taskId}`);
            } else if (response && response.data && response.data.task_id) {
              // 在response.data中
              taskId = response.data.task_id;
              console.log(`从response.data.task_id获取到任务ID: ${taskId}`);
            } else if (typeof response === 'string') {
              // 响应可能直接是任务ID字符串
              taskId = response;
              console.log(`响应是字符串，可能直接是任务ID: ${taskId}`);
            } else if (response && typeof response === 'object') {
              // 尝试查找任何可能是任务ID的属性
              const possibleKeys = Object.keys(response);
              console.log('响应中的所有属性:', possibleKeys);
              
              for (const key of possibleKeys) {
                const value = response[key];
                if (typeof value === 'string' && 
                    (key.includes('id') || key.includes('task') || 
                     value.includes('-') && value.length > 30)) {
                  taskId = value;
                  console.log(`从response.${key}找到可能的任务ID: ${taskId}`);
                  break;
                } else if (value && typeof value === 'object') {
                  // 检查嵌套对象
                  const nestedKeys = Object.keys(value);
                  for (const nestedKey of nestedKeys) {
                    if ((nestedKey.includes('id') || nestedKey.includes('task')) && 
                        typeof value[nestedKey] === 'string') {
                      taskId = value[nestedKey];
                      console.log(`从response.${key}.${nestedKey}找到可能的任务ID: ${taskId}`);
                      break;
                    }
                  }
                  if (taskId) break;
                }
              }
            }
            
            if (taskId) {
              console.log(`获取到异步翻译任务ID: ${taskId}`);
              pollTranslationResult(taskId);
            } else {
              console.error('无法从响应中找到task_id:', response);
              ElMessage.error('翻译失败，请重试');
              isTranslating.value = false;
            }
          } catch (asyncError) {
            console.error('异步翻译请求失败:', asyncError);
            ElMessage.error('异步翻译请求失败，请重试');
            isTranslating.value = false;
          }
        } else {
          // 同步翻译
          const response = await translateText(
            sourceText.value,
            source,
            target,
            domain.value,
            'standard',
            selectedModel.value
          );
          
          result = response.data;
          
          // 处理翻译结果
          translatedText.value = result.translated_text;
          
          // 如果是自动检测语言，更新检测到的语言
          if (source === 'auto' && result.detected_language) {
            // 标准化检测到的语言代码
            const detected = normalizeLanguageCode(result.detected_language);
            detectedLanguage.value = detected;
            console.log(`检测到语言: ${detected}`);
          }
          
          // 将结果添加到历史记录
          addToHistory({
            original_text: sourceText.value,
            translated_text: result.translated_text,
            source_language: result.source_lang || source,
            target_language: result.target_lang || target,
            domain: domain.value,
            created_at: new Date().toISOString()
          });
          
          isTranslating.value = false;
        }
      } catch (error) {
        console.error('翻译失败:', error);
        ElMessage.error('翻译失败，请重试');
        isTranslating.value = false;
      }
    };
    
    // 添加检测中文字符的函数
    const containsChinese = (text) => {
      const pattern = /[\u4e00-\u9fff\u3400-\u4dbf]/;
      return pattern.test(text);
    };
    
    // 处理源文本变化，自动翻译
    const handleSourceTextChange = () => {
      if (translationTimeout) {
        clearTimeout(translationTimeout);
      }
      
      if (sourceText.value.trim() && sourceText.value.length > 1) {
        translationTimeout = setTimeout(() => {
          translate();
        }, 1000);
      } else {
        translatedText.value = '';
      }
    };
    
    // 交换语言
    const swapLanguages = () => {
      if (sourceLanguage.value === 'auto') {
        if (detectedLanguage.value) {
          // 使用已检测到的语言而不是"auto"
          const temp = normalizeLanguageCode(detectedLanguage.value);
          sourceLanguage.value = targetLanguage.value;
          targetLanguage.value = temp;
          
          // 如果有翻译文本，自动执行反向翻译
          if (translatedText.value.trim()) {
            const originalSource = sourceText.value;
            sourceText.value = translatedText.value;
            translatedText.value = originalSource;
            autoTranslateIfNeeded();
          }
          return;
        } else {
          ElMessage.info('未检测到源语言，无法切换');
          return;
        }
      }

      if (sourceLanguage.value === targetLanguage.value) {
        ElMessage.info('源语言和目标语言相同，无需切换');
        return;
      }
      
      const temp = sourceLanguage.value;
      sourceLanguage.value = targetLanguage.value;
      targetLanguage.value = temp;
      
      // 修正自动检测问题
      if (targetLanguage.value === 'auto') {
        if (detectedLanguage.value) {
          targetLanguage.value = normalizeLanguageCode(detectedLanguage.value);
        } else {
          targetLanguage.value = 'en-US';  // 使用标准化的英语代码
        }
      }
      
      // 如果有翻译文本，自动执行反向翻译
      if (translatedText.value.trim()) {
        const originalSource = sourceText.value;
        sourceText.value = translatedText.value;
        translatedText.value = originalSource;
        autoTranslateIfNeeded();
      }
    };
    
    // 清空源文本
    const clearSourceText = () => {
      sourceText.value = '';
      translatedText.value = '';
      ElMessage.success('已清空文本');
    };

    // 从剪贴板粘贴
    const pasteFromClipboard = async () => {
      try {
        // 检查剪贴板API是否可用
        if (!navigator.clipboard || !navigator.clipboard.readText) {
          ElMessage.warning('您的浏览器不支持剪贴板功能，请手动粘贴');
          return;
        }

        const text = await navigator.clipboard.readText();
        if (text.trim()) {
          sourceText.value = text;
          ElMessage.success('已粘贴文本');
          // 自动执行翻译
          if (sourceText.value.trim()) {
            translate();
          }
        } else {
          ElMessage.warning('剪贴板为空');
        }
      } catch (err) {
        console.error('粘贴失败:', err);
        ElMessage.error('粘贴失败，请手动粘贴或使用 Ctrl+V');
      }
    };

    // 插入示例文本
    const insertSampleText = () => {
      const samples = [
        '你好，世界！这是一个翻译测试。',
        'Hello, this is a translation test. How are you today?',
        'こんにちは、これは翻訳テストです。',
        'Bonjour, ceci est un test de traduction.',
        'Hola, esta es una prueba de traducción.',
        'Привет, это тест перевода.',
        '안녕하세요, 이것은 번역 테스트입니다.',
        'Ciao, questo è un test di traduzione.',
        'Olá, este é um teste de tradução.',
        'Hallo, dit is een vertaaltest.'
      ];

      const randomSample = samples[Math.floor(Math.random() * samples.length)];
      sourceText.value = randomSample;
      ElMessage.success('已插入示例文本');
    };

    // 设置语言对
    const setLanguagePair = (source, target) => {
      sourceLanguage.value = source;
      targetLanguage.value = target;

      // 如果有文本，自动翻译
      if (sourceText.value.trim()) {
        translate();
      }

      ElMessage.success(`已切换到 ${getLanguageName(source)} → ${getLanguageName(target)}`);
    };

    // 检查是否是当前语言对
    const isCurrentPair = (source, target) => {
      return sourceLanguage.value === source && targetLanguage.value === target;
    };

    // 复制翻译结果
    const copyTranslatedText = async () => {
      if (!translatedText.value) return;

      try {
        // 检查剪贴板API是否可用
        if (!navigator.clipboard || !navigator.clipboard.writeText) {
          // 降级方案：创建临时文本区域
          const textArea = document.createElement('textarea');
          textArea.value = translatedText.value;
          document.body.appendChild(textArea);
          textArea.select();
          document.execCommand('copy');
          document.body.removeChild(textArea);
          ElMessage.success('已复制到剪贴板');
          return;
        }

        await navigator.clipboard.writeText(translatedText.value);
        ElMessage.success('已复制到剪贴板');
      } catch (err) {
        console.error('复制失败:', err);
        ElMessage.error('复制失败，请手动复制或使用 Ctrl+C');
      }
    };
    
    // 朗读翻译结果
    const speakTranslatedText = () => {
      if (!translatedText.value) {
        ElMessage.warning('没有可朗读的文本');
        return;
      }

      if (!isSpeechSynthesisSupported.value) {
        ElMessage.warning('您的浏览器不支持语音合成功能');
        return;
      }

      try {
        // 停止当前朗读
        window.speechSynthesis.cancel();

        const utterance = new SpeechSynthesisUtterance(translatedText.value);

        // 设置语言，如果目标语言是标准语言代码
        const langCode = targetLanguage.value;
        if (langCode && langCode !== 'auto') {
          utterance.lang = langCode;
        }

        // 设置语音参数
        utterance.rate = 0.9; // 语速
        utterance.pitch = 1.0; // 音调
        utterance.volume = 1.0; // 音量

        // 添加事件监听
        utterance.onstart = () => {
          console.log('开始朗读');
        };

        utterance.onend = () => {
          console.log('朗读结束');
        };

        utterance.onerror = (event) => {
          console.error('朗读出错:', event.error);
          ElMessage.error('朗读失败，请重试');
        };

        window.speechSynthesis.speak(utterance);
        ElMessage.success('开始朗读');
      } catch (error) {
        console.error('朗读功能出错:', error);
        ElMessage.error('朗读功能出错，请重试');
      }
    };
    
    // 下载翻译结果
    const downloadTranslation = () => {
      if (!translatedText.value) return;
      
      const content = `源语言(${getLanguageName(sourceLanguage.value)}):\n${sourceText.value}\n\n目标语言(${getLanguageName(targetLanguage.value)}):\n${translatedText.value}`;
      const blob = new Blob([content], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `translation_${sourceLanguage.value}_to_${targetLanguage.value}.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    };
    
    // 切换语音输入
    const toggleVoiceInput = (target) => {
      if (!isSpeechRecognitionSupported.value) {
        ElMessage.warning('您的浏览器不支持语音识别');
        return;
      }
      
      voiceInputTarget.value = target;
      
      if (voiceInputActive.value) {
        stopVoiceInput();
      } else {
        startVoiceInput();
      }
    };
    
    // 开始语音输入
    const startVoiceInput = () => {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      const recognition = new SpeechRecognition();
      
      recognition.lang = sourceLanguage.value;
      recognition.continuous = true;
      recognition.interimResults = true;
      
      recognition.onresult = (event) => {
        const transcript = Array.from(event.results)
          .map(result => result[0].transcript)
          .join('');
        
        sourceText.value = transcript;
        handleSourceTextChange();
      };
      
      recognition.onend = () => {
        voiceInputActive.value = false;
      };
      
      recognition.start();
      voiceInputActive.value = true;
    };
    
    // 停止语音输入
    const stopVoiceInput = () => {
      window.speechSynthesis.cancel();
      voiceInputActive.value = false;
    };
    
    // getLanguageName 现在从全局配置中获取
    
    // 处理源语言变化
    const handleSourceLanguageChange = (value) => {
      sourceLanguage.value = normalizeLanguageCode(value);
      autoTranslateIfNeeded();
    };
    
    // 处理目标语言变化
    const handleTargetLanguageChange = (value) => {
      targetLanguage.value = normalizeLanguageCode(value);
      autoTranslateIfNeeded();
    };
    
    // 处理领域变化
    const handleDomainChange = (value) => {
      domain.value = value;
      if (sourceText.value.trim()) {
        translate();
      }
    };
    
    // 处理模型变化
    const handleModelChange = (value) => {
      selectedModel.value = value;
      // 保存选择的模型到本地存储
      localStorage.setItem('selected_model', value);
      
      if (sourceText.value.trim()) {
        translate();
      }
    };
    
    // 从历史记录加载翻译
    const loadHistoryItem = (record) => {
      sourceText.value = record.original_text || record.source_text;
      translatedText.value = record.translated_text;

      // 确保使用标准化的语言代码
      sourceLanguage.value = normalizeLanguageCode(record.source_language);
      targetLanguage.value = normalizeLanguageCode(record.target_language);

      domain.value = record.domain || 'general';
    };

    // 复制历史记录中的文本
    const copyHistoryText = async (text) => {
      try {
        await navigator.clipboard.writeText(text);
        ElMessage.success('已复制到剪贴板');
      } catch (error) {
        console.error('复制失败:', error);
        ElMessage.error('复制失败，请手动复制');
      }
    };
    
    // 截断文本
    const truncateText = (text, maxLength) => {
      if (!text) return '';
      if (text.length <= maxLength) return text;
      return text.substring(0, maxLength) + '...';
    };
    
    // 显示反馈弹窗
    const showFeedbackModal = () => {
      feedbackVisible.value = true;
    };
    
    // 提交反馈
    const submitFeedback = async () => {
      if (!feedbackText.value && feedbackOptions.value.length === 0) {
        ElMessage.warning('请提供反馈内容或选择问题类型');
        return;
      }

      try {
        // 准备反馈数据
        const feedbackData = {
          source_text: sourceText.value,
          translated_text: translatedText.value,
          source_language: sourceLanguage.value,
          target_language: targetLanguage.value,
          feedback_text: feedbackText.value,
          feedback_options: feedbackOptions.value,
          rating: 3, // 默认3星评分，可以后续添加评分组件
          model_used: selectedModel.value,
          task_id: translationTaskId.value
        };

        console.log('提交反馈数据:', feedbackData);

        // 发送反馈到后端API
        const response = await fetch('/api/v1/translation/feedback', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(feedbackData)
        });

        const result = await response.json();

        if (result.success) {
          ElMessage.success(result.message || '感谢您的反馈！');
          console.log('反馈提交成功，ID:', result.feedback_id);
        } else {
          ElMessage.error(result.error || '提交反馈失败');
        }

      } catch (error) {
        console.error('提交反馈失败:', error);
        ElMessage.error('提交反馈失败，请重试');
      }

      // 关闭反馈弹窗并清空内容
      feedbackVisible.value = false;
      feedbackText.value = '';
      feedbackOptions.value = [];
    };
    
    // 获取模型显示名称
    const getModelDisplayName = (modelId) => {
      // 模型名称映射表
      const modelNameMap = {
        'deepseek-r1:8b': 'DeepSeek R1 8B',
        'llama2:13b': 'Llama 2 13B',
        'gemma:7b': 'Gemma 7B',
        'mistral': 'Mistral 7B',
        'qwen:7b': 'Qwen 7B',
        'qwen:14b': 'Qwen 14B',
        'yi:6b': 'Yi 6B',
        'yi:34b': 'Yi 34B',
        'mixtral:8x7b': 'Mixtral 8x7B',
        'vicuna:7b': 'Vicuna 7B',
        'vicuna:13b': 'Vicuna 13B',
        'codellama:7b': 'CodeLlama 7B',
        'codellama:13b': 'CodeLlama 13B',
        'codellama:34b': 'CodeLlama 34B',
        'wizardcoder:7b': 'WizardCoder 7B',
        'wizardcoder:13b': 'WizardCoder 13B',
        'wizardcoder:34b': 'WizardCoder 34B',
        'llama2': 'Llama 2',
        'llama3': 'Llama 3',
        'phi': 'Phi',
        'phi2': 'Phi-2',
        'phi3': 'Phi-3',
        'gemma:2b': 'Gemma 2B'
      };
      
      // 如果有映射，使用映射名称
      if (modelNameMap[modelId]) {
        return modelNameMap[modelId];
      }
      
      // 处理带有版本号的模型ID
      if (modelId.includes(':')) {
        const [name, version] = modelId.split(':');
        // 首字母大写
        const formattedName = name.charAt(0).toUpperCase() + name.slice(1);
        return `${formattedName} ${version}`;
      }
      
      // 默认情况下返回原始ID，首字母大写
      return modelId.charAt(0).toUpperCase() + modelId.slice(1);
    };
    

    
    // 在组件卸载前清除轮询
    onBeforeUnmount(() => {
      if (translationPollingInterval.value) {
        clearInterval(translationPollingInterval.value);
        translationPollingInterval.value = null;
      }
    });
    
    // 添加浏览器能力检测函数
    const checkBrowserSupport = () => {
      // 检查语音识别API支持
      isSpeechRecognitionSupported.value = 'SpeechRecognition' in window || 'webkitSpeechRecognition' in window;
      
      // 检查语音合成API支持
      isSpeechSynthesisSupported.value = 'speechSynthesis' in window;
      
      console.log(`浏览器支持检测: 语音识别=${isSpeechRecognitionSupported.value}, 语音合成=${isSpeechSynthesisSupported.value}`);
    };
    
    return {
      // 全局语言配置
      languages,
      languageCategories,
      popularLanguages,
      commonLanguagePairs,
      filteredLanguages,
      popularLanguageList,
      languagesByCategory,
      languageSearchQuery,
      getLanguageName,
      normalizeLanguageCode,

      // 组件状态
      sourceLanguage,
      targetLanguage,
      sourceText,
      translatedText,
      isTranslating,
      voiceInputActive,
      voiceInputTarget,
      domain,
      selectedModel,
      translationHistory,
      feedbackVisible,
      feedbackText,
      feedbackOptions,
      isSpeechRecognitionSupported,
      isSpeechSynthesisSupported,
      detectedLanguage,
      stats,

      // 方法
      translate,
      handleSourceTextChange,
      swapLanguages,
      clearSourceText,
      pasteFromClipboard,
      insertSampleText,
      setLanguagePair,
      isCurrentPair,
      forceRefreshLanguages,
      copyTranslatedText,
      speakTranslatedText,
      downloadTranslation,
      toggleVoiceInput,
      getLanguageName,
      handleSourceLanguageChange,
      handleTargetLanguageChange,
      handleDomainChange,
      handleModelChange,
      loadHistoryItem,
      copyHistoryText,
      truncateText,
      formatTime,
      showFeedbackModal,
      submitFeedback,
      getModelDisplayName,
      loadingModels,
      ollamaStatus,
      showModelSettings,
      fetchOllamaModels,
      testModelConnection,
      availableModels,
      useAsyncTranslation,
      translationTaskId,
      translationPollingInterval,
      // 新增的语言相关功能
      languageSearchQuery,
      filteredLanguages,
      languageCategories,
      popularLanguages,
      popularLanguageList
    };
  }
});
</script>

<style scoped>
.text-translation-container {
  padding: 20px;
}

.translation-card {
  margin-top: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.control-area {
  margin-bottom: 20px;
}

.language-controls {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  flex-wrap: wrap;
}

.swap-button {
  display: flex;
  align-items: center;
  justify-content: center;
}

.text-panel {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.panel-title {
  font-weight: 500;
  color: #333;
}

.detected-language {
  font-size: 12px;
  color: #666;
  margin-left: 5px;
}

.panel-actions {
  display: flex;
  gap: 8px;
}

.translation-textarea {
  flex: 1;
  min-height: 300px;
}

.translation-textarea :deep(.el-textarea__inner) {
  resize: vertical;
  padding: 16px;
  font-size: 14px;
  line-height: 1.6;
  min-height: 300px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
}

.translation-textarea:focus {
  outline: none;
  box-shadow: none;
}

.translation-result-area {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.translation-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  z-index: 10;
}

.panel-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #fafafa;
  border-top: 1px solid #f0f0f0;
}

.character-count {
  color: #999;
  font-size: 13px;
}

.translation-stats {
  color: #666;
  font-size: 13px;
}

.voice-input-indicator {
  padding: 8px 16px;
  background-color: #e6f7ff;
}

.active-voice {
  color: #1890ff;
}

.translation-history {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.history-card {
  margin-bottom: 0;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.language-pair {
  display: flex;
  align-items: center;
  gap: 8px;
}

.arrow-icon {
  color: #909399;
  font-size: 14px;
}

.history-time {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #909399;
}

.history-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.text-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.text-label {
  font-size: 12px;
  font-weight: 600;
  color: #606266;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.text-content {
  padding: 12px;
  border-radius: 6px;
  line-height: 1.6;
  font-size: 14px;
  white-space: pre-wrap;
  word-break: break-word;
}

.source-text {
  background-color: #f8f9fa;
  border-left: 3px solid #409eff;
}

.translated-text {
  background-color: #f0f9ff;
  border-left: 3px solid #67c23a;
}

.history-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.history-source, .history-translated {
  margin: 0;
  font-size: 13px;
  color: #666;
}

.history-arrow {
  color: #1890ff;
  font-size: 12px;
}

.feedback-options {
  margin-top: 16px;
}

/* 添加模型选择器样式 */
.model-control {
  margin-left: auto;
  display: flex;
  align-items: center;
}

/* 添加代理服务器设置样式 */
.settings-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 16px;
}

.settings-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.settings-label {
  font-weight: 500;
  color: #333;
}

.settings-hint {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

@media (max-width: 768px) {
  .language-controls {
    flex-wrap: wrap;
  }
  
  .model-control {
    margin-left: 0;
    margin-top: 10px;
    width: 100%;
  }
}

.async-control {
  margin-left: 16px;
}

/* 快捷语言对选择样式 */
.quick-language-pairs {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 12px;
  padding: 12px 0;
  border-top: 1px solid #f0f0f0;
}

.quick-pairs-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.quick-language-pairs .el-button-group .el-button {
  font-size: 12px;
  padding: 6px 12px;
}

/* 快捷键提示样式 */
.keyboard-shortcuts {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.shortcuts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.shortcut-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  font-size: 13px;
}

.shortcut-item kbd {
  background: #f5f5f5;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 11px;
  font-family: monospace;
  color: #374151;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.shortcut-item span {
  color: #666;
  font-weight: 500;
}

/* 新增的快捷操作样式 */
.panel-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
}

.footer-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.footer-right {
  display: flex;
  align-items: center;
}

.quick-actions {
  display: flex;
  align-items: center;
  gap: 4px;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.quick-actions:hover {
  opacity: 1;
}

.quick-actions .el-button {
  padding: 4px 6px;
  font-size: 12px;
}

/* 翻译进度指示器 */
.translation-progress {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 16px;
}

.progress-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 500;
  color: #409eff;
}

.progress-details {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  font-size: 12px;
}

.progress-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.task-id {
  font-family: monospace;
  background: #e6f7ff;
  padding: 2px 6px;
  border-radius: 4px;
  color: #1890ff;
}

/* 翻译质量指示器 */
.translation-quality {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8px;
  padding: 6px 12px;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
  font-size: 12px;
}

.quality-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #52c41a;
  font-weight: 500;
}

.quality-score {
  display: flex;
  align-items: center;
  gap: 8px;
}

.score-text {
  color: #52c41a;
  font-weight: 500;
}

.result-textarea.has-content {
  border-color: #52c41a;
}

/* 动画效果 */
.translation-textarea {
  transition: border-color 0.2s, box-shadow 0.2s;
}

.translation-textarea:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

.el-button {
  transition: all 0.2s;
}

.el-button:hover {
  transform: translateY(-1px);
}

/* 响应式设计优化 */
@media (max-width: 768px) {
  .translation-container {
    padding: 12px;
  }

  .text-panel {
    margin-bottom: 16px;
  }

  .panel-footer {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .footer-left,
  .footer-right {
    justify-content: center;
  }

  .quick-actions {
    justify-content: center;
  }
}
</style>