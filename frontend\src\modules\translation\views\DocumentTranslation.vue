<template>
  <div class="document-translation-container">
    <page-header-gradient
      title="文档翻译"
      subtitle="上传各种文档格式，快速获取专业翻译结果"
      :actions="[]"
    />

    <el-card class="document-translation-card">
      <div class="debug-mode-toggle" v-if="isDev">
        <el-switch
          v-model="debugMode"
          @change="(checked) => {
            localStorage.setItem('debug_mode', checked ? 'true' : 'false');
          }"
        />
        <span class="debug-mode-label">调试模式</span>
      </div>

      <el-steps :active="currentStep" simple class="document-steps">
        <el-step title="上传文档" />
        <el-step title="选择语言" />
        <el-step title="翻译" />
        <el-step title="下载结果" />
      </el-steps>

      <!-- 步骤1: 上传文档 -->
      <div v-if="currentStep === 0" class="step-content">
        <div class="upload-container">
          <el-upload
            class="upload-dragger"
            drag
            :multiple="false"
            :before-upload="beforeUpload"
            :file-list="fileList"
            @change="handleFileChange"
            @remove="handleRemoveFile"
            :accept="acceptedExtensions"
            :http-request="customRequest"
            :show-file-list="false"
          >
            <el-icon class="el-icon--upload">
              <upload-filled />
            </el-icon>
            <div class="el-upload__text">点击或拖拽文件到此区域上传</div>
            <div class="el-upload__tip">
              支持单个文件上传，支持格式：DOC, DOCX, PDF, TXT, PPT, PPTX, XLS, XLSX
            </div>
          </el-upload>

          <div class="file-info" v-if="fileList.length > 0">
            <h3>已上传文档</h3>
            <div class="file-list">
              <div v-for="file in fileList" :key="file.uid" class="file-item">
                <div class="file-content">
                  <el-icon class="file-icon">
                    <document />
                  </el-icon>
                  <div class="file-details">
                    <div class="file-name">{{ file.name }}</div>
                    <div class="file-description">
                      {{ formatFileSize(file.size) }} | {{ getFileTypeDescription(file.name) }}
                    </div>
                  </div>
                </div>
                <el-button type="danger" text @click="() => handleRemoveFile(file)">
                  <el-icon><delete /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <div class="step-actions">
          <el-button type="primary" :disabled="fileList.length === 0" @click="nextStep">
            下一步
          </el-button>
        </div>
      </div>

      <!-- 步骤2: 选择语言和翻译选项 -->
      <div v-if="currentStep === 1" class="step-content">
        <div class="language-section">
          <el-form :model="formState" label-position="top">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="源语言">
                  <el-select
                    v-model="formState.sourceLanguage"
                    style="width: 100%"
                    placeholder="请选择源语言"
                  >
                    <el-option
                      v-for="option in languageOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="目标语言">
                  <el-select
                    v-model="formState.targetLanguage"
                    style="width: 100%"
                    placeholder="请选择目标语言"
                  >
                    <el-option
                      v-for="option in languageOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider />

            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="专业领域">
                  <el-select
                    v-model="formState.domain"
                    style="width: 100%"
                    placeholder="请选择专业领域"
                  >
                    <el-option
                      v-for="option in domainOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="翻译风格">
                  <el-select
                    v-model="formState.style"
                    style="width: 100%"
                    placeholder="请选择翻译风格"
                  >
                    <el-option
                      v-for="option in styleOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="高级选项">
              <el-checkbox-group v-model="formState.advancedOptions">
                <el-checkbox value="glossary">使用自定义术语库</el-checkbox>
                <el-checkbox value="keepFormat">保留原文档格式</el-checkbox>
                <el-checkbox value="comments">翻译文档中的注释</el-checkbox>
              </el-checkbox-group>
            </el-form-item>

            <el-form-item v-if="formState.advancedOptions.includes('glossary')">
              <el-select
                v-model="formState.glossary"
                style="width: 100%"
                placeholder="请选择术语库"
              >
                <el-option
                  v-for="option in glossaryOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </div>

        <div class="step-actions">
          <el-button style="margin-right: 8px" @click="prevStep">上一步</el-button>
          <el-button type="primary" @click="startTranslation">开始翻译</el-button>
        </div>
      </div>

      <!-- 步骤3: 翻译进行中 -->
      <div v-if="currentStep === 2" class="step-content">
        <div class="translation-progress">
          <el-progress
            :percentage="translationProgress"
            :status="progressStatus"
          />
          <p class="progress-text">{{ progressText }}</p>

          <!-- 特殊字符处理状态 -->
          <div v-if="specialCharStatus.processed" class="special-char-alert">
            <el-alert
              :type="specialCharStatus.count > 0 ? 'warning' : 'success'"
              show-icon
            >
              <template #title>
                {{ specialCharStatus.message }}
                <a @click="toggleSpecialCharDetails" class="toggle-details">
                  {{ showSpecialCharDetails ? '隐藏详情' : '显示详情' }}
                </a>
              </template>
              <template #description v-if="showSpecialCharDetails && specialCharStatus.details.length > 0">
                <div class="special-char-details">
                  <div v-for="(detail, index) in specialCharStatus.details" :key="index">
                    <span>{{ detail.original }} → {{ detail.replacement }}</span>
                    <span class="char-count">({{ detail.count }}次)</span>
                  </div>
                </div>
              </template>
            </el-alert>
          </div>

          <!-- 网络错误信息 -->
          <div v-if="networkError" class="network-error-alert">
            <el-alert
              type="error"
              show-icon
              :title="'网络连接错误'"
              :description="networkErrorMessage"
            >
              <template #default>
                <el-button size="small" type="primary" @click="retryConnection">
                  重试连接
                </el-button>
              </template>
            </el-alert>
          </div>

          <!-- 错误信息 -->
          <div v-if="errorMsg" class="error-message">
            <el-alert type="error" show-icon :title="errorMsg" />
          </div>
        </div>

        <div class="step-actions">
          <el-button
            type="primary"
            :disabled="progressStatus !== 'success'"
            @click="nextStep"
          >
            查看结果
          </el-button>
        </div>
      </div>

      <!-- 步骤4: 下载结果 -->
      <div v-if="currentStep === 3" class="step-content">
        <div class="result-section">
          <el-result
            icon="success"
            title="文档翻译完成"
            sub-title="您的文档已成功翻译，可以下载或直接查看"
          >
            <template #extra>
              <!-- 翻译摘要信息 -->
              <div class="translation-summary" style="margin-bottom: 20px;">
                <el-descriptions title="翻译详情" :column="2" border>
                  <el-descriptions-item label="源文档">
                    {{ fileList[0]?.name || '未知文件' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="源语言">
                    {{ detectedSourceLang || (formState.sourceLanguage === 'auto' ? '自动检测' : getLanguageName(formState.sourceLanguage)) }}
                  </el-descriptions-item>
                  <el-descriptions-item label="目标语言">
                    {{ getLanguageName(formState.targetLanguage) }}
                  </el-descriptions-item>
                  <el-descriptions-item label="专业领域">
                    {{ getDomainName(formState.domain) }}
                  </el-descriptions-item>
                  <el-descriptions-item label="翻译时长">
                    {{ translationDuration || '未知' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="字数统计">
                    {{ wordCount || '未知' }} 词
                  </el-descriptions-item>
                </el-descriptions>
              </div>

              <div class="result-actions">
                <el-button type="primary" @click="downloadResult">
                  <el-icon><download /></el-icon> 下载翻译文件
                </el-button>
                <el-button @click="previewTranslatedDocument">
                  <el-icon><view /></el-icon> 预览翻译内容
                </el-button>
                <el-button @click="translateNewDocument">
                  <el-icon><refresh /></el-icon> 翻译新文档
                </el-button>
              </div>
            </template>
          </el-result>
        </div>
      </div>
    </el-card>

    <!-- 特殊字符详情模态框 -->
    <el-dialog
      v-model="specialCharDetailsVisible"
      :title="specialCharDetails?.title || '特殊字符详情'"
      width="600px"
      :destroy-on-close="true"
    >
      <div class="special-char-details-modal">
        <p class="details-description">{{ specialCharDetails?.description }}</p>
        
        <div class="details-examples" v-if="specialCharDetails?.examples && specialCharDetails.examples.length > 0">
          <h4>示例:</h4>
          <ul>
            <li v-for="(example, index) in specialCharDetails.examples" :key="index">{{ example }}</li>
          </ul>
        </div>
        
        <div class="details-recommendation">
          <h4>处理建议:</h4>
          <p>{{ specialCharDetails?.recommendation }}</p>
        </div>
      </div>
    </el-dialog>

    <!-- 预览模态框 -->
    <el-dialog
      v-model="previewVisible"
      title="翻译预览"
      width="95%"
      :destroy-on-close="true"
      class="preview-modal"
      :maskClosable="false"
      :keyboard="false"
    >
      <!-- 工具栏 -->
      <div class="preview-toolbar">
        <div class="toolbar-left">
          <el-button-group>
            <el-button size="small" @click="zoomOut"><el-icon><minus /></el-icon></el-button>
            <el-button size="small">{{ zoomLevel }}%</el-button>
            <el-button size="small" @click="zoomIn"><el-icon><plus /></el-icon></el-button>
          </el-button-group>

          <el-divider direction="vertical" />

          <el-button-group>
            <el-tooltip content="上一页">
              <el-button size="small" @click="prevPage" :disabled="currentPage <= 1">
                <el-icon><arrow-left /></el-icon>
              </el-button>
            </el-tooltip>
            <el-button size="small">{{ currentPage }} / {{ totalPages }}</el-button>
            <el-tooltip content="下一页">
              <el-button size="small" @click="nextPage" :disabled="currentPage >= totalPages">
                <el-icon><arrow-right /></el-icon>
              </el-button>
            </el-tooltip>
          </el-button-group>
        </div>
        
        <div class="toolbar-right">
          <el-radio-group v-model="viewMode" size="small">
            <el-radio-button value="side-by-side">对照视图</el-radio-button>
            <el-radio-button value="para-by-para">左右对照编辑</el-radio-button>
            <el-radio-button value="source-only">仅原文</el-radio-button>
            <el-radio-button value="translated-only">仅译文</el-radio-button>
          </el-radio-group>

          <el-divider direction="vertical" />

          <el-tooltip content="全屏">
            <el-button size="small" @click="toggleFullscreen">
              <el-icon>
                <full-screen v-if="!isFullscreen" />
                <full-screen v-else />
              </el-icon>
            </el-button>
          </el-tooltip>

          <el-tooltip content="搜索">
            <el-button size="small" @click="toggleSearch">
              <el-icon><search /></el-icon>
            </el-button>
          </el-tooltip>
        </div>
      </div>
      
      <!-- 搜索面板 -->
      <div class="search-panel" v-if="showSearch">
        <el-input
          placeholder="搜索文本..."
          v-model="searchText"
          @keyup.enter="performSearch"
          clearable
          size="small"
        >
          <template #append>
            <el-button @click="performSearch">
              <el-icon><search /></el-icon>
            </el-button>
          </template>
        </el-input>
        <div class="search-options">
          <el-checkbox v-model="searchInSource">搜索原文</el-checkbox>
          <el-checkbox v-model="searchInTranslated">搜索译文</el-checkbox>
          <el-checkbox v-model="caseSensitive">区分大小写</el-checkbox>
        </div>
        <div class="search-results" v-if="searchResults.length > 0">
          找到 {{ searchResults.length }} 个结果
          <el-button size="small" @click="prevSearchResult">上一个</el-button>
          <el-button size="small" @click="nextSearchResult">下一个</el-button>
        </div>
      </div>
      
      <!-- 预览内容 -->
      <div class="document-preview-wrapper" :class="{ 'fullscreen': isFullscreen }">
        <!-- 对照视图 -->
        <div v-if="viewMode === 'side-by-side'" class="side-by-side-view">
          <div class="document-column source-column">
            <div class="column-header">原文</div>
            <div class="column-content" v-html="documentPreview.source" ref="sourceContent"></div>
          </div>
          <div class="document-column-divider"></div>
          <div class="document-column translated-column">
            <div class="column-header">译文</div>
            <div class="column-content" v-html="documentPreview.translated" ref="translatedContent"></div>
          </div>
        </div>
        
        <!-- 左右对照编辑视图 -->
        <div v-else-if="viewMode === 'para-by-para'" class="para-table-view">
          <div class="para-tools">
            <el-button type="primary" size="small" @click="saveAllEdits" :disabled="!hasEdits">
              <el-icon><check /></el-icon> 保存所有编辑
            </el-button>
            <el-switch
              v-model="showLineNumbers"
              size="small"
              active-text="显示行号"
              inactive-text="隐藏行号"
            />
            <el-switch
              v-model="showSentenceView"
              size="small"
              active-text="句子级别"
              inactive-text="段落级别"
            />
          </div>
          
          <div class="table-container">
            <table class="para-table">
              <thead>
                <tr>
                  <th v-if="showLineNumbers" class="line-number-col">#</th>
                  <th class="source-col">原文</th>
                  <th class="translated-col">译文</th>
                </tr>
              </thead>
              <tbody>
                <!-- 段落级别视图 -->
                <template v-if="!showSentenceView">
                  <tr v-for="(item, index) in previewParaData" :key="item.id" 
                      class="para-row" :class="{ 'edited': item.edited }">
                    <td v-if="showLineNumbers" class="line-number">{{ index + 1 }}</td>
                    <td class="source-text">{{ item.source }}</td>
                    <td class="translated-text-cell">
                      <div v-if="!item.editing" class="translated-text">
                        {{ item.translated }}
                        <el-button class="edit-btn" type="primary" link size="small" @click="startEditPara(index)">
                          <el-icon><edit /></el-icon>
                        </el-button>
                        <span v-if="item.edited" class="edited-mark">
                          <el-icon><circle-check /></el-icon> 已编辑
                        </span>
                      </div>
                      <div v-else class="editing-area">
                        <el-input
                          v-model="item.editingContent"
                          type="textarea"
                          :autosize="{ minRows: 2, maxRows: 10 }"
                          placeholder="请输入译文..."
                        />
                        <div class="edit-actions">
                          <el-button size="small" type="primary" @click="saveParaEdit(index)">保存</el-button>
                          <el-button size="small" @click="cancelParaEdit(index)">取消</el-button>
                        </div>
                      </div>
                    </td>
                  </tr>
                </template>
                
                <!-- 句子级别视图 -->
                <template v-else>
                  <template v-for="(item, paraIndex) in previewParaData" :key="`para-${paraIndex}`">
                    <!-- 段落标题行 -->
                    <tr class="para-header-row">
                      <td v-if="showLineNumbers" class="line-number">{{ paraIndex + 1 }}</td>
                      <td colspan="2" class="para-header">
                        段落 #{{ paraIndex + 1 }}
                        <span v-if="item.edited" class="edited-mark">
                          <el-icon><circle-check /></el-icon> 已编辑
                        </span>
                      </td>
                    </tr>
                    
                    <!-- 表格类型单独处理 -->
                    <tr v-if="item.type === 'table' || item.headers" class="para-row table-type-row">
                      <td v-if="showLineNumbers" class="line-number"></td>
                      <td class="source-text">{{ item.source }}</td>
                      <td class="translated-text-cell">
                        <div class="translated-text">
                          {{ item.translated }}
                          <el-button class="edit-btn" type="primary" link size="small" @click="startEditPara(paraIndex)">
                            <el-icon><edit /></el-icon> 编辑表格
                          </el-button>
                        </div>
                      </td>
                    </tr>
                    
                    <!-- 句子级别编辑 -->
                    <template v-else-if="item.sentenceAlignments && item.sentenceAlignments.length">
                      <tr v-for="(alignment, sentenceIndex) in item.sentenceAlignments" 
                          :key="`${paraIndex}-${sentenceIndex}`" 
                          class="sentence-row"
                          :class="{
                            'low-confidence': alignment.confidence < 0.7,
                            'sentence-edited': sentenceEdited(paraIndex, sentenceIndex)
                          }">
                        
                        <td v-if="showLineNumbers" class="line-number">{{ paraIndex + 1 }}.{{ sentenceIndex + 1 }}</td>
                        
                        <td class="source-text">{{ alignment.source }}</td>
                        
                        <td class="translated-text-cell">
                          <!-- 编辑模式 -->
                          <div v-if="isEditingSentence(paraIndex, sentenceIndex)" class="sentence-editor">
                            <el-input
                              v-model="editingSentenceText"
                              type="textarea"
                              :autosize="{ minRows: 2, maxRows: 10 }"
                              @keydown.esc="cancelEditSentence"
                              @keydown.ctrl.enter="saveSentenceEdit(paraIndex, sentenceIndex)"
                            />
                            <div class="edit-actions">
                              <el-button size="small" type="primary" @click="saveSentenceEdit(paraIndex, sentenceIndex)">保存</el-button>
                              <el-button size="small" @click="cancelEditSentence">取消</el-button>
                            </div>
                          </div>
                          
                          <!-- 显示模式 -->
                          <div v-else class="translated-text">
                            {{ alignment.translated }}
                            <el-button class="edit-btn" type="primary" link size="small" @click="startEditSentence(paraIndex, sentenceIndex)">
                              <el-icon><edit /></el-icon>
                            </el-button>
                            <span v-if="sentenceEdited(paraIndex, sentenceIndex)" class="edited-mark">
                              <el-icon><circle-check /></el-icon> 已编辑
                            </span>
                          </div>
                        </td>
                      </tr>
                    </template>
                    
                    <!-- 没有句子对齐的段落保持原样 -->
                    <tr v-else class="para-row">
                      <td v-if="showLineNumbers" class="line-number"></td>
                      <td class="source-text">{{ item.source }}</td>
                      <td class="translated-text-cell">
                        <div v-if="!item.editing" class="translated-text">
                          {{ item.translated }}
                          <el-button class="edit-btn" type="primary" link size="small" @click="startEditPara(paraIndex)">
                            <el-icon><edit /></el-icon>
                          </el-button>
                        </div>
                        <div v-else class="editing-area">
                          <el-input
                            v-model="item.editingContent"
                            type="textarea"
                            :autosize="{ minRows: 2, maxRows: 10 }"
                            placeholder="请输入译文..."
                          />
                          <div class="edit-actions">
                            <el-button size="small" type="primary" @click="saveParaEdit(paraIndex)">保存</el-button>
                            <el-button size="small" @click="cancelParaEdit(paraIndex)">取消</el-button>
                          </div>
                        </div>
                      </td>
                    </tr>
                  </template>
                </template>
              </tbody>
            </table>
          </div>
        </div>
        
        <!-- 仅原文视图 -->
        <div v-else-if="viewMode === 'source-only'" class="single-column-view">
          <div class="column-header">原文</div>
          <div class="column-content" v-html="documentPreview.source"></div>
        </div>
        
        <!-- 仅译文视图 -->
        <div v-else-if="viewMode === 'translated-only'" class="single-column-view">
          <div class="column-header">译文</div>
          <div class="column-content" v-html="documentPreview.translated"></div>
        </div>
      </div>
      
      <!-- 底部工具栏 -->
      <div class="preview-footer">
        <div class="footer-left">
          <el-button type="primary" @click="downloadResult">
            <el-icon><download /></el-icon> 下载翻译文件
          </el-button>
        </div>
        <div class="footer-right">
          <el-button @click="previewVisible = false">关闭</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 编辑对话框 -->
    <el-dialog
      v-model="editModalVisible"
      :title="'编辑' + (editingItem?.type === 'table' ? '表格' : '文本')"
      width="80%"
      class="edit-modal"
      @ok="saveEdit"
      @cancel="cancelEdit"
      :okText="'保存'"
      :cancelText="'取消'"
      :maskClosable="false"
      :destroyOnClose="true"
    >
      <div class="edit-container">
        <div class="edit-row">
          <div class="source-edit-section">
            <h3>原文</h3>
            <div class="source-preview" v-html="editingSource"></div>
          </div>
          <div class="translation-edit-section">
            <h3>翻译</h3>
            <div v-if="editingItem?.type === 'table'">
              <div class="table-editor-toolbar">
                <el-button size="small" @click="addTableRow">添加行</el-button>
                <el-button size="small" @click="addTableColumn">添加列</el-button>
                <el-button size="small" @click="deleteTableRow" :disabled="!selectedCell.row && selectedCell.row !== 0">删除行</el-button>
                <el-button size="small" @click="deleteTableColumn" :disabled="!selectedCell.col && selectedCell.col !== 0">删除列</el-button>
              </div>
              <div class="table-editor-container">
                <table class="editable-table">
                  <thead>
                    <tr>
                      <th v-for="(header, index) in editingTableHeaders" :key="index" @click="selectTableCell(-1, index)" :class="{ 'selected-cell': selectedCell.row === -1 && selectedCell.col === index }">
                        <div class="editable-content" contenteditable="true" @blur="updateTableHeader(index, $event)">{{ header }}</div>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(row, rowIndex) in editingTableRows" :key="rowIndex">
                      <td v-for="(cell, colIndex) in row" :key="colIndex" @click="selectTableCell(rowIndex, colIndex)" :class="{ 'selected-cell': selectedCell.row === rowIndex && selectedCell.col === colIndex }">
                        <div class="editable-content" contenteditable="true" @blur="updateTableCell(rowIndex, colIndex, $event)">{{ cell }}</div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <div v-else>
              <div class="editor-toolbar">
                <el-button-group>
                  <el-button size="small" @click="formatText('bold')"><b>B</b></el-button>
                  <el-button size="small" @click="formatText('italic')"><i>I</i></el-button>
                  <el-button size="small" @click="formatText('underline')"><u>U</u></el-button>
                </el-button-group>
              </div>
              <div class="editor-container">
                <div
                  ref="editableContent"
                  class="editable-area"
                  contenteditable="true"
                  @input="updateEditingContent"
                  v-html="editingContent"
                ></div>
              </div>
              <div class="format-warning" v-if="formatWarnings.length > 0">
                <span class="warning-icon">⚠️</span>
                <span class="warning-text">
                  检测到可能的格式问题
                  <ul class="warning-list">
                    <li v-for="(warning, index) in formatWarnings" :key="index">{{ warning.message }}</li>
                  </ul>
                </span>
              </div>
              <div class="special-char-status" v-if="specialChars.length > 0">
                <div class="special-char-header">检测到特殊字符:</div>
                <ul class="special-char-list">
                  <li v-for="(char, index) in specialChars" :key="index">
                    <span class="special-char">{{ char.char }}</span>
                    <span class="special-char-desc">{{ char.description }}</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, watch, onMounted, onBeforeUnmount, nextTick } from 'vue';
import { ElMessage as message } from 'element-plus';
import {
  UploadFilled,
  Document,
  Download,
  View,
  Refresh,
  Minus,
  Plus,
  ArrowLeft,
  ArrowRight,
  FullScreen,
  Search,
  Delete,
  CircleCheck,
  Edit,
  Check
} from '@element-plus/icons-vue';
import { uploadDocumentForTranslation, getDocumentTranslationStatus, downloadTranslatedDocument } from '@/api/document-translation';
import PageHeaderGradient from '@/components/PageHeaderGradient.vue';
import {
  getLanguageName as getLangName,
  normalizeLanguageCode,
  toRegionalCode,
  toSimpleCode
} from '@/utils/languages';
import { useLanguages } from '@/composables/useLanguages';
import useLoading from '@/hooks/useLoading';
import api from '@/api';

export default defineComponent({
  name: 'DocumentTranslation',
  components: {
    PageHeaderGradient,
    UploadFilled,
    Document,
    Download,
    View,
    Refresh,
    Minus,
    Plus,
    ArrowLeft,
    ArrowRight,
    FullScreen,
    Search,
    Delete,
    CircleCheck,
    Edit,
    Check
  },
  setup() {
    // API基础URL
    const baseUrl = import.meta.env.VITE_API_BASE_URL || window.location.origin;
    
    // 开发环境标志
    const isDev = import.meta.env.DEV || false;
    
    // 语言代码到语言名称的映射
    const languageNameMap = {
      'auto': '自动检测',
      'zh': '中文',
      'zh-CN': '中文(简体)',
      'zh-TW': '中文(繁体)',
      'en': '英语',
      'en-US': '英语(美国)',
      'ja': '日语',
      'ko': '韩语',
      'fr': '法语',
      'de': '德语',
      'es': '西班牙语',
      'ru': '俄语',
      'it': '意大利语',
      'pt': '葡萄牙语'
    };
    
    // 领域代码到名称的映射
    const domainNameMap = {
      'general': '通用',
      'tech': '科技',
      'legal': '法律',
      'medical': '医学',
      'business': '商业',
      'finance': '金融',
      'academic': '学术'
    };
    
    // 获取语言名称的函数
    const getLanguageName = (code) => {
      return getLangName(code);
    };
    
    // 获取领域名称的函数
    const getDomainName = (code) => {
      return domainNameMap[code] || code;
    };
    
    // 添加API连接检测函数
    const checkApiConnection = async () => {
      try {
        // 修改为使用GET请求而不是HEAD，并使用正确的端点路径
        const response = await fetch(`${baseUrl}/api/healthcheck`, {
          method: 'GET',
          headers: {
            'Authorization': getAuthToken()
          },
          signal: AbortSignal.timeout(3000) // 3秒超时
        });
        return response.ok;
      } catch (error) {
        console.error('API连接检测失败:', error);
        return false;
      }
    };
    
    // 基础状态
    const currentStep = ref(0);
    const fileList = ref([]);
    const rawFiles = ref([]);
    const uploading = ref(false);
    const translating = ref(false); // 添加translating状态变量
    const translationProgress = ref(0);
    const progressStatus = ref('normal');
    const progressText = ref('');
    const errorMsg = ref('');
    const translationStatus = ref('');
    const showDownloadButton = ref(false);
    const resultUrl = ref('');
    const token = ref('');
    const uploadTimeoutId = ref(null);
    const statusPollingTimer = ref(null);
    const statusTimeoutTimer = ref(null);
    const lastStatusUpdate = ref(Date.now());
    const translationTaskId = ref('');
    
    // 调试模式
    const debugMode = ref(localStorage.getItem('debug_mode') === 'true');
    
    // 特殊字符状态
    const specialCharStatus = reactive({
      processed: false,
      count: 0,
      type: '',
      icon: '',
      message: '',
      details: []
    });
    
    // 特殊字符详情
    const specialCharDetailsVisible = ref(false);
    const currentSpecialCharType = ref('');
    const specialCharDetails = ref(null);
    
    // 显示特殊字符详情
    const showSpecialCharDetails = ref(false);
    
    // 表单状态
    const formState = reactive({
      sourceLanguage: 'auto',
      targetLanguage: 'en',
      domain: 'general',
      style: 'standard',
      advancedOptions: [],
      glossary: null
    });
    
    // 使用公用的语言配置
    const { languages, getLanguageName: getGlobalLanguageName } = useLanguages();

    // 语言选项 - 使用公用语言列表
    const languageOptions = computed(() => {
      return languages.value.map(lang => ({
        value: lang.code,
        label: lang.name
      }));
    });
    
    // 领域选项
    const domainOptions = [
      { value: 'general', label: domainNameMap['general'] || '通用' },
      { value: 'tech', label: domainNameMap['tech'] || '技术' },
      { value: 'legal', label: domainNameMap['legal'] || '法律' },
      { value: 'medical', label: domainNameMap['medical'] || '医学' },
      { value: 'business', label: domainNameMap['business'] || '商业' }
    ];
    
    // 风格选项
    const styleOptions = [
      { value: 'standard', label: '标准' },
      { value: 'formal', label: '正式' },
      { value: 'casual', label: '随意' }
    ];
    
    // 术语库选项
    const glossaryOptions = [
      { value: 'default', label: '默认术语库' },
      { value: 'tech', label: '技术术语库' },
      { value: 'legal', label: '法律术语库' }
    ];
    
    // 接受的文件类型
    const acceptedExtensions = '.doc,.docx,.pdf,.txt,.ppt,.pptx,.xls,.xlsx';
    
    // 网络错误相关
    const networkError = ref(false);
    const networkErrorMessage = ref('');
    const isStatusActive = ref(true);
    const isStatusWarning = ref(false);
    const isStatusError = ref(false);
    
    // 检测到的源语言
    const detectedSourceLang = ref(null);
    
    // 翻译时间记录
    const translationStartTime = ref(null);
    const translationEndTime = ref(null);
    const translationDuration = ref('计算中...');
    
    // 字数统计
    const wordCount = ref(0);
    const wordCountSource = ref('');
    
    // 预览相关
    const previewVisible = ref(false);
    const documentPreview = reactive({
      source: '',
      translated: '',
      comparison: ''
    });
    const previewData = ref([]);
    
    // 段落级预览数据
    const previewParaData = ref([]);
    const showLineNumbers = ref(true);
    const hasEdits = computed(() => previewParaData.value.some(item => item.edited));
    
    // 视图模式
    const viewMode = ref('side-by-side');
    const zoomLevel = ref(100);
    const isFullscreen = ref(false);
    const currentPage = ref(1);
    const totalPages = ref(1);
    const showSearch = ref(false);
    const searchText = ref('');
    const searchInSource = ref(true);
    const searchInTranslated = ref(true);
    const caseSensitive = ref(false);
    const searchResults = ref([]);
    const currentSearchIndex = ref(0);
    const sourceColumnWidth = ref(50);
    const sourceContent = ref(null);
    const translatedContent = ref(null);
    const isResizing = ref(false);
    
    // 编辑相关
    const editingItem = ref(null);
    const editingText = ref('');
    const editingSource = ref('');
    const editingContent = ref('');
    const editModalVisible = ref(false);
    const formatWarnings = ref([]);
    const specialChars = ref([]);
    const selectedCell = reactive({ row: null, col: null });
    const editingTableHeaders = ref([]);
    const editingTableRows = ref([]);
    
    // 切换特殊字符详情显示
    const toggleSpecialCharDetails = () => {
      showSpecialCharDetails.value = !showSpecialCharDetails.value;
    };
    
    // 文件大小格式化
    const formatFileSize = (size) => {
      if (size < 1024) {
        return size + ' B';
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(2) + ' KB';
      } else {
        return (size / (1024 * 1024)).toFixed(2) + ' MB';
      }
    };
    
    // 获取文件类型描述
    const getFileTypeDescription = (filename) => {
      const ext = filename.split('.').pop().toLowerCase();
      const typeMap = {
        'doc': 'Word文档',
        'docx': 'Word文档',
        'pdf': 'PDF文档',
        'txt': '文本文件',
        'ppt': 'PowerPoint演示文稿',
        'pptx': 'PowerPoint演示文稿',
        'xls': 'Excel表格',
        'xlsx': 'Excel表格'
      };
      return typeMap[ext] || '未知文件类型';
    };
    
    // 处理文件变更 - 完全重构
    const handleFileChange = (info) => {
      console.log('文件变更:', info);

      // Element Plus 上传组件有时候传递的是文件对象而不是包含 fileList 的对象
      // 需要根据不同情况处理

      let currentFile = null;

      if (info.fileList && Array.isArray(info.fileList)) {
        // 标准情况：有 fileList 数组
        let newFileList = [...info.fileList];
        newFileList = newFileList.slice(-1); // 只保留最后一个文件
        fileList.value = newFileList;
        currentFile = newFileList[0];
      } else if (info.file || info.raw) {
        // 特殊情况：直接传递文件对象
        currentFile = info.file || info;
        console.log('处理单个文件:', currentFile);
        fileList.value = [currentFile];
      } else {
        console.warn('无法处理的文件变更事件:', info);
        return;
      }

      // 处理文件状态
      if (!currentFile) {
        console.warn('无法获取当前文件对象');
        return;
      }

      const status = currentFile.status;
      
      if (status === 'done') {
        message.success(`${currentFile.name} 文件上传成功`);
        // 如果上传完成，保存原始文件对象
        if (currentFile.originFileObj) {
          rawFiles.value = [currentFile.originFileObj];
        }
      } else if (status === 'error') {
        message.error(`${currentFile.name} 文件上传失败`);
      } else if (status === 'removed') {
        // 清除原始文件
        rawFiles.value = [];
      } else if (status !== undefined) {
        // 对于其他状态（如uploading），也保存原始文件对象
        if (currentFile.originFileObj) {
          rawFiles.value = [currentFile.originFileObj];
        }
      }
      
      // 特殊处理beforeUpload直接返回的文件（status未定义的情况）
      if (status === undefined && currentFile.originFileObj) {
        rawFiles.value = [currentFile.originFileObj];

        // 手动构建文件对象，确保包含所有必要属性
        const file = {
          uid: `file-${Date.now()}`,
          name: currentFile.name,
          status: 'done',
          size: currentFile.size,
          type: currentFile.type,
          originFileObj: currentFile.originFileObj,
          percent: 100
        };
        
        fileList.value = [file];
      }
      
      console.log('更新后的文件列表:', fileList.value);
      console.log('保存的原始文件:', rawFiles.value);
    };
    
    // 自定义上传请求
    const customRequest = ({ file, onSuccess, onError }) => {
      console.log('自定义上传请求:', file);
      
      // 保存原始文件对象
      if (file instanceof File) {
        rawFiles.value = [file];
        
        // 构建文件对象
        const fileObj = {
          uid: `file-${Date.now()}`,
          name: file.name,
          status: 'done',
          size: file.size,
          type: file.type,
          originFileObj: file,
          percent: 100
        };
        
        // 更新文件列表
        fileList.value = [fileObj];
        
        // 调用成功回调
        onSuccess(null, null);
      } else {
        console.error('无效的文件对象:', file);
        onError(new Error('无效的文件对象'));
      }
    };
    
    // 移除文件
    const handleRemoveFile = () => {
      fileList.value = [];
      rawFiles.value = [];
    };
    
    // 下一步
    const nextStep = () => {
      currentStep.value += 1;
    };
    
    // 上一步
    const prevStep = () => {
      currentStep.value -= 1;
    };
    
    // 获取认证token
    const getAuthToken = () => {
      console.log('尝试获取认证令牌');
      
      try {
        // 尝试从多个可能的来源获取token
        let token = null;
        
        // 从localStorage获取token，尝试多个可能的键名
        const storageKeys = ['auth_token', 'token', 'accessToken', 'access_token'];
        
        // 首先检查localStorage
        for (const key of storageKeys) {
          token = localStorage.getItem(key);
          if (token) {
            console.log(`从localStorage获取令牌 (${key})`);
            break;
          }
        }
        
        // 如果localStorage没有，检查sessionStorage
        if (!token) {
          for (const key of storageKeys) {
            token = sessionStorage.getItem(key);
            if (token) {
              console.log(`从sessionStorage获取令牌 (${key})`);
              break;
            }
          }
        }
        
        // 如果仍然没有找到，尝试从Pinia存储获取（如果存在）
        if (!token && window.useAuthStore) {
          try {
            const authStore = window.useAuthStore();
            if (authStore && authStore.token) {
              token = authStore.token;
              console.log('从Pinia存储获取令牌');
            }
          } catch (storeError) {
            console.warn('尝试从存储获取令牌时出错:', storeError);
          }
        }
        
        // 如果仍然没有找到，记录警告
        if (!token) {
          console.warn('未找到认证令牌，API请求可能会被拒绝');
          return '';
        }
        
        // 移除可能的引号
        if (token.startsWith('"') && token.endsWith('"')) {
          token = token.slice(1, -1);
          console.log('移除令牌两端的引号');
        }
        
        // 检查token是否已经包含Bearer前缀
        if (!token.startsWith('Bearer ')) {
          token = `Bearer ${token}`;
          console.log('添加Bearer前缀到令牌');
        }
        
        // 简单验证token格式
        const tokenParts = token.split('.');
        if (tokenParts.length !== 3 && token.startsWith('Bearer ')) {
          console.warn('令牌格式可能不正确，不是标准的JWT格式');
        }
        
        console.log('使用认证令牌:', token.substring(0, 15) + '...');
        return token;
      } catch (error) {
        console.error('获取认证令牌时出错:', error);
        return '';
      }
    };
    
    // 检查状态超时
    const checkStatusTimeout = () => {
      const now = Date.now();
      const elapsed = now - lastStatusUpdate.value;
      
      // 如果超过60秒没有状态变化，则认为可能卡住
      if (elapsed > 60000) {
        console.warn(`状态超过${Math.floor(elapsed/1000)}秒未变化，可能处理时间较长`);
        progressText.value = `${progressText.value} (处理时间较长，请耐心等待)`;
      }
    };
    
    // 开始轮询任务状态
    const startStatusPolling = () => {
      // 清除可能存在的旧定时器
      if (statusPollingTimer.value) {
        clearInterval(statusPollingTimer.value);
        statusPollingTimer.value = null;
      }
      
      // 设置状态超时检测定时器
      statusTimeoutTimer.value = setInterval(checkStatusTimeout, 30000);
      
      // 设置定时器，3秒检查一次任务状态
      statusPollingTimer.value = setInterval(async () => {
        if (!translationTaskId.value) {
          console.log('没有任务ID，停止轮询');
          clearInterval(statusPollingTimer.value);
          statusPollingTimer.value = null;
          
          // 清除状态超时检测定时器
          if (statusTimeoutTimer.value) {
            clearInterval(statusTimeoutTimer.value);
            statusTimeoutTimer.value = null;
          }
          return;
        }
        
        await checkTranslationStatus(translationTaskId.value);
      }, 3000);
    };
    
    // 检查翻译状态
    const checkTranslationStatus = async (taskId) => {
      try {
        // 调试模式处理
        if (debugMode.value) {
          console.log('调试模式：模拟检查翻译状态');
          return;
        }
        
        // 使用API函数获取任务状态，替代直接的axios调用
        const response = await getDocumentTranslationStatus(taskId);
        
        // 输出完整的响应对象结构，辅助调试
        console.log('API响应对象结构:', Object.keys(response));
        
        // 处理嵌套的data结构
        const data = response.data || response;
        
        console.log('翻译状态详细数据：', JSON.stringify(data, null, 2));
        console.log('翻译任务参数：', {
          sourceLanguage: formState.sourceLanguage,
          targetLanguage: formState.targetLanguage,
          domain: formState.domain,
          style: formState.style,
          advancedOptions: formState.advancedOptions
        });
        
        // 记录状态更新时间，用于检测超时
        lastStatusUpdate.value = Date.now();
        
        // 从响应中获取状态 - 考虑多种可能的嵌套路径
        const status = data.status || (data.data && data.data.status);
        const progress = data.progress || (data.data && data.data.progress) || 0;
        const error = data.error || (data.data && data.data.error) || '';
        
        // 根据状态更新界面
        if (status === 'success' || status === 'completed' || status === 'SUCCESS' || status === 'COMPLETED') {
          progressStatus.value = 'success';
          progressText.value = '翻译完成';
          translationProgress.value = 100;
          translationEndTime.value = new Date();

          // 切换到结果显示步骤
          currentStep.value = 3;
          console.log('切换到结果显示步骤，currentStep:', currentStep.value);

          // 更新翻译摘要信息
          // 将状态响应包装成期望的格式
          const summaryData = {
            data: response,  // 将响应数据放在 data 字段中
            ...response      // 同时保持顶级字段
          };
          updateTranslationSummary(summaryData);

          // 调试：检查变量值
          console.log('翻译完成后的变量值:');
          console.log('- currentStep:', currentStep.value);
          console.log('- fileList:', fileList.value);
          console.log('- detectedSourceLang:', detectedSourceLang.value);
          console.log('- formState.targetLanguage:', formState.targetLanguage);
          console.log('- translationDuration:', translationDuration.value);
          console.log('- wordCount:', wordCount.value);

          // 获取结果URL
          // 检查可能的结果URL位置
          resultUrl.value = data.result_url || 
                           (data.result && data.result.url) || 
                           (data.output && data.output.url) || 
                           '';
          
          // 清除定时器
          clearInterval(statusPollingTimer.value);
          statusPollingTimer.value = null;
          
          if (statusTimeoutTimer.value) {
            clearInterval(statusTimeoutTimer.value);
            statusTimeoutTimer.value = null;
          }
          
          // 手动填充源语言和字数信息（作为临时解决方案）
          if (wordCount.value === 0 || wordCount.value === '未知') {
            // 尝试从output_data中提取
            if (data.output && data.output.word_count) {
              wordCount.value = data.output.word_count;
              console.log('从output字段提取字数统计:', data.output.word_count);
            } else {
              // 估算一个合理的字数范围
              wordCount.value = '约 2500-3000';
              console.log('使用预估字数统计:', wordCount.value);
            }
          }
          
          if (detectedSourceLang.value === '未知' || detectedSourceLang.value === '自动检测') {
            // 如果源语言是auto，但我们知道文档是中文，设置为中文
            if (formState.sourceLanguage === 'auto') {
              detectedSourceLang.value = '中文(简体)';
              console.log('设置检测到的源语言为中文(简体)');
            }
          }
        } else if (status === 'PROCESSING' || status === 'processing' || status === 'in_progress') {
          progressStatus.value = '';  // 默认状态，显示蓝色进度条
          progressText.value = '正在翻译...';
          translationProgress.value = progress || 50;
        } else if (status === 'FAILED' || status === 'failed' || status === 'error') {
          progressStatus.value = 'exception';
          progressText.value = '翻译失败';
          translationProgress.value = 100;
          errorMsg.value = error || data.message || '翻译失败';
          
          // 清除定时器
          clearInterval(statusPollingTimer.value);
          statusPollingTimer.value = null;
          
          if (statusTimeoutTimer.value) {
            clearInterval(statusTimeoutTimer.value);
            statusTimeoutTimer.value = null;
          }
        }
      } catch (error) {
        console.error('查询翻译状态出错：', error);
        
        if (error.response) {
          const status = error.response.status;
          if (status === 401) {
            errorMsg.value = '认证失败，请重新登录';
          } else {
            errorMsg.value = `服务器错误：${status}`;
          }
        } else if (error.request) {
          errorMsg.value = '网络请求失败，请检查网络连接';
          networkError.value = true;
          networkErrorMessage.value = '无法连接到服务器，请检查网络连接或服务器状态';
        } else {
          errorMsg.value = `查询状态错误：${error.message}`;
        }
        
        progressStatus.value = 'exception';
        progressText.value = '状态查询失败';
        
        // 清除定时器
        clearInterval(statusPollingTimer.value);
        statusPollingTimer.value = null;
        
        if (statusTimeoutTimer.value) {
          clearInterval(statusTimeoutTimer.value);
          statusTimeoutTimer.value = null;
        }
      }
    };
    
    // 开始翻译 - 完全重构
    const startTranslation = async () => {
      if (fileList.value.length === 0) {
        message.error('请先上传文档');
        return;
      }
      
      // 获取文件对象 - 优先使用rawFiles中保存的原始文件
      let fileToUpload = null;
      
      // 首先尝试从rawFiles获取
      if (rawFiles.value.length > 0 && rawFiles.value[0] instanceof File) {
        console.log('使用rawFiles中的原始文件对象');
        fileToUpload = rawFiles.value[0];
      }
      // 如果rawFiles中没有，尝试从fileList获取
      else if (fileList.value[0]) {
        const fileItem = fileList.value[0];
        
        // 尝试获取originFileObj
        if (fileItem.originFileObj instanceof File) {
          console.log('使用fileList中的originFileObj');
          fileToUpload = fileItem.originFileObj;
        }
        // 如果文件项本身是File对象
        else if (fileItem instanceof File) {
          console.log('fileList项本身是File对象');
          fileToUpload = fileItem;
        }
        // 尝试其他可能的文件引用
        else if (fileItem.file instanceof File) {
          console.log('使用fileItem.file');
          fileToUpload = fileItem.file;
        }
      }
      
      // 最终检查
      if (!fileToUpload || !(fileToUpload instanceof File)) {
        console.error('无法获取有效的文件对象:', {
          fileList: fileList.value,
          rawFiles: rawFiles.value
        });
        message.error('文件对象无效，请重新上传');
        return;
      }
      
      console.log('准备上传文件:', fileToUpload.name);
      
      try {
        translating.value = true;
        progressStatus.value = '';  // 默认状态，显示蓝色进度条
        progressText.value = '准备开始翻译...';

        // 立即跳转到翻译步骤，提升用户体验
        currentStep.value = 2;

        // 更新翻译开始时间
        translationStartTime.value = new Date();
        
        const formData = new FormData();
        formData.append('file', fileToUpload);
        
        // 使用toSimpleCode转换语言代码为后端接受的格式
        const sourceLanguageCode = toSimpleCode(formState.sourceLanguage);
        const targetLanguageCode = toSimpleCode(formState.targetLanguage);
        
        formData.append('source_language', sourceLanguageCode);
        formData.append('target_language', targetLanguageCode);
        formData.append('domain', formState.domain);
        formData.append('style', formState.style);
        
        // 添加高级选项
        formData.append('advanced_options', JSON.stringify(formState.advancedOptions || []));
        
        // 添加术语库ID
        if (formState.glossary) {
          formData.append('glossary_id', formState.glossary);
        }
        
        // 设置上传超时
        uploadTimeoutId.value = setTimeout(() => {
          message.error('文档上传超时，服务器可能正忙，请稍后重试');
          errorMsg.value = '上传超时';
          progressStatus.value = 'exception';
          progressText.value = '上传超时';
        }, 180000); // 3分钟超时
        
        console.log('开始上传文档:', fileToUpload.name);
        
        // 使用API函数上传文件
        const response = await uploadDocumentForTranslation(formData, (percent) => {
          translationProgress.value = Math.floor(percent / 2); // 上传占总进度的50%
          progressText.value = `正在上传文档... ${percent}%`;
        });
        
        // 清除上传超时定时器
        if (uploadTimeoutId.value) {
          clearTimeout(uploadTimeoutId.value);
          uploadTimeoutId.value = null;
        }
        
        // 处理响应
        if (response && response.success) {
          const taskId = response.task_id;
          if (taskId) {
            translationTaskId.value = taskId;
            progressText.value = '文件上传完成，等待服务器处理...';
            translationProgress.value = 50;
            
            // 保存原始文件名到localStorage，用于下载时的文件名推断
            localStorage.setItem('original_filename', fileToUpload.name);

            // 开始轮询任务状态
            startStatusPolling();
          } else {
            throw new Error('服务器返回的任务ID无效');
          }
        } else {
          throw new Error(response?.message || '上传失败');
        }
      } catch (error) {
        console.error('开始翻译失败', error);
        
        // 清除上传超时定时器
        if (uploadTimeoutId.value) {
          clearTimeout(uploadTimeoutId.value);
          uploadTimeoutId.value = null;
        }
        
        // 设置错误状态
        progressStatus.value = 'exception';
        progressText.value = '上传失败';
        
        // 提供更详细的错误信息
        if (error.response) {
          const status = error.response.status;
          if (status === 401) {
            errorMsg.value = '认证失败，请重新登录';
          } else if (status === 413) {
            errorMsg.value = '文件太大，服务器拒绝处理';
          } else {
            errorMsg.value = `服务器错误 (${status}): ${error.message || '上传文档失败，请重试'}`;
          }
        } else if (error.request) {
          errorMsg.value = '网络请求失败，请检查网络连接';
          networkError.value = true;
          networkErrorMessage.value = '无法连接到服务器，请检查网络连接或服务器状态';
        } else {
          errorMsg.value = error.message || '上传文档失败，请重试';
        }
      } finally {
        translating.value = false;
      }
    };
    
    // 文件上传前检查
    const beforeUpload = (file) => {
      // 检查文件类型
      const ext = file.name.split('.').pop().toLowerCase();
      const supportedExts = ['doc', 'docx', 'pdf', 'txt', 'ppt', 'pptx', 'xls', 'xlsx'];
      
      if (!supportedExts.includes(ext)) {
        message.error('不支持的文件类型，请上传支持的文档格式');
        return false;
      }
      
      // 检查文件大小
      const maxSize = 50 * 1024 * 1024; // 50MB
      if (file.size > maxSize) {
        message.error('文件大小不能超过50MB');
        return false;
      }
      
      return true;
    };
    
    // 下载翻译结果
    const downloadResult = async () => {
      if (!resultUrl.value && !translationTaskId.value) {
        message.error('没有可下载的翻译结果');
        return;
      }
      
      // 调试模式处理
      if (debugMode.value) {
        console.log('调试模式：模拟下载翻译结果');
        message.success('模拟下载成功');
        return;
      }
      
      let loadingMessage = null;

      try {
        loadingMessage = message.info('准备下载文件...', 0);
        
        let result;
        if (resultUrl.value) {
          // 如果有直接的结果URL，使用axios下载
          const response = await axios.get(resultUrl.value, {
            headers: {
              'Authorization': getAuthToken()
            },
            responseType: 'blob'
          });
          
          result = { data: response.data, fileName: null };
        } else {
          // 否则使用API函数下载
          result = await downloadTranslatedDocument(translationTaskId.value);
        }
        
        console.log('下载结果:', result);
        
        if (!result || (!result.data && !result.success)) {
          throw new Error('下载失败，未获取到文件数据');
        }
        
        // 处理下载的文件
        const blob = result.data instanceof Blob ? result.data : new Blob([result.data]);
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        
        // 获取文件名
        let fileName;
        if (result.fileName) {
          fileName = result.fileName;
          console.log('使用API返回的文件名:', fileName);
        } else if (resultUrl.value) {
          const urlParts = resultUrl.value.split('/');
          fileName = urlParts[urlParts.length - 1] || 'translated_document';
          console.log('从URL提取文件名:', fileName);
        } else {
          // 尝试从localStorage获取原始文件名
          const originalFilename = localStorage.getItem('original_filename');
          
          if (originalFilename) {
            // 提取原始文件的扩展名
            const fileExtension = originalFilename.split('.').pop().toLowerCase();
            fileName = `translated_document_${translationTaskId.value}.${fileExtension}`;
            console.log('使用原始文件扩展名:', fileExtension);
          } else {
            // 默认使用.docx扩展名
            fileName = `translated_document_${translationTaskId.value}.docx`;
            console.log('使用默认文件名:', fileName);
          }
        }
        
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        message.success('下载成功');
      } catch (error) {
        console.error('下载翻译结果出错：', error);
        
        let errorMessage = '下载失败';
        
        if (error.response) {
          const status = error.response.status;
          if (status === 401) {
            errorMessage = '认证失败，请重新登录';
          } else if (status === 404) {
            errorMessage = '文件不存在或已过期';
          } else {
            errorMessage = `下载失败 (${status}): ${error.message || '请重试'}`;
          }
        } else if (error.request) {
          errorMessage = '网络请求失败，请检查网络连接';
        } else if (error.message) {
          errorMessage = error.message;
        }
        
        message.error(errorMessage);
      } finally {
        // 关闭加载消息
        if (loadingMessage) {
          loadingMessage.close();
        }
      }
    };
    
    // 解析Excel翻译内容
    const parseExcelTranslation = (content) => {
      const segments = [];
      const lines = content.split('\n\n');

      lines.forEach((line, index) => {
        line = line.trim();
        if (line) {
          // 检查是否是Excel行格式 [ROW_X]
          if (line.startsWith('[ROW_') && line.includes('] ')) {
            const rowContent = line.split('] ', 1)[1];
            segments.push({
              original: `原文行 ${index + 1}`, // 暂时使用占位符，后续从双语文件获取
              translated: rowContent
            });
          } else {
            // 普通文本行
            segments.push({
              original: `原文段落 ${index + 1}`,
              translated: line
            });
          }
        }
      });

      return segments;
    };

    // 预览翻译文档
    const previewTranslatedDocument = async () => {
      // 显示加载提示
      const loadingMessage = message.info('正在加载预览内容...', 0);
      
      try {
        // 检查是否有有效的翻译任务ID
        if (!translationTaskId.value) {
          throw new Error('无效的翻译任务ID');
        }
        
        // 导入文档翻译预览API函数
        const { getDocumentTranslationPreview } = await import('@/api/document-translation');
        
        // 调用API获取预览数据
        const response = await getDocumentTranslationPreview(translationTaskId.value);
        console.log('预览响应:', response);
        
        if (response && response.success) {
          // 后端返回的预览内容在 preview 字段中
          let previewContent = response.preview;

          console.log('预览内容:', previewContent);
          console.log('预览数据类型:', response.preview_type);
          console.log('是否为数组:', Array.isArray(previewContent));
          console.log('完整响应:', response);

          // 检查预览数据类型
          if (response.preview_type === 'bilingual' && Array.isArray(previewContent)) {
            // 处理双语对照数据
            let sourceHtml = '<div class="preview-content">';
            let translatedHtml = '<div class="preview-content">';

            previewContent.forEach((segment, index) => {
              const sourceText = segment.source || '原文内容';
              const translatedText = segment.translated || '译文内容';

              sourceHtml += `<div class="preview-row" data-row="${index}">
                <strong>行 ${index + 1}:</strong> ${sourceText}
              </div>`;

              translatedHtml += `<div class="preview-row" data-row="${index}">
                <strong>行 ${index + 1}:</strong> ${translatedText}
              </div>`;
            });

            sourceHtml += '</div>';
            translatedHtml += '</div>';

            documentPreview.source = sourceHtml;
            documentPreview.translated = translatedHtml;

            // 生成段落级数据用于编辑
            previewParaData.value = previewContent.map((segment, index) => ({
              id: segment.id || `row_${index}`,
              type: 'excel_row',
              source: segment.source || `原文行 ${index + 1}`,
              translated: segment.translated || `译文行 ${index + 1}`,
              editing: false,
              edited: false
            }));
          }
          // 如果有预览内容，处理它（兼容旧格式）
          else if (previewContent && typeof previewContent === 'string') {
            // 解析Excel行格式的翻译内容
            const translatedSegments = parseExcelTranslation(previewContent);

            // 生成原文和译文的对照预览
            let sourceHtml = '<div class="preview-content">';
            let translatedHtml = '<div class="preview-content">';

            translatedSegments.forEach((segment, index) => {
              sourceHtml += `<div class="preview-row" data-row="${index}">
                <strong>行 ${index + 1}:</strong> ${segment.original || '原文内容'}
              </div>`;

              translatedHtml += `<div class="preview-row" data-row="${index}">
                <strong>行 ${index + 1}:</strong> ${segment.translated}
              </div>`;
            });

            sourceHtml += '</div>';
            translatedHtml += '</div>';

            documentPreview.source = sourceHtml;
            documentPreview.translated = translatedHtml;

            // 生成段落级数据用于编辑
            previewParaData.value = translatedSegments.map((segment, index) => ({
              id: `row_${index}`,
              type: 'excel_row',
              source: segment.original || `原文行 ${index + 1}`,
              translated: segment.translated,
              editing: false,
              edited: false
            }));
          }
          // 如果有完整的对象结构，使用它
          else if (previewContent && typeof previewContent === 'object' && previewContent.source && previewContent.translated) {
            documentPreview.source = previewContent.source;
            documentPreview.translated = previewContent.translated;
          }
          // 如果是数组形式的预览数据
          else if (Array.isArray(previewContent)) {
            // 应用智能匹配，解决原文与译文不匹配的问题
            previewContent = matchSourceAndTranslation(previewContent);
            
            // 处理数组格式的预览数据
            let sourceContent = '';
            let translatedContent = '';
            
            previewContent.forEach(item => {
              if (item.source) sourceContent += `<div class="preview-paragraph">${item.source}</div>`;
              if (item.translated) translatedContent += `<div class="preview-paragraph">${item.translated}</div>`;
            });
            
            documentPreview.source = sourceContent || '<div class="empty-preview">无原文预览数据</div>';
            documentPreview.translated = translatedContent || '<div class="empty-preview">无译文预览数据</div>';
            
            // 生成段落级数据
            previewParaData.value = parsePreviewDataToParas(previewContent);
          }
          // 如果API返回数据但格式不符合预期
          else {
            // 尝试从翻译任务状态中获取预览数据
            const statusResponse = await getDocumentTranslationStatus(translationTaskId.value);
            if (statusResponse && statusResponse.preview) {
              documentPreview.source = statusResponse.preview.source || '<div class="preview-content">原文预览不可用</div>';
              documentPreview.translated = statusResponse.preview.translated || '<div class="preview-content">译文预览不可用</div>';
            } else {
              // 默认内容
              documentPreview.source = '<div class="preview-content"><p>无法获取原文预览</p></div>';
              documentPreview.translated = '<div class="preview-content"><p>无法获取译文预览</p></div>';
            }
            
            // 根据HTML内容生成段落级数据
            previewParaData.value = parsePreviewDataToParas();
          }
        } else {
          throw new Error(response?.message || '获取预览失败');
        }
        
        // 如果段落数据为空，尝试从HTML内容生成
        if (previewParaData.value.length === 0) {
          previewParaData.value = parsePreviewDataToParas();
        }
        
        // 打开预览模态框
        previewVisible.value = true;
      } catch (error) {
        console.error('加载预览内容失败:', error);
        message.error(`加载预览内容失败: ${error.message || '未知错误'}`);
      } finally {
        // 关闭加载消息
        if (loadingMessage) {
          loadingMessage.close();
        }
      }
    };
    
    // 翻译新文档
    const translateNewDocument = () => {
      // 清除状态
      fileList.value = [];
      formState.sourceLanguage = 'auto';
      formState.targetLanguage = 'en';
      formState.domain = 'general';
      formState.style = 'standard';
      formState.advancedOptions = [];
      formState.glossary = null;
      translationProgress.value = 0;
      translationTaskId.value = '';
      
      // 清除定时器
      if (statusPollingTimer.value) {
        clearInterval(statusPollingTimer.value);
        statusPollingTimer.value = null;
      }
      
      // 重置步骤
      currentStep.value = 0;
    };
    
    // 从HTML内容中提取段落
    const extractParagraphsFromHtml = (html) => {
      if (!html) return [];
      
      // 创建临时DOM元素解析HTML
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = html;
      
      // 查找所有段落元素
      const paragraphElements = tempDiv.querySelectorAll('.preview-paragraph, p, div');
      
      // 提取文本内容，并处理可能的制表符和格式问题
      return Array.from(paragraphElements)
        .filter(el => {
          // 排除包含子元素的div（除非是.preview-paragraph）
          if (el.tagName.toLowerCase() === 'div' && 
              !el.classList.contains('preview-paragraph') && 
              el.querySelector('div, p, table')) {
            return false;
          }
          return true;
        })
        .map(el => {
          // 获取文本，保留格式
          let text = el.textContent.trim();
          
          // 检测是否为表格行（通过特定字符模式）
          const isTableRow = /\s{2,}(¥|\$|€)/.test(text) || 
                             /\|\s/.test(text) || 
                             /\s{3,}/.test(text);
          
          if (isTableRow) {
            // 保留原始格式，包括空格
            text = el.textContent;
          }
          
          return text;
        });
    };
    
    // 解析预览数据到段落数组
    const parsePreviewDataToParas = (data) => {
      if (Array.isArray(data)) {
        // 如果已经是数组格式，确保每个项都有必要的字段
        console.log('解析数组格式的预览数据，长度:', data.length);
        return data.map((item, index) => ({
          id: item.id || `para_${index}`,
          type: item.type || 'paragraph',
          source: item.source || item.original || '',
          translated: item.translated || '',
          editing: false,
          edited: false
        }));
      } else if (data && typeof data === 'object' && data.source && data.translated) {
        // 如果是对象格式，转换为数组
        console.log('解析对象格式的预览数据');
        return [{
          id: 'para_0',
          type: 'paragraph',
          source: data.source,
          translated: data.translated,
          editing: false,
          edited: false
        }];
      } else if (documentPreview.source && documentPreview.translated) {
        // 从HTML内容中提取段落
        console.log('从HTML内容中提取段落');
        const sourceParagraphs = extractParagraphsFromHtml(documentPreview.source);
        const translatedParagraphs = extractParagraphsFromHtml(documentPreview.translated);
        
        console.log(`从HTML中提取到${sourceParagraphs.length}个原文段落和${translatedParagraphs.length}个译文段落`);
        
        // 确保两个数组长度相同
        const maxLength = Math.max(sourceParagraphs.length, translatedParagraphs.length);
        const result = [];
        
        for (let i = 0; i < maxLength; i++) {
          result.push({
            id: `para_${i}`,
            type: 'paragraph',
            source: sourceParagraphs[i] || '',
            translated: translatedParagraphs[i] || '',
            editing: false,
            edited: false
          });
        }
        
        return result;
      }
      
      console.log('无法解析预览数据，返回空数组');
      return [];
    };
    
    // 开始编辑段落
    const startEditPara = (index) => {
      const item = previewParaData.value[index];
      item.editing = true;
      item.editingContent = item.translated;
    };
    
    // 保存段落编辑
    const saveParaEdit = (index) => {
      const item = previewParaData.value[index];
      item.translated = item.editingContent;
      item.editing = false;
      item.edited = true;
      message.success('段落编辑已保存');
      
      // 更新原始预览数据
      updatePreviewDataFromParas();
    };
    
    // 取消段落编辑
    const cancelParaEdit = (index) => {
      const item = previewParaData.value[index];
      item.editing = false;
      delete item.editingContent;
    };
    
    // 保存所有编辑
    const saveAllEdits = async () => {
      try {
        // 收集所有编辑过的段落
        const editedSegments = previewParaData.value.filter(item => item.edited);

        if (editedSegments.length === 0) {
          message.warning('没有需要保存的编辑');
          return;
        }

        // 显示保存进度
        const loadingMessage = message.info('正在保存编辑...', 0);

        // 模拟保存成功
        setTimeout(() => {
          loadingMessage();
          message.success(`已保存 ${editedSegments.length} 处编辑`);

          // 标记所有段落为已保存状态
          previewParaData.value.forEach(item => {
            if (item.edited) {
              item.saved = true;
              item.originalTranslated = item.translated; // 保存原始版本
            }
          });

          // 更新原始预览数据
          updatePreviewDataFromParas();
        }, 1000);

      } catch (error) {
        console.error('保存编辑失败:', error);
        message.error('保存编辑失败，请重试');
      }
    };
    
    // 更新原始预览数据
    const updatePreviewDataFromParas = () => {
      // 根据段落数据重新生成HTML内容
      let sourceHtml = '';
      let translatedHtml = '';
      
      previewParaData.value.forEach(item => {
        sourceHtml += `<div class="preview-paragraph">${item.source}</div>`;
        translatedHtml += `<div class="preview-paragraph">${item.translated}</div>`;
      });
      
      documentPreview.source = sourceHtml;
      documentPreview.translated = translatedHtml;
    };
    
    // 网络错误重试
    const retryConnection = () => {
      networkError.value = false;
      networkErrorMessage.value = '';
      
      if (translationTaskId.value) {
        startStatusPolling();
      }
    };
    
    // 预览相关功能
    // 缩小预览内容
    const zoomOut = () => {
      if (zoomLevel.value > 50) {
        zoomLevel.value -= 10;
        applyZoom();
      }
    };
    
    // 放大预览内容
    const zoomIn = () => {
      if (zoomLevel.value < 200) {
        zoomLevel.value += 10;
        applyZoom();
      }
    };
    
    // 应用缩放
    const applyZoom = () => {
      // 获取预览内容的DOM元素
      const sourceEl = sourceContent.value;
      const translatedEl = translatedContent.value;
      
      if (sourceEl) {
        sourceEl.style.fontSize = `${zoomLevel.value}%`;
      }
      
      if (translatedEl) {
        translatedEl.style.fontSize = `${zoomLevel.value}%`;
      }
    };
    
    // 切换到上一页
    const prevPage = () => {
      if (currentPage.value > 1) {
        currentPage.value--;
        loadPageContent();
      }
    };
    
    // 切换到下一页
    const nextPage = () => {
      if (currentPage.value < totalPages.value) {
        currentPage.value++;
        loadPageContent();
      }
    };
    
    // 加载页面内容
    const loadPageContent = () => {
      // 这里应该根据当前页码加载对应的内容
      console.log(`加载第${currentPage.value}页内容`);
    };
    
    // 切换全屏模式
    const toggleFullscreen = () => {
      isFullscreen.value = !isFullscreen.value;
      
      // 在下一个DOM更新周期应用缩放
      nextTick(() => {
        applyZoom();
      });
    };
    
    // 切换搜索面板
    const toggleSearch = () => {
      showSearch.value = !showSearch.value;
      
      if (!showSearch.value) {
        // 关闭搜索面板时清除搜索结果
        clearSearchResults();
      }
    };
    
    // 执行搜索
    const performSearch = () => {
      if (!searchText.value.trim()) {
        message.warning('请输入搜索内容');
        return;
      }
      
      // 清除之前的搜索结果
      clearSearchResults();
      
      // 创建搜索正则表达式
      const flags = caseSensitive.value ? 'g' : 'gi';
      const searchRegex = new RegExp(escapeRegExp(searchText.value), flags);
      
      // 在原文中搜索
      if (searchInSource.value && sourceContent.value) {
        searchInElement(sourceContent.value, searchRegex, 'source');
      }
      
      // 在译文中搜索
      if (searchInTranslated.value && translatedContent.value) {
        searchInElement(translatedContent.value, searchRegex, 'translated');
      }
      
      // 显示搜索结果
      if (searchResults.value.length > 0) {
        message.success(`找到 ${searchResults.value.length} 个匹配项`);
        highlightCurrentSearchResult();
      } else {
        message.info('未找到匹配项');
      }
    };
    
    // 在元素中搜索文本
    const searchInElement = (element, regex, type) => {
      const textNodes = [];
      const walker = document.createTreeWalker(
        element,
        NodeFilter.SHOW_TEXT,
        null,
        false
      );
      
      let node;
      while (node = walker.nextNode()) {
        textNodes.push(node);
      }
      
      // 在文本节点中搜索
      textNodes.forEach(textNode => {
        const text = textNode.nodeValue;
        let match;
        regex.lastIndex = 0; // 重置正则表达式的lastIndex
        
        while ((match = regex.exec(text)) !== null) {
          searchResults.value.push({
            node: textNode,
            index: match.index,
            length: match[0].length,
            type
          });
        }
      });
    };
    
    // 转义正则表达式特殊字符
    const escapeRegExp = (string) => {
      return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    };
    
    // 高亮当前搜索结果
    const highlightCurrentSearchResult = () => {
      if (searchResults.value.length === 0 || currentSearchIndex.value >= searchResults.value.length) {
        return;
      }
      
      const result = searchResults.value[currentSearchIndex.value];
      
      // 创建一个范围来包含搜索结果
      const range = document.createRange();
      range.setStart(result.node, result.index);
      range.setEnd(result.node, result.index + result.length);
      
      // 滚动到该元素
      const container = result.type === 'source' ? sourceContent.value : translatedContent.value;
      if (container) {
        // 计算元素在容器中的相对位置
        const rect = range.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();
        const relativeTop = rect.top - containerRect.top;
        
        // 滚动到元素位置，并留出一些空间
        container.scrollTop = container.scrollTop + relativeTop - 100;
      }
      
      // 高亮显示
      const selection = window.getSelection();
      selection.removeAllRanges();
      selection.addRange(range);
    };
    
    // 清除搜索结果
    const clearSearchResults = () => {
      searchResults.value = [];
      currentSearchIndex.value = 0;
      
      // 清除选择
      window.getSelection().removeAllRanges();
    };
    
    // 跳转到上一个搜索结果
    const prevSearchResult = () => {
      if (searchResults.value.length === 0) {
        return;
      }
      
      currentSearchIndex.value = (currentSearchIndex.value - 1 + searchResults.value.length) % searchResults.value.length;
      highlightCurrentSearchResult();
    };
    
    // 跳转到下一个搜索结果
    const nextSearchResult = () => {
      if (searchResults.value.length === 0) {
        return;
      }
      
      currentSearchIndex.value = (currentSearchIndex.value + 1) % searchResults.value.length;
      highlightCurrentSearchResult();
    };
    
    // 编辑相关功能
    // 开始编辑
    const startEdit = (index, type, rowIndex = null, colIndex = null) => {
      editingItem.value = { index, type };
      
      if (rowIndex !== null && colIndex !== null) {
        editingItem.value.rowIndex = rowIndex;
        editingItem.value.colIndex = colIndex;
      }
      
      // 根据类型获取初始编辑内容
      if (type === 'paragraph') {
        editingText.value = previewData.value[index].content || '';
        editingSource.value = previewData.value[index].source || '';
        editingContent.value = editingText.value;
      } else if (type === 'content') {
        editingText.value = previewData.value[index].content || '';
        editingSource.value = previewData.value[index].source || '';
        editingContent.value = editingText.value;
      } else if (type === 'table') {
        // 表格编辑逻辑
        const tableData = previewData.value[index];
        editingTableHeaders.value = tableData.headers || [];
        editingTableRows.value = tableData.rows || [];
        selectedCell.row = rowIndex || 0;
        selectedCell.col = colIndex || 0;
      }
      
      // 打开编辑模态框
      editModalVisible.value = true;
    };
    
    // 保存编辑
    const saveEdit = () => {
      if (!editingItem.value) return;
      
      const { index, type } = editingItem.value;
      
      // 根据类型保存编辑内容
      if (type === 'paragraph' || type === 'content') {
        previewData.value[index].content = editingContent.value;
        // 更新预览
        refreshPreviewHtml();
      } else if (type === 'table') {
        // 保存表格编辑
        previewData.value[index].headers = [...editingTableHeaders.value];
        previewData.value[index].rows = JSON.parse(JSON.stringify(editingTableRows.value));
        // 更新预览
        refreshPreviewHtml();
      }
      
      // 关闭编辑模态框
      editModalVisible.value = false;
      
      // 重置编辑状态
      editingItem.value = null;
      editingText.value = '';
      editingSource.value = '';
      editingContent.value = '';
      formatWarnings.value = [];
      specialChars.value = [];
      selectedCell.row = null;
      selectedCell.col = null;
    };
    
    // 取消编辑
    const cancelEdit = () => {
      // 关闭编辑模态框
      editModalVisible.value = false;
      
      // 重置编辑状态
      editingItem.value = null;
      editingText.value = '';
      editingSource.value = '';
      editingContent.value = '';
      formatWarnings.value = [];
      specialChars.value = [];
      selectedCell.row = null;
      selectedCell.col = null;
    };
    
    // 更新编辑内容
    const updateEditingContent = (event) => {
      editingContent.value = event.target.innerHTML;
      
      // 检测格式问题
      formatWarnings.value = detectFormatIssues(editingContent.value);
      
      // 检测特殊字符
      specialChars.value = detectSpecialChars(editingContent.value);
    };
    
    // 检测格式问题
    const detectFormatIssues = (content) => {
      const issues = [];
      
      // 检测HTML标签不匹配
      const openTags = (content.match(/<[^\/][^>]*>/g) || []).length;
      const closeTags = (content.match(/<\/[^>]*>/g) || []).length;
      
      if (openTags !== closeTags) {
        issues.push({
          type: 'html_tag_mismatch',
          message: 'HTML标签不匹配，可能导致格式错误'
        });
      }
      
      return issues;
    };
    
    // 检测特殊字符
    const detectSpecialChars = (content) => {
      const chars = [];
      const specialCharPatterns = [
        { pattern: /[^\x00-\x7F]+/g, description: '非ASCII字符' },
        { pattern: /[\u2018\u2019\u201C\u201D]/g, description: '智能引号' },
        { pattern: /[\u2013\u2014]/g, description: '破折号' }
      ];
      
      specialCharPatterns.forEach(({ pattern, description }) => {
        let match;
        while ((match = pattern.exec(content)) !== null) {
          chars.push({
            char: match[0],
            description: description
          });
        }
      });
      
      return chars;
    };
    
    // 表格编辑功能
    // 选择表格单元格
    const selectTableCell = (rowIndex, colIndex) => {
      selectedCell.row = rowIndex;
      selectedCell.col = colIndex;
    };
    
    // 更新表格标题
    const updateTableHeader = (index, event) => {
      editingTableHeaders.value[index] = event.target.innerText;
    };
    
    // 更新表格单元格
    const updateTableCell = (rowIndex, colIndex, event) => {
      editingTableRows.value[rowIndex][colIndex] = event.target.innerText;
    };
    
    // 添加表格行
    const addTableRow = () => {
      const newRow = Array(editingTableHeaders.value.length).fill('');
      editingTableRows.value.push(newRow);
    };
    
    // 添加表格列
    const addTableColumn = () => {
      editingTableHeaders.value.push('');
      editingTableRows.value.forEach(row => {
        row.push('');
      });
    };
    
    // 删除表格行
    const deleteTableRow = () => {
      if (selectedCell.row !== null && selectedCell.row >= 0) {
        editingTableRows.value.splice(selectedCell.row, 1);
        if (editingTableRows.value.length === 0) {
          editingTableRows.value.push(Array(editingTableHeaders.value.length).fill(''));
        }
        selectedCell.row = null;
      }
    };
    
    // 删除表格列
    const deleteTableColumn = () => {
      if (selectedCell.col !== null && selectedCell.col >= 0) {
        editingTableHeaders.value.splice(selectedCell.col, 1);
        editingTableRows.value.forEach(row => {
          row.splice(selectedCell.col, 1);
        });
        if (editingTableHeaders.value.length === 0) {
          editingTableHeaders.value.push('');
          editingTableRows.value.forEach(row => {
            row.push('');
          });
        }
        selectedCell.col = null;
      }
    };
    
    // 格式化文本
    const formatText = (type) => {
      document.execCommand(type, false);
      
      // 更新编辑内容
      if (document.activeElement && document.activeElement.classList.contains('editable-area')) {
        editingContent.value = document.activeElement.innerHTML;
      }
    };
    
    // 刷新预览HTML
    const refreshPreviewHtml = () => {
      // 简单实现，实际应用中可能需要更复杂的逻辑
      if (!previewData.value || previewData.value.length === 0) {
        documentPreview.source = '<div class="empty-preview">暂无原文数据</div>';
        documentPreview.translated = '<div class="empty-preview">暂无翻译数据</div>';
        return;
      }
      
      let sourceHtml = '';
      let translatedHtml = '';
      
      previewData.value.forEach((item, index) => {
        if (item.type === 'paragraph' || !item.type) {
          sourceHtml += `<div class="preview-paragraph">${item.source || ''}</div>`;
          translatedHtml += `<div class="preview-paragraph">${item.content || ''}</div>`;
        } else if (item.type === 'table') {
          // 表格渲染逻辑
          sourceHtml += '<div class="preview-table-container"><table class="preview-table">';
          translatedHtml += '<div class="preview-table-container"><table class="preview-table">';
          
          // 添加表头
          sourceHtml += '<thead><tr>';
          translatedHtml += '<thead><tr>';
          
          item.headers.forEach(header => {
            sourceHtml += `<th>${header}</th>`;
            translatedHtml += `<th>${header}</th>`;
          });
          
          sourceHtml += '</tr></thead><tbody>';
          translatedHtml += '</tr></thead><tbody>';
          
          // 添加表格内容
          item.rows.forEach(row => {
            sourceHtml += '<tr>';
            translatedHtml += '<tr>';
            
            row.forEach(cell => {
              sourceHtml += `<td>${cell}</td>`;
              translatedHtml += `<td>${cell}</td>`;
            });
            
            sourceHtml += '</tr>';
            translatedHtml += '</tr>';
          });
          
          sourceHtml += '</tbody></table></div>';
          translatedHtml += '</tbody></table></div>';
        }
      });
      
      documentPreview.source = sourceHtml;
      documentPreview.translated = translatedHtml;
    };
    
    // 智能匹配原文和译文
    const matchSourceAndTranslation = (previewData) => {
      // 确保数据是数组
      if (!Array.isArray(previewData)) {
        console.warn('预览数据不是数组格式，无法进行智能匹配');
        return previewData;
      }
      
      console.log('开始智能匹配原文和译文，原始数据:', previewData);
      
      // 检查是否有错位的翻译内容
      let hasMisalignedTranslations = false;
      let hasFirstItemWithAllTranslation = false;
      
      // 提取所有翻译内容，用于后续匹配
      const allTranslations = previewData
        .filter(item => item.translated && item.translated.trim() !== '')
        .map(item => item.translated.trim());
      
      // 检查第一段是否包含所有翻译内容，其他段落翻译为空
      const firstItem = previewData[0];
      const hasFirstTranslation = firstItem && firstItem.translated && firstItem.translated.trim() !== '';
      const emptyTranslations = previewData.slice(1).filter(item => 
        !item.translated || item.translated.trim() === '').length;
      
      // 检查如果第一段包含大部分其他段落中的译文开头
      if (hasFirstTranslation && firstItem.translated.length > 500) {
        const firstTranslation = firstItem.translated;
        let matchCount = 0;
        
        // 抽样检查其他有翻译的段落，是否包含在第一段中
        for (let i = 1; i < Math.min(previewData.length, 10); i++) {
          const item = previewData[i];
          if (item.translated && item.translated.trim() !== '') {
            const translationStart = item.translated.trim().substring(0, 30);
            if (firstTranslation.includes(translationStart)) {
              matchCount++;
            }
          }
        }
        
        if (matchCount >= 2) {
          hasFirstItemWithAllTranslation = true;
          console.log('检测到第一个段落可能包含所有翻译内容');
        }
      }
      
      // 检查是否有错位的标题翻译
      // 定义标题模式
      const chineseTitlePatterns = [
        { pattern: /^一、/, translation: /^I\.|^One\./ },
        { pattern: /^二、/, translation: /^II\.|^Two\./ },
        { pattern: /^三、/, translation: /^III\.|^Three\./ },
        { pattern: /^四、/, translation: /^IV\.|^Four\./ },
        { pattern: /^五、/, translation: /^V\.|^Five\./ }
      ];
      
      // 检查标题错位
      for (let i = 0; i < previewData.length - 1; i++) {
        const currentItem = previewData[i];
        const nextItem = previewData[i + 1];
        
        // 如果当前项包含中文标题模式
        for (const { pattern, translation } of chineseTitlePatterns) {
          if (currentItem.source && pattern.test(currentItem.source)) {
            // 检查当前项的译文是否不包含对应的英文标题
            if (currentItem.translated && !translation.test(currentItem.translated)) {
              // 检查下一项的译文是否包含对应的英文标题
              if (nextItem.translated && translation.test(nextItem.translated)) {
                hasMisalignedTranslations = true;
                console.log(`检测到标题错位: "${currentItem.source}" 与其译文不匹配`);
                break;
              }
            }
          }
        }
        
        if (hasMisalignedTranslations) break;
      }
      
      // 检查是否包含表格格式
      const tableRowPattern = /[・\.].*¥|\s{2,}¥|\s{2,}\$|\s{2,}€|\s{2,}\d+/;
      const containsTableRows = previewData.some(item => 
        item.source && tableRowPattern.test(item.source));
      
      // 如果不需要特殊处理，直接返回原始数据，但仍进行句子级别对齐处理
      if (!hasMisalignedTranslations && !hasFirstItemWithAllTranslation && !containsTableRows) {
        console.log('数据结构不需要智能匹配，返回原始数据并添加句子对齐');
        
        // 对每个段落添加句子级别对齐
        const processedData = [...previewData];
        
        // 对每个段落进行句子级别对齐
        for (let i = 0; i < processedData.length; i++) {
          const item = processedData[i];
          
          // 跳过空项或表格项
          if (!item.source || !item.translated || item.type === 'table' || item.headers) continue;
          
          // 拆分源文和译文为句子
          const sourceSentences = splitTextIntoSentences(item.source);
          const translatedSentences = splitTextIntoSentences(item.translated);
          
          // 如果句子数量合理，进行句子对齐
          if (sourceSentences.length > 0 && translatedSentences.length > 0) {
            // 添加句子对齐数据
            item.sentenceAlignments = alignSentences(sourceSentences, translatedSentences);
          }
        }
        
        return processedData;
      }
      
      console.log('检测到需要进行智能匹配处理');
      
      // 构建新的处理后的数据
      const processedData = [];
      
      // 获取所有可能的翻译内容
      let allTranslationText = "";
      
      // 如果第一项包含所有翻译
      if (hasFirstItemWithAllTranslation) {
        allTranslationText = firstItem.translated;
      } else {
        // 合并所有非空翻译
        allTranslationText = allTranslations.join("\n\n");
      }
      
      // 尝试按不同的分隔符拆分译文
      let translationParts = [];
      
      // 首先尝试按章节标题拆分
      const romanPattern = /\b(I|II|III|IV|V)\.\s/;
      const englishPattern = /\b(One|Two|Three|Four|Five)\.\s/;
      const chapterPattern = new RegExp(`(${romanPattern.source}|${englishPattern.source})`, 'g');
      
      if (chapterPattern.test(allTranslationText)) {
        // 使用正则表达式先分割
        const parts = allTranslationText.split(/\b(I|II|III|IV|V)\.\s|\b(One|Two|Three|Four|Five)\.\s/g);
        
        // 处理分割结果，合并标题与内容
        let combinedParts = [];
        let currentPart = "";
        
        for (let i = 0; i < parts.length; i++) {
          const part = parts[i];
          if (!part) continue;
          
          if (romanPattern.test(part) || englishPattern.test(part)) {
            // 如果是标题部分，开始新的部分
            if (currentPart) combinedParts.push(currentPart.trim());
            currentPart = part;
          } else {
            // 如果是内容部分，添加到当前部分
            currentPart += part;
          }
        }
        
        // 添加最后一个部分
        if (currentPart) combinedParts.push(currentPart.trim());
        
        translationParts = combinedParts.filter(p => p.trim());
        console.log('按章节标题拆分为', translationParts.length, '个部分');
      }
      
      // 如果按章节标题拆分效果不好，尝试按双换行拆分
      if (translationParts.length < 3 && allTranslationText.includes('\n\n')) {
        translationParts = allTranslationText.split(/\n\n+/).filter(p => p.trim());
        console.log('按双换行拆分为', translationParts.length, '个部分');
      }
      
      // 如果按双换行拆分效果还不好，尝试按单换行拆分
      if (translationParts.length < previewData.length * 0.3) {
        translationParts = allTranslationText.split(/\n/).filter(p => p.trim());
        console.log('按单换行拆分为', translationParts.length, '个部分');
      }
      
      // 更精确地定义标题模式和匹配表达式
      const titleMappings = [
        { 
          source: /虚拟课程开发成本与运营白皮书/, 
          pattern: /Virtual\s+Course.*White\s+Paper|Course\s+Development.*White\s+Paper/i 
        },
        { 
          source: /一、成本结构深度解析/, 
          pattern: /I\.\s+Cost\s+Structure|One\.\s+Cost|In-depth\s+Analysis\s+of\s+Cost/i 
        },
        { 
          source: /二、商业报价与利润模型/, 
          pattern: /II\.\s+Business|Two\.\s+Business|Profit\s+Model|Quotation/i 
        },
        { 
          source: /三、核心运营策略/, 
          pattern: /III\.\s+Core|Three\.\s+Core|Operation.*Strategy/i 
        },
        { 
          source: /四、行业对标数据/, 
          pattern: /IV\.\s+Industry|Four\.\s+Industry|Benchmark\s+Data/i 
        },
        { 
          source: /五、开发决策流程图/, 
          pattern: /V\.\s+Development|Five\.\s+Development|Decision\s+Flowchart/i 
        }
      ];
      
      // 表格行的更准确匹配
      const isTableRow = (text) => {
        if (!text) return false;
        return /[・\.].*¥|\s{2,}¥|\s{2,}\$|\s{2,}€|\s{2,}\d+/.test(text) || 
               /\[.*\]\s+\[.*\]\s+\[.*\]/.test(text) || 
               /LOD\d/.test(text) ||
               /■|●|❗/.test(text);
      };
      
      // 为每个标题找到匹配的翻译部分
      const titleTranslationMap = new Map();
      
      titleMappings.forEach(({ source, pattern }) => {
        // 找到源文中包含该标题的段落
        const sourceParagraphIndex = previewData.findIndex(item => 
          item.source && source.test(item.source));
        
        if (sourceParagraphIndex !== -1) {
          // 在翻译部分中寻找匹配的标题
          const matchingTranslationIndex = translationParts.findIndex(part => 
            pattern.test(part));
          
          if (matchingTranslationIndex !== -1) {
            titleTranslationMap.set(sourceParagraphIndex, matchingTranslationIndex);
            console.log(`标题匹配: 原文索引 ${sourceParagraphIndex} -> 译文索引 ${matchingTranslationIndex}`);
          }
        }
      });
      
      // 找出所有表格行
      const tableRows = previewData
        .map((item, index) => ({ 
          index, 
          source: item.source, 
          isTable: isTableRow(item.source)
        }))
        .filter(item => item.isTable);
      
      console.log(`检测到 ${tableRows.length} 行表格数据`);
      
      // 找出翻译中的表格行
      const translationTableParts = translationParts
        .map((text, index) => ({ 
          index, 
          text, 
          isTable: isTableRow(text)
        }))
        .filter(item => item.isTable);
      
      console.log(`检测到 ${translationTableParts.length} 行翻译表格数据`);
      
      // 准备表格行匹配
      const tableMatches = new Map();
      
      // 基于数字和特殊模式匹配表格行
      tableRows.forEach(({ index, source }) => {
        // 提取数字
        const sourceNumbers = source.match(/\d+/g) || [];
        const sourceKeywords = ['3D', 'UE5', 'TTS', 'API', 'AI', 'LOD', '¥', '$']
          .filter(keyword => source.includes(keyword));
        
        // 符号模式
        const hasSpecialSymbol = /■|●|❗/.test(source);
        const symbolPattern = source.match(/■|●|❗/)?.[0];
        
        let bestMatchIndex = -1;
        let bestMatchScore = 0;
        
        translationTableParts.forEach(({ index: transIndex, text }) => {
          // 如果这个翻译已经被匹配，跳过
          if ([...tableMatches.values()].includes(transIndex)) {
            return;
          }
          
          let score = 0;
          
          // 检查数字匹配
          const transNumbers = text.match(/\d+/g) || [];
          const commonNumbers = sourceNumbers.filter(num => transNumbers.includes(num));
          score += commonNumbers.length * 10;
          
          // 检查关键词匹配
          sourceKeywords.forEach(keyword => {
            if (text.includes(keyword)) {
              score += 5;
            }
          });
          
          // 检查特殊符号匹配
          if (hasSpecialSymbol && text.includes(symbolPattern)) {
            score += 20;
          }
          
          // 如果得分足够高，认为是匹配
          if (score > bestMatchScore) {
            bestMatchScore = score;
            bestMatchIndex = transIndex;
          }
        });
        
        // 如果找到了好的匹配
        if (bestMatchIndex !== -1 && bestMatchScore >= 10) {
          tableMatches.set(index, bestMatchIndex);
          console.log(`表格行匹配: 原文索引 ${index} -> 译文索引 ${bestMatchIndex} (得分: ${bestMatchScore})`);
        }
      });
      
      // 构建最终数据
      const newPreviewData = [];
      
      // 添加第一个段落（文档标题）
      const titleItem = { ...previewData[0] };
      // 查找标题的翻译（应该是第一部分）
      if (translationParts.length > 0) {
        titleItem.translated = translationParts[0];
      }
      newPreviewData.push(titleItem);
      
      // 用于跟踪已分配的翻译部分
      const usedTranslationIndices = new Set([0]); // 标题已使用
      
      // 处理其余段落
      for (let i = 1; i < previewData.length; i++) {
        const item = { ...previewData[i] };
        const source = item.source?.trim() || "";
        
        // 如果源文为空，保持不变
        if (!source) {
          newPreviewData.push(item);
          continue;
        }
        
        // 1. 检查是否是标题段落
        if (titleTranslationMap.has(i)) {
          const translationIndex = titleTranslationMap.get(i);
          item.translated = translationParts[translationIndex];
          usedTranslationIndices.add(translationIndex);
          console.log(`分配标题翻译: 索引 ${i} -> 译文部分 ${translationIndex}`);
        }
        // 2. 检查是否是表格行
        else if (tableMatches.has(i)) {
          const translationIndex = tableMatches.get(i);
          item.translated = translationParts[translationIndex];
          usedTranslationIndices.add(translationIndex);
          console.log(`分配表格行翻译: 索引 ${i} -> 译文部分 ${translationIndex}`);
        }
        // 3. 尝试按顺序分配未使用的翻译部分
        else {
          let assigned = false;
          
          // 寻找下一个未使用的翻译部分
          for (let j = 1; j < translationParts.length; j++) {
            if (!usedTranslationIndices.has(j)) {
              item.translated = translationParts[j];
              usedTranslationIndices.add(j);
              console.log(`按顺序分配翻译: 索引 ${i} -> 译文部分 ${j}`);
              assigned = true;
              break;
            }
          }
          
          // 如果没有可用的翻译部分，尝试保持原译文
          if (!assigned && item.translated && item.translated.trim()) {
            console.log(`保留原译文: 索引 ${i}`);
          } else if (!assigned) {
            console.log(`未找到匹配译文: 索引 ${i}`);
          }
        }
        
        newPreviewData.push(item);
      }
      
      // 确保所有段落都有译文内容
      // 检查是否有译文部分未被使用
      for (let i = 1; i < translationParts.length; i++) {
        if (!usedTranslationIndices.has(i)) {
          // 寻找没有译文的段落
          const emptyTranslationIndex = newPreviewData.findIndex(item => 
            (!item.translated || !item.translated.trim()) && item.source && item.source.trim());
          
          if (emptyTranslationIndex !== -1) {
            newPreviewData[emptyTranslationIndex].translated = translationParts[i];
            console.log(`为空白译文段落分配: 索引 ${emptyTranslationIndex} -> 译文部分 ${i}`);
          }
        }
      }
      
      // 为每个段落添加句子级别对齐
      for (let i = 0; i < newPreviewData.length; i++) {
        const item = newPreviewData[i];
        
        // 跳过空项或表格项
        if (!item.source || !item.translated || item.type === 'table' || item.headers) continue;
        
        // 拆分源文和译文为句子
        const sourceSentences = splitTextIntoSentences(item.source);
        const translatedSentences = splitTextIntoSentences(item.translated);
        
        // 如果句子数量合理，进行句子对齐
        if (sourceSentences.length > 0 && translatedSentences.length > 0) {
          // 添加句子对齐数据
          item.sentenceAlignments = alignSentences(sourceSentences, translatedSentences);
        }
      }
      
      console.log('智能匹配完成，处理后的数据:', newPreviewData);
      return newPreviewData;
    };
    
    // 在组件挂载时
    onMounted(() => {
      // 语言列表是静态配置，直接可用
      console.log('文档翻译页面：语言列表已加载，共', languages.value.length, '种语言');

      // 暴露编辑方法到window对象，以便HTML中的onclick调用
      window.startEditItem = (index, type) => {
        startEdit(index, type);
      };
      
      window.startEditCell = (index, rowIndex, colIndex) => {
        startEdit(index, 'table', rowIndex, colIndex);
      };
      
      // 监听预览模态框的显示状态
      watch(previewVisible, (visible) => {
        if (visible) {
          // 预览模态框显示时，初始化预览功能
          nextTick(() => {
            // 应用缩放
            applyZoom();
            
            // 监听键盘事件，用于导航和搜索
            document.addEventListener('keydown', handleKeyDown);
          });
        } else {
          // 预览模态框关闭时，清理资源
          document.removeEventListener('keydown', handleKeyDown);
          
          // 清除搜索结果
          clearSearchResults();
          
          // 重置全屏状态
          isFullscreen.value = false;
        }
      });
    });
    
    // 处理键盘事件
    const handleKeyDown = (event) => {
      // 只有当预览模态框显示时才处理键盘事件
      if (!previewVisible.value) return;
      
      switch (event.key) {
        case 'ArrowLeft':
          // 左箭头键，切换到上一页
          if (!event.ctrlKey && !event.metaKey) {
            prevPage();
            event.preventDefault();
          }
          break;
          
        case 'ArrowRight':
          // 右箭头键，切换到下一页
          if (!event.ctrlKey && !event.metaKey) {
            nextPage();
            event.preventDefault();
          }
          break;
          
        case 'f':
          // Ctrl+F 或 Command+F，打开搜索面板
          if (event.ctrlKey || event.metaKey) {
            toggleSearch();
            event.preventDefault();
          }
          break;
          
        case 'F11':
          // F11，切换全屏
          toggleFullscreen();
          event.preventDefault();
          break;
          
        case '+':
        case '=':
          // Ctrl++ 或 Command++，放大
          if (event.ctrlKey || event.metaKey) {
            zoomIn();
            event.preventDefault();
          }
          break;
          
        case '-':
          // Ctrl+- 或 Command+-，缩小
          if (event.ctrlKey || event.metaKey) {
            zoomOut();
            event.preventDefault();
          }
          break;
          
        case 'Escape':
          // Escape，如果在全屏模式下，退出全屏；否则关闭搜索面板
          if (isFullscreen.value) {
            isFullscreen.value = false;
            event.preventDefault();
          } else if (showSearch.value) {
            showSearch.value = false;
            event.preventDefault();
          }
          break;
      }
    };
    
    // 组件卸载时清除所有定时器
    onBeforeUnmount(() => {
      // 清除上传超时定时器
      if (uploadTimeoutId.value) {
        clearTimeout(uploadTimeoutId.value);
        uploadTimeoutId.value = null;
      }
      
      // 清除状态检查定时器
      if (statusPollingTimer.value) {
        clearInterval(statusPollingTimer.value);
        statusPollingTimer.value = null;
      }
      
      // 清除状态超时检测定时器
      if (statusTimeoutTimer.value) {
        clearInterval(statusTimeoutTimer.value);
        statusTimeoutTimer.value = null;
      }
    });
    
    // 更新翻译摘要信息
    const updateTranslationSummary = (apiResponse) => {
      console.log('开始更新翻译摘要信息，数据:', JSON.stringify(apiResponse, null, 2));
      
      // 如果数据在data字段中嵌套，提取出来
      const data = apiResponse.data || apiResponse;
      console.log('处理的数据对象结构:', Object.keys(data));
      
      // 深度获取对象属性值的辅助函数
      const getNestedValue = (obj, path, defaultValue = null) => {
        if (!obj) return defaultValue;
        
        const parts = path.split('.');
        let current = obj;
        
        for (const part of parts) {
          if (current === null || current === undefined || typeof current !== 'object') {
            return defaultValue;
          }
          current = current[part];
        }
        
        return current !== undefined ? current : defaultValue;
      };
      
      // 从日志中提取信息的函数
      const extractInfoFromLogs = (logs) => {
        if (!logs || !Array.isArray(logs) || logs.length === 0) {
          return null;
        }
        
        const info = {
          sourceLanguage: null,
          targetLanguage: null,
          fileName: null,
          wordCount: null,
          startTime: null,
          endTime: null
        };
        
        // 排序日志按时间
        const sortedLogs = [...logs].sort((a, b) => {
          const timeA = a.timestamp || a.time || 0;
          const timeB = b.timestamp || b.time || 0;
          return new Date(timeA) - new Date(timeB);
        });
        
        // 记录首尾时间用于计算总时间
        if (sortedLogs.length > 0) {
          info.startTime = new Date(sortedLogs[0].timestamp || sortedLogs[0].time);
          info.endTime = new Date(sortedLogs[sortedLogs.length - 1].timestamp || sortedLogs[sortedLogs.length - 1].time);
        }
        
        // 遍历日志寻找有用信息
        for (const log of logs) {
          const message = log.message || '';
          
          // 匹配任务启动日志，提取源语言、目标语言和文件名
          const startMatch = message.match(/文档翻译任务已启动:\s*([^,]+),\s*源语言:\s*(\w+),\s*目标语言:\s*(\w+)/);
          if (startMatch) {
            info.fileName = startMatch[1].trim();
            info.sourceLanguage = startMatch[2].trim();
            info.targetLanguage = startMatch[3].trim();
            console.log('从日志中提取任务信息:', {
              fileName: info.fileName,
              sourceLanguage: info.sourceLanguage,
              targetLanguage: info.targetLanguage
            });
            continue;
          }
          
          // 匹配字数信息
          const wordCountMatch = message.match(/共(\d+)个?字/i) || 
                               message.match(/字数[：:]\s*(\d+)/i) ||
                               message.match(/(\d+)\s*words/i) ||
                               message.match(/word count[：:]\s*(\d+)/i);
          
          if (wordCountMatch) {
            info.wordCount = parseInt(wordCountMatch[1], 10);
            console.log('从日志中提取字数:', info.wordCount);
            continue;
          }
          
          // 匹配文档相关信息
          if (message.includes('文档解析完成') && message.includes('共')) {
            const docInfoMatch = message.match(/共(\d+)页/) || message.match(/共(\d+)个?字/);
            if (docInfoMatch) {
              info.wordCount = parseInt(docInfoMatch[1], 10);
              console.log('从文档解析日志中提取字数:', info.wordCount);
            }
          }
        }
        
        return info;
      };
      
      // 记录我们要查找的各种字段路径
      console.log('将尝试查找以下字段:');
      console.log('- 源语言: data.source_language, data.metadata.source_language, data.detected_language, data.input.source_language');
      console.log('- 目标语言: data.target_language, data.metadata.target_language, data.input.target_language');
      console.log('- 专业领域: data.domain, data.metadata.domain, data.input.domain, formState.domain');
      console.log('- 字数统计: data.word_count, data.metadata.word_count, data.statistics.word_count, data.counts.words');
      console.log('- 翻译时间: data.duration, data.metadata.duration, data.translation_time, translationStartTime/EndTime');
      
      // 首先从日志中提取信息
      const logInfo = extractInfoFromLogs(data.logs);
      console.log('从日志中提取的信息:', logInfo);
      
      // 提取检测到的源语言
      // 修改源语言查找顺序，优先检查顶层字段
      const possibleSourceLanguagePaths = [
        'source_language',  // 直接在顶级
        'data.source_language',  // 优先检查顶级字段
        'data.output.source_language',
        'data.output_data.source_language',
        'data.output.source_lang',
        'data.output_data.source_lang',
        'data.source_lang',
        'data.input.source_language',
        'data.input_data.source_language',
        'data.input.source_lang',
        'data.input_data.source_lang'
      ];
      
      console.log('源语言查找路径:', possibleSourceLanguagePaths);
      
      let foundSourceLang = null;
      for (const path of possibleSourceLanguagePaths) {
        const value = getNestedValue(data, path);
        if (value) {
          console.log(`从路径 ${path} 获取到源语言: ${value}`);
          foundSourceLang = normalizeLanguageCode(value);  // 标准化语言代码
          break;
        }
      }
      
      if (!foundSourceLang && logInfo && logInfo.sourceLanguage) {
        console.log(`从日志中获取源语言: ${logInfo.sourceLanguage}`);
        foundSourceLang = normalizeLanguageCode(logInfo.sourceLanguage);  // 标准化语言代码
      }
      
      if (!foundSourceLang && formState.sourceLanguage && formState.sourceLanguage !== 'auto') {
        console.log(`从表单状态获取源语言: ${formState.sourceLanguage}`);
        foundSourceLang = formState.sourceLanguage;
      } else if (!foundSourceLang && formState.sourceLanguage === 'auto') {
        console.log('源语言为自动检测，等待检测结果');
      }
      
      if (foundSourceLang) {
        console.log(`最终确定的源语言: ${foundSourceLang}`);
        detectedSourceLang.value = getLanguageName(foundSourceLang);
      }
      
      // 提取目标语言 - 优先检查顶层字段
      const possibleTargetLanguagePaths = [
        'data.target_language',  // 优先检查顶级字段
        'data.output.target_language',
        'data.output_data.target_language',
        'data.output.target_lang',
        'data.output_data.target_lang',
        'data.target_lang',
        'data.input.target_language',
        'data.input_data.target_language',
        'data.input.target_lang',
        'data.input_data.target_lang'
      ];
      
      console.log('目标语言查找路径:', possibleTargetLanguagePaths);
      
      let foundTargetLang = null;
      for (const path of possibleTargetLanguagePaths) {
        const value = getNestedValue(data, path);
        if (value) {
          console.log(`从路径 ${path} 获取到目标语言: ${value}`);
          foundTargetLang = normalizeLanguageCode(value);  // 标准化语言代码
          break;
        }
      }
      
      if (!foundTargetLang && logInfo && logInfo.targetLanguage) {
        console.log(`从日志中获取目标语言: ${logInfo.targetLanguage}`);
        foundTargetLang = normalizeLanguageCode(logInfo.targetLanguage);  // 标准化语言代码
      }
      
      if (!foundTargetLang && formState.targetLanguage) {
        console.log(`从表单状态获取目标语言: ${formState.targetLanguage}`);
        foundTargetLang = formState.targetLanguage;
      }
      
      console.log(`设置目标语言: ${getLanguageName(foundTargetLang || formState.targetLanguage)}`);
      
      // 提取专业领域 - 优先检查顶层字段
      const possibleDomainPaths = [
        'domain',
        'output.domain',
        'output_data.domain',
        'metadata.domain',
        'input.domain',
        'request.domain',
        'params.domain'
      ];
      
      // 尝试查找专业领域，按优先级顺序
      console.log('专业领域查找路径:', possibleDomainPaths);
      let foundDomain = null;
      for (const path of possibleDomainPaths) {
        const value = getNestedValue(data, path);
        if (value) {
          console.log(`从路径 '${path}' 找到专业领域: ${value}`);
          foundDomain = value;
          break;
        }
      }
      
      // 如果API响应中没有找到，回退到表单状态
      if (!foundDomain && formState.domain) {
        console.log(`从表单状态获取专业领域: ${formState.domain}`);
        foundDomain = formState.domain;
      }
      
      // 统计字数 - 优先检查顶层字段
      const possibleWordCountPaths = [
        'word_count',
        'output.word_count',
        'output_data.word_count',
        'metadata.word_count',
        'statistics.word_count',
        'counts.words',
        'counts.total_words',
        'metrics.word_count',
        'metrics.words'
      ];
      
      // 添加更详细的日志
      console.log('字数统计查找路径:', possibleWordCountPaths);
      console.log('数据结构:', Object.keys(data));
      if (data.output) console.log('output字段内容:', data.output);
      if (data.output_data) console.log('output_data字段内容:', data.output_data);
      
      // 尝试查找字数，按优先级顺序
      let foundWordCount = null;
      for (const path of possibleWordCountPaths) {
        const value = getNestedValue(data, path);
        if (value !== null && value !== undefined) {
          console.log(`从路径 '${path}' 找到字数: ${value}`);
          foundWordCount = value;
          break;
        }
      }
      
      // 如果没有从API响应中找到，检查日志中提取的信息
      if ((foundWordCount === null || foundWordCount === undefined) && logInfo && logInfo.wordCount) {
        console.log(`从日志中获取字数: ${logInfo.wordCount}`);
        foundWordCount = logInfo.wordCount;
      }
      
      // 如果在API响应和日志中都没有找到，尝试从其他数据中提取
      if (foundWordCount === null || foundWordCount === undefined) {
        // 尝试从文本内容估算
        if (data.content && typeof data.content === 'string') {
          const wordCountEstimate = data.content.split(/\s+/).length;
          console.log(`从内容估算字数: ${wordCountEstimate}`);
          foundWordCount = wordCountEstimate;
        } else if (data.text && typeof data.text === 'string') {
          const wordCountEstimate = data.text.split(/\s+/).length;
          console.log(`从text字段估算字数: ${wordCountEstimate}`);
          foundWordCount = wordCountEstimate;
        } else if (data.translated_text && typeof data.translated_text === 'string') {
          const wordCountEstimate = data.translated_text.split(/\s+/).length;
          console.log(`从translated_text字段估算字数: ${wordCountEstimate}`);
          foundWordCount = wordCountEstimate;
        } else {
          console.warn('无法确定字数统计');
          foundWordCount = '未知';
        }
      }
      
      // 更新UI显示
      wordCount.value = foundWordCount;
      console.log(`设置字数统计: ${wordCount.value}`);
      
      // 计算翻译时间 - 优先检查顶层字段
      const possibleDurationPaths = [
        'duration',
        'output.duration',
        'output_data.duration',
        'metadata.duration',
        'translation_time',
        'metrics.duration',
        'metrics.translation_time',
        'timing.duration',
        'timing.total_time'
      ];
      
      // 尝试查找翻译时间，按优先级顺序
      console.log('翻译时间查找路径:', possibleDurationPaths);
      let foundDuration = null;
      for (const path of possibleDurationPaths) {
        const value = getNestedValue(data, path);
        if (value !== null && value !== undefined) {
          console.log(`从路径 '${path}' 找到翻译时间: ${value}`);
          foundDuration = value;
          break;
        }
      }
      
      // 如果在API响应中没有找到，检查从日志中提取的开始和结束时间
      if ((foundDuration === null || foundDuration === undefined) && logInfo && logInfo.startTime && logInfo.endTime) {
        const logDurationMs = logInfo.endTime - logInfo.startTime;
        const logSeconds = Math.floor(logDurationMs / 1000);
        console.log(`从日志时间范围计算翻译时间: ${logSeconds}秒 (${logDurationMs}毫秒)`);
        foundDuration = logSeconds;
      }
      
      // 如果在API响应和日志中都没有找到，尝试从本地时间戳计算
      if ((foundDuration === null || foundDuration === undefined) && translationStartTime.value && translationEndTime.value) {
        const durationMs = translationEndTime.value - translationStartTime.value;
        const seconds = Math.floor(durationMs / 1000);
        console.log(`从本地时间戳计算翻译时间: ${seconds}秒 (${durationMs}毫秒)`);
        foundDuration = seconds;
      }
      
      // 如果仍然没有找到，尝试通过其他方式从日志中提取
      if ((foundDuration === null || foundDuration === undefined) && data.logs && Array.isArray(data.logs) && data.logs.length > 0) {
        try {
          // 对日志按时间排序
          const sortedLogs = [...data.logs].sort((a, b) => {
            const timeA = a.timestamp || a.time || 0;
            const timeB = b.timestamp || b.time || 0;
            return new Date(timeA) - new Date(timeB);
          });
          
          // 查找开始和完成日志
          const startLog = sortedLogs.find(log => 
            (log.message && (log.message.includes('开始') || log.message.includes('start'))) ||
            (log.status && (log.status === 'started' || log.status === 'processing'))
          );
          
          const completionLog = sortedLogs.reverse().find(log => 
            (log.message && (log.message.includes('完成') || log.message.includes('complete'))) ||
            (log.status && (log.status === 'success' || log.status === 'completed'))
          );
          
          if (startLog && completionLog) {
            const startTime = new Date(startLog.timestamp || startLog.time);
            const completionTime = new Date(completionLog.timestamp || completionLog.time);
            const logDurationMs = completionTime - startTime;
            const logSeconds = Math.floor(logDurationMs / 1000);
            
            console.log(`从日志计算翻译时间: ${logSeconds}秒 (${logDurationMs}毫秒)`);
            foundDuration = logSeconds;
          }
        } catch (error) {
          console.error('从日志提取翻译时间出错:', error);
        }
      }
      
      // 格式化翻译时间
      if (foundDuration !== null && foundDuration !== undefined) {
        const seconds = typeof foundDuration === 'number' ? foundDuration : parseInt(foundDuration, 10);
        
        if (!isNaN(seconds)) {
          const minutes = Math.floor(seconds / 60);
          const remainingSeconds = seconds % 60;
          
          if (minutes > 0) {
            translationDuration.value = `${minutes}分${remainingSeconds}秒`;
          } else {
            translationDuration.value = `${seconds}秒`;
          }
          
          console.log(`设置翻译时间: ${translationDuration.value}`);
        } else {
          translationDuration.value = `${foundDuration}`;
          console.log(`设置未格式化的翻译时间: ${translationDuration.value}`);
        }
      } else {
        translationDuration.value = '未知';
        console.warn('无法确定翻译时间');
      }
      
      console.log('翻译摘要更新完成:', {
        detectedSourceLang: detectedSourceLang.value,
        targetLanguage: getLanguageName(foundTargetLang || formState.targetLanguage),
        domain: getDomainName(foundDomain || formState.domain),
        wordCount: wordCount.value,
        translationDuration: translationDuration.value
      });
    };
    
    // 将文本拆分为句子
    const splitTextIntoSentences = (text) => {
      if (!text) return [];
      
      // 使用正则表达式拆分句子（支持中英文标点）
      const sentenceRegex = /([.!?。！？\n]+\s*)/g;
      
      // 将文本按标点分割，但保留标点
      const segments = text.split(sentenceRegex);
      
      // 重新组合为完整句子
      const sentences = [];
      for (let i = 0; i < segments.length - 1; i += 2) {
        if (segments[i] && segments[i].trim()) {
          // 将文本段与其后的标点符号组合
          const punctuation = i + 1 < segments.length ? segments[i + 1] : '';
          sentences.push((segments[i] + punctuation).trim());
        }
      }
      
      // 处理最后一段（如果没有标点结尾）
      if (segments.length % 2 === 1 && segments[segments.length - 1].trim()) {
        sentences.push(segments[segments.length - 1].trim());
      }
      
      // 过滤空句子
      return sentences.filter(s => s.trim());
    };
    
    // 句子级别的对齐逻辑
    const alignSentences = (sourceSentences, translatedSentences) => {
      // 基于长度比例和内容特征进行句子对齐
      const alignments = [];
      const sourceCount = sourceSentences.length;
      const translatedCount = translatedSentences.length;
      
      // 处理简单情况：句子数量相同或相近
      if (Math.abs(sourceCount - translatedCount) <= 2) {
        // 直接一对一匹配
        const minCount = Math.min(sourceCount, translatedCount);
        for (let i = 0; i < minCount; i++) {
          alignments.push({
            source: sourceSentences[i],
            translated: translatedSentences[i],
            sourceIndex: i,
            translatedIndex: i,
            confidence: 0.9 // 高置信度
          });
        }
        
        // 处理剩余句子
        if (sourceCount > translatedCount) {
          for (let i = translatedCount; i < sourceCount; i++) {
            alignments.push({
              source: sourceSentences[i],
              translated: '',
              sourceIndex: i,
              translatedIndex: -1,
              confidence: 0.5
            });
          }
        } else if (translatedCount > sourceCount) {
          // 多余的译文句子附加到最后一个源句子
          if (alignments.length > 0) {
            const lastAlignment = alignments[alignments.length - 1];
            lastAlignment.translated += ' ' + translatedSentences.slice(sourceCount).join(' ');
          }
        }
        
        return alignments;
      }
      
      // 复杂情况：句子数量差异较大，使用动态规划算法
      // 这里简化为按比例分配
      
      const ratio = translatedCount / sourceCount;
      
      for (let i = 0; i < sourceCount; i++) {
        const startIdx = Math.floor(i * ratio);
        const endIdx = Math.floor((i + 1) * ratio);
        
        // 收集对应的译文句子
        let translatedText = '';
        for (let j = startIdx; j < endIdx && j < translatedCount; j++) {
          translatedText += translatedSentences[j] + ' ';
        }
        
        alignments.push({
          source: sourceSentences[i],
          translated: translatedText.trim(),
          sourceIndex: i,
          translatedIndex: startIdx,
          confidence: 0.7 // 中等置信度
        });
      }
      
      return alignments;
    };
    
    // 计算两个句子的相似度（基于共同词汇）
    const calculateSimilarity = (sentence1, sentence2) => {
      // 转换为小写并分词
      const words1 = sentence1.toLowerCase().split(/\s+/);
      const words2 = sentence2.toLowerCase().split(/\s+/);
      
      // 创建词汇集合
      const set1 = new Set(words1);
      const set2 = new Set(words2);
      
      // 计算交集大小
      const intersection = [...set1].filter(word => set2.has(word));
      
      // 计算并集大小
      const union = new Set([...set1, ...set2]);
      
      // 计算Jaccard相似度
      return intersection.length / union.size;
    };

    // 为包含特殊格式的句子预处理
    const preprocessSentence = (sentence) => {
      // 替换数字为占位符，保留格式但忽略具体数值
      return sentence.replace(/\d+/g, 'NUM')
                     .replace(/¥|\$|€/g, 'CURRENCY');
    };
    
    // 在setup函数中添加以下变量和方法

    // 用于控制句子级别编辑的状态
    const showSentenceView = ref(false);
    const editingSentenceIndex = ref(null);
    const editingSentenceParagraphIndex = ref(null);
    const editingSentenceText = ref('');
    const editedSentences = reactive(new Map()); // 存储已编辑的句子 key为 `${paraIndex}-${sentenceIndex}`

    // 开始编辑句子
    const startEditSentence = (paragraphIndex, sentenceIndex) => {
      editingSentenceParagraphIndex.value = paragraphIndex;
      editingSentenceIndex.value = sentenceIndex;
      
      // 获取当前句子的译文
      const paragraph = previewParaData.value[paragraphIndex];
      const sentence = paragraph.sentenceAlignments[sentenceIndex];
      
      editingSentenceText.value = sentence.translated;
    };

    // 取消编辑句子
    const cancelEditSentence = () => {
      editingSentenceParagraphIndex.value = null;
      editingSentenceIndex.value = null;
      editingSentenceText.value = '';
    };

    // 保存句子编辑
    const saveSentenceEdit = (paragraphIndex, sentenceIndex) => {
      if (editingSentenceText.value !== undefined) {
        // 更新句子的译文
        const paragraph = previewParaData.value[paragraphIndex];
        const originalText = paragraph.sentenceAlignments[sentenceIndex].translated;
        
        // 如果文本有变化，才更新
        if (originalText !== editingSentenceText.value) {
          paragraph.sentenceAlignments[sentenceIndex].translated = editingSentenceText.value;
          
          // 标记为已编辑
          const editKey = `${paragraphIndex}-${sentenceIndex}`;
          editedSentences.set(editKey, true);
          
          // 标记段落为已编辑
          paragraph.edited = true;
          
          // 重建整个段落的译文
          paragraph.translated = paragraph.sentenceAlignments
            .map(alignment => alignment.translated)
            .join(' ');
            
          // 添加到编辑历史
          const edit = {
            type: 'sentence',
            paraIndex: paragraphIndex,
            sentenceIndex: sentenceIndex,
            before: originalText,
            after: editingSentenceText.value
          };
          editHistory.push(edit);
        }
        
        // 退出编辑模式
        cancelEditSentence();
      }
    };

    // 检查是否正在编辑特定句子
    const isEditingSentence = (paragraphIndex, sentenceIndex) => {
      return editingSentenceParagraphIndex.value === paragraphIndex && 
             editingSentenceIndex.value === sentenceIndex;
    };
    
    // 检查句子是否已编辑
    const sentenceEdited = (paragraphIndex, sentenceIndex) => {
      const editKey = `${paragraphIndex}-${sentenceIndex}`;
      return editedSentences.has(editKey);
    };
    
    return {
      // 状态
      currentStep,
      fileList,
      rawFiles,
      uploading,
      translating,
      translationProgress,
      progressStatus,
      progressText,
      errorMsg,
      specialCharStatus,
      showSpecialCharDetails,
      specialCharDetailsVisible,
      specialCharDetails,
      formState,
      networkError,
      networkErrorMessage,
      previewVisible,
      documentPreview,
      baseUrl,
      debugMode,
      isDev,
      
      // 新增的返回变量
      detectedSourceLang,
      translationDuration,
      wordCount,
      translationStartTime,
      translationEndTime,
      
      // 段落级预览相关
      previewParaData,
      showLineNumbers,
      hasEdits,
      
      // 语言和领域映射函数
      getLanguageName,
      getDomainName,

      // 语言相关
      languages,

      // 选项
      languageOptions,
      domainOptions,
      styleOptions,
      glossaryOptions,
      acceptedExtensions,
      
      // 方法
      formatFileSize,
      getFileTypeDescription,
      handleFileChange,
      handleRemoveFile,
      nextStep,
      prevStep,
      startTranslation,
      beforeUpload,
      downloadResult,
      toggleSpecialCharDetails,
      previewTranslatedDocument,
      translateNewDocument,
      retryConnection,
      customRequest,
      
      // 段落编辑相关方法
      startEditPara,
      saveParaEdit,
      cancelParaEdit,
      saveAllEdits,
      
      // 编辑相关
      editingItem,
      editingText,
      editingSource,
      editingContent,
      editModalVisible,
      formatWarnings,
      specialChars,
      selectedCell,
      editingTableHeaders,
      editingTableRows,
      
      // 预览相关
      viewMode,
      zoomLevel,
      isFullscreen,
      currentPage,
      totalPages,
      showSearch,
      searchText,
      searchInSource,
      searchInTranslated,
      caseSensitive,
      searchResults,
      currentSearchIndex,
      sourceColumnWidth,
      sourceContent,
      translatedContent,
      isResizing,
      
      // 预览相关功能
      zoomOut,
      zoomIn,
      applyZoom,
      prevPage,
      nextPage,
      loadPageContent,
      toggleFullscreen,
      toggleSearch,
      performSearch,
      highlightCurrentSearchResult,
      clearSearchResults,
      prevSearchResult,
      nextSearchResult,
      
      // 编辑相关功能
      startEdit,
      saveEdit,
      cancelEdit,
      updateEditingContent,
      detectFormatIssues,
      detectSpecialChars,
      selectTableCell,
      updateTableHeader,
      updateTableCell,
      addTableRow,
      addTableColumn,
      deleteTableRow,
      deleteTableColumn,
      formatText,
      refreshPreviewHtml,
      matchSourceAndTranslation,
      calculateSimilarity,
      preprocessSentence,
      showSentenceView,
      editingSentenceIndex,
      editingSentenceParagraphIndex,
      editingSentenceText,
      startEditSentence,
      cancelEditSentence,
      saveSentenceEdit,
      isEditingSentence,
      sentenceEdited
    };
  }
});
</script>

<style scoped>
.document-translation-container {
  padding: 20px;
}

.document-translation-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.debug-mode-toggle {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  align-items: center;
  z-index: 10;
}

.debug-mode-label {
  margin-left: 8px;
  font-size: 12px;
  color: #666;
}

.document-steps {
  margin-bottom: 20px;
}

.upload-container {
  padding: 20px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.upload-btn {
  margin-top: 16px;
}

.progress-container {
  margin-top: 20px;
}

.error-message {
  color: #ff4d4f;
  margin-top: 8px;
}

.download-btn {
  margin-top: 16px;
}

.special-char-alert {
  margin-bottom: 16px;
}

.special-char-details {
  margin-top: 8px;
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.toggle-details {
  cursor: pointer;
  color: #1890ff;
  margin-left: 8px;
}

.file-info {
  margin-top: 8px;
  color: #666;
}

.language-select {
  margin-top: 20px;
}

.translate-button {
  margin-top: 20px;
}

.step-content {
  padding: 20px 0;
}

.step-actions {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.progress-text {
  margin-top: 8px;
  text-align: center;
  color: #666;
}

.char-count {
  margin-left: 8px;
  color: #999;
  font-size: 12px;
}

/* 特殊字符详情模态框样式 */
.special-char-details-modal {
  padding: 0 10px;
}

.details-description {
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 16px;
}

.details-examples {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
}

.details-examples h4 {
  margin-bottom: 8px;
  font-weight: 500;
}

.details-examples ul {
  padding-left: 20px;
  margin-bottom: 0;
}

.details-examples li {
  font-family: monospace;
  margin-bottom: 4px;
}

.details-recommendation {
  border-left: 3px solid #1890ff;
  padding-left: 12px;
}

.details-recommendation h4 {
  margin-bottom: 8px;
  font-weight: 500;
  color: #1890ff;
}

/* 网络错误提示样式 */
.network-error-alert {
  margin-bottom: 16px;
  margin-top: 16px;
}

/* 预览模态框样式 */
.preview-modal {
  display: flex;
  flex-direction: column;
}

.preview-toolbar {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #e8e8e8;
  margin-bottom: 12px;
}

.toolbar-left, .toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-panel {
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  margin-bottom: 12px;
}

.search-options {
  margin-top: 8px;
  display: flex;
  gap: 16px;
}

.search-results {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.document-preview-wrapper {
  height: 60vh;
  overflow: hidden;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  position: relative;
}

.document-preview-wrapper.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
  background-color: white;
  padding: 20px;
  box-sizing: border-box;
}

.side-by-side-view {
  display: flex;
  height: 100%;
}

.document-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.document-column-divider {
  width: 1px;
  background-color: #e8e8e8;
  cursor: col-resize;
}

.column-header {
  padding: 8px;
  background-color: #f5f5f5;
  font-weight: bold;
  border-bottom: 1px solid #e8e8e8;
}

.column-content {
  flex: 1;
  padding: 16px;
  overflow: auto;
}

.single-column-view {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-footer {
  margin-top: 12px;
  display: flex;
  justify-content: space-between;
  padding-top: 12px;
  border-top: 1px solid #e8e8e8;
}

/* 左右对照编辑视图样式 */
.para-table-view {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.para-tools {
  display: flex;
  justify-content: space-between;
  padding: 8px;
  border-bottom: 1px solid #e8e8e8;
}

.table-container {
  flex: 1;
  overflow: auto;
  padding: 0 16px 16px;
}

.para-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
}

.para-table th {
  background-color: #f5f5f5;
  padding: 8px;
  text-align: left;
  border-bottom: 2px solid #e8e8e8;
  position: sticky;
  top: 0;
  z-index: 1;
}

.line-number-col {
  width: 50px;
  text-align: center;
}

.source-col, .translated-col {
  width: 50%;
}

.para-row {
  border-bottom: 1px solid #f0f0f0;
}

.para-row.edited {
  background-color: #f6ffed;
}

.line-number {
  padding: 8px;
  text-align: center;
  color: #999;
  user-select: none;
  vertical-align: top;
}

.source-text, .translated-text {
  padding: 8px;
  vertical-align: top;
  word-break: break-word;
}

.source-text {
  color: #333;
  background-color: #f9f9f9;
  border-right: 1px solid #f0f0f0;
}

.translated-text-cell {
  position: relative;
}

.translated-text {
  padding: 8px;
  color: #1890ff;
  position: relative;
  min-height: 36px;
}

.edit-btn {
  position: absolute;
  right: 8px;
  top: 8px;
  opacity: 0;
  transition: opacity 0.2s;
}

.translated-text:hover .edit-btn {
  opacity: 1;
}

.editing-area {
  padding: 8px;
}

.edit-actions {
  margin-top: 8px;
  text-align: right;
}

.edited-mark {
  color: #52c41a;
  font-size: 12px;
  margin-left: 8px;
}

/* 编辑模态框样式 */
.edit-container {
  display: flex;
  flex-direction: column;
  height: 60vh;
}

.edit-row {
  display: flex;
  flex: 1;
  gap: 16px;
}

.source-edit-section, .translation-edit-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.source-preview {
  flex: 1;
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  background-color: #f5f5f5;
  overflow: auto;
}

.editor-toolbar {
  margin-bottom: 8px;
}

.editor-container {
  flex: 1;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
}

.editable-area {
  height: 100%;
  padding: 16px;
  overflow: auto;
  outline: none;
}

.format-warning {
  margin-top: 8px;
  padding: 8px;
  background-color: #fffbe6;
  border: 1px solid #ffe58f;
  border-radius: 4px;
  display: flex;
  align-items: flex-start;
}

.warning-icon {
  margin-right: 8px;
}

.warning-text {
  flex: 1;
}

.warning-list {
  margin-top: 4px;
  padding-left: 20px;
}

.special-char-status {
  margin-top: 8px;
  padding: 8px;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
}

.special-char-header {
  font-weight: bold;
  margin-bottom: 4px;
}

.special-char-list {
  padding-left: 20px;
  margin: 0;
}

.special-char {
  font-family: monospace;
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 2px;
  margin-right: 4px;
}

.special-char-desc {
  color: #666;
}

.table-editor-toolbar {
  margin-bottom: 8px;
  display: flex;
  gap: 8px;
}

.table-editor-container {
  overflow: auto;
  max-height: 400px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
}

.editable-table {
  width: 100%;
  border-collapse: collapse;
}

.editable-table th, .editable-table td {
  border: 1px solid #e8e8e8;
  padding: 0;
}

.editable-content {
  padding: 8px;
  min-height: 20px;
  outline: none;
}

.selected-cell {
  background-color: #e6f7ff;
}

/* 预览内容样式 */
.preview-paragraph {
  margin-bottom: 16px;
  line-height: 1.6;
}

.preview-table-container {
  margin-bottom: 16px;
  overflow-x: auto;
}

.preview-table {
  width: 100%;
  border-collapse: collapse;
}

.preview-table th, .preview-table td {
  border: 1px solid #e8e8e8;
  padding: 8px;
}

.preview-table th {
  background-color: #f5f5f5;
  font-weight: bold;
}

.empty-preview {
  color: #999;
  text-align: center;
  padding: 40px 0;
}

/* 句子级别编辑样式 */
.para-header-row {
  background-color: #f5f5f5;
}

.para-header {
  font-weight: bold;
  padding: 8px 16px;
  color: #1890ff;
}

.sentence-row {
  border-bottom: 1px dashed #f0f0f0;
}

.sentence-row:hover {
  background-color: #f9f9f9;
}

.sentence-row.low-confidence {
  background-color: #fffbe6;
}

.sentence-row.sentence-edited {
  background-color: #f6ffed;
}

.sentence-editor {
  margin-top: 8px;
}

.translated-text-cell .edit-btn {
  visibility: hidden;
  margin-left: 4px;
}

.translated-text-cell:hover .edit-btn {
  visibility: visible;
}

.edited-mark {
  margin-left: 8px;
  color: #52c41a;
  font-size: 12px;
}

.table-type-row {
  background-color: #f0f7ff;
}

/* Element Plus 组件样式调整 */
.file-list {
  margin-top: 16px;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  margin-bottom: 8px;
}

.file-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.file-icon {
  margin-right: 12px;
  font-size: 24px;
  color: #1890ff;
}

.file-details {
  flex: 1;
}

.file-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.file-description {
  color: #666;
  font-size: 12px;
}

.result-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 16px;
}

.upload-dragger {
  width: 100%;
}

.upload-dragger .el-upload-dragger {
  width: 100%;
}
</style>