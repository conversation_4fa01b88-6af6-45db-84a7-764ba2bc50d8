#!/usr/bin/env python3
"""
修复数据库表结构，添加 agent_id 字段到 knowledge_documents 表
"""

import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from app.database.db_manager import get_db_manager
except ImportError:
    # 如果导入失败，尝试直接使用数据库连接
    import psycopg2
    import sqlite3

    def get_db_connection():
        """获取数据库连接"""
        db_url = "postgresql://postgres:langpro8@localhost:5432/ai_platform"
        if db_url.startswith('postgresql'):
            return psycopg2.connect(db_url), True  # PostgreSQL
        else:
            return sqlite3.connect(db_url.replace('sqlite:///', '')), False  # SQLite

    class SimpleDBManager:
        def __init__(self):
            self.conn, self.use_postgres = get_db_connection()

        def execute_query(self, query, params=None):
            try:
                cursor = self.conn.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)

                if query.strip().upper().startswith('SELECT') or query.strip().upper().startswith('PRAGMA'):
                    result = cursor.fetchall()
                else:
                    self.conn.commit()
                    result = cursor.rowcount

                cursor.close()
                return result
            except Exception as e:
                print(f"数据库查询错误: {e}")
                return None

    def get_db_manager():
        return SimpleDBManager()

def check_and_fix_knowledge_documents_table():
    """检查并修复 knowledge_documents 表结构"""
    
    print("🔧 检查和修复 knowledge_documents 表结构")
    print("=" * 60)
    
    try:
        db_manager = get_db_manager()
        print(f"📊 数据库类型: {'PostgreSQL' if db_manager.use_postgres else 'SQLite'}")
        
        # 1. 检查表是否存在
        print("\n1. 检查 knowledge_documents 表是否存在")
        if db_manager.use_postgres:
            check_table_query = """
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'knowledge_documents'
                )
            """
        else:
            check_table_query = """
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='knowledge_documents'
            """
        
        table_exists = db_manager.execute_query(check_table_query)
        
        if db_manager.use_postgres:
            table_exists = table_exists[0][0] if table_exists else False
        else:
            table_exists = len(table_exists) > 0 if table_exists else False
        
        print(f"   表存在: {table_exists}")
        
        if not table_exists:
            print("   ❌ knowledge_documents 表不存在，需要创建")
            create_table()
            return
        
        # 2. 检查 agent_id 字段是否存在
        print("\n2. 检查 agent_id 字段是否存在")
        if db_manager.use_postgres:
            check_column_query = """
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns 
                WHERE table_name = 'knowledge_documents'
                ORDER BY ordinal_position
            """
        else:
            check_column_query = "PRAGMA table_info(knowledge_documents)"
        
        columns = db_manager.execute_query(check_column_query)
        
        print("   当前表结构:")
        if db_manager.use_postgres:
            for col in columns:
                print(f"   - {col[0]} ({col[1]}) {'NULL' if col[2] == 'YES' else 'NOT NULL'}")
            
            has_agent_id = any(col[0] == 'agent_id' for col in columns)
        else:
            for col in columns:
                print(f"   - {col[1]} ({col[2]}) {'NULL' if col[3] == 0 else 'NOT NULL'}")
            
            has_agent_id = any(col[1] == 'agent_id' for col in columns)
        
        print(f"   agent_id 字段存在: {has_agent_id}")
        
        # 3. 如果 agent_id 字段不存在，添加它
        if not has_agent_id:
            print("\n3. 添加 agent_id 字段")
            if db_manager.use_postgres:
                add_column_query = """
                    ALTER TABLE knowledge_documents 
                    ADD COLUMN agent_id VARCHAR
                """
            else:
                add_column_query = """
                    ALTER TABLE knowledge_documents 
                    ADD COLUMN agent_id TEXT
                """
            
            result = db_manager.execute_query(add_column_query)
            print(f"   执行结果: {result}")
            print("   ✅ agent_id 字段添加完成")
        else:
            print("   ✅ agent_id 字段已存在")
        
        # 4. 检查现有数据
        print("\n4. 检查现有数据")
        count_query = "SELECT COUNT(*) FROM knowledge_documents"
        total_count = db_manager.execute_query(count_query)
        total_count = total_count[0][0] if total_count else 0
        
        print(f"   总文档数: {total_count}")
        
        # 如果有 agent_id 字段，检查分配情况
        try:
            agent_null_query = "SELECT COUNT(*) FROM knowledge_documents WHERE agent_id IS NULL"
            null_count = db_manager.execute_query(agent_null_query)
            null_count = null_count[0][0] if null_count else 0
            
            print(f"   未分配智能体的文档数: {null_count}")
            print(f"   已分配智能体的文档数: {total_count - null_count}")
        except Exception as e:
            print(f"   检查分配情况失败: {e}")
        
        # 5. 测试新的API查询
        print("\n5. 测试智能体专属知识库查询")
        test_agent_id = "e55f5e84-6d8b-4265-8e55-728bdb0d2455"
        
        try:
            if db_manager.use_postgres:
                test_query = """
                    SELECT COUNT(*) FROM knowledge_documents 
                    WHERE agent_id = %s OR (agent_id IS NULL AND knowledge_base_id LIKE %s)
                """
                params = (test_agent_id, f"{test_agent_id}-%")
            else:
                test_query = """
                    SELECT COUNT(*) FROM knowledge_documents 
                    WHERE agent_id = ? OR (agent_id IS NULL AND knowledge_base_id LIKE ?)
                """
                params = (test_agent_id, f"{test_agent_id}-%")
            
            agent_docs = db_manager.execute_query(test_query, params)
            agent_docs_count = agent_docs[0][0] if agent_docs else 0
            
            print(f"   测试智能体 {test_agent_id[:8]}... 的文档数: {agent_docs_count}")
        except Exception as e:
            print(f"   测试查询失败: {e}")
        
        print("\n✅ 数据库表结构检查和修复完成")
        
    except Exception as e:
        print(f"❌ 数据库操作失败: {e}")
        import traceback
        traceback.print_exc()

def create_table():
    """创建 knowledge_documents 表"""
    print("\n📝 创建 knowledge_documents 表")
    
    try:
        db_manager = get_db_manager()
        
        if db_manager.use_postgres:
            create_query = """
                CREATE TABLE knowledge_documents (
                    id VARCHAR PRIMARY KEY DEFAULT gen_random_uuid()::text,
                    agent_id VARCHAR,
                    knowledge_base_id VARCHAR,
                    title VARCHAR NOT NULL,
                    content TEXT NOT NULL,
                    content_type VARCHAR DEFAULT 'text',
                    doc_metadata JSON DEFAULT '{}',
                    file_path VARCHAR,
                    file_size INTEGER DEFAULT 0,
                    chunks JSON DEFAULT '[]',
                    embeddings JSON DEFAULT '[]',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """
        else:
            create_query = """
                CREATE TABLE knowledge_documents (
                    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
                    agent_id TEXT,
                    knowledge_base_id TEXT,
                    title TEXT NOT NULL,
                    content TEXT NOT NULL,
                    content_type TEXT DEFAULT 'text',
                    doc_metadata TEXT DEFAULT '{}',
                    file_path TEXT,
                    file_size INTEGER DEFAULT 0,
                    chunks TEXT DEFAULT '[]',
                    embeddings TEXT DEFAULT '[]',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """
        
        result = db_manager.execute_query(create_query)
        
        if result is not None:
            print("   ✅ knowledge_documents 表创建成功")
        else:
            print("   ❌ knowledge_documents 表创建失败")
            
    except Exception as e:
        print(f"   ❌ 创建表失败: {e}")

if __name__ == "__main__":
    check_and_fix_knowledge_documents_table()
    
    print(f"\n🚀 接下来可以测试:")
    print("1. 运行: python test_simple_agent_kb.py")
    print("2. 打开前端页面测试智能体专属知识库功能")
    print("3. 在智能体编辑器中上传文件，验证隔离效果")
    print(f"4. 前端测试URL: http://192.168.1.143:3000/utilities/daily/ai-agent/editor?id=e55f5e84-6d8b-4265-8e55-728bdb0d2455")
