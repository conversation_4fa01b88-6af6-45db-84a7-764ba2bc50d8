"""
翻译模型配置和推荐
提供不同场景下的最佳翻译模型选择
"""

from typing import Dict, List, Optional
from dataclasses import dataclass
from enum import Enum

class TranslationQuality(Enum):
    """翻译质量等级"""
    FAST = "fast"           # 快速翻译，适合实时场景
    BALANCED = "balanced"   # 平衡质量和速度
    HIGH = "high"          # 高质量翻译，适合专业文档
    PREMIUM = "premium"    # 顶级质量，适合重要文档

class ModelSize(Enum):
    """模型大小"""
    SMALL = "small"    # < 3B 参数
    MEDIUM = "medium"  # 3B - 7B 参数
    LARGE = "large"    # 7B - 13B 参数
    XLARGE = "xlarge"  # > 13B 参数

@dataclass
class TranslationModel:
    """翻译模型配置"""
    name: str
    display_name: str
    size: ModelSize
    quality: TranslationQuality
    languages: List[str]  # 支持的语言对
    description: str
    parameters: Dict
    recommended_use_cases: List[str]
    pros: List[str]
    cons: List[str]

# 推荐的翻译模型配置
RECOMMENDED_MODELS = {
    # 快速翻译模型
    "qwen2.5:3b": TranslationModel(
        name="qwen2.5:3b",
        display_name="Qwen2.5 3B (快速)",
        size=ModelSize.SMALL,
        quality=TranslationQuality.FAST,
        languages=["zh", "en", "ja", "ko", "fr", "de", "es", "ru"],
        description="阿里巴巴开源的轻量级多语言模型，专门优化过中文处理",
        parameters={
            "temperature": 0.1,
            "top_p": 0.8,
            "max_tokens": 1024,
            "stop": ["</s>", "<|im_end|>"],
            "num_predict": 512
        },
        recommended_use_cases=[
            "实时聊天翻译",
            "简单文本翻译",
            "快速原型开发"
        ],
        pros=[
            "速度快，响应时间短",
            "资源占用少",
            "中文处理能力强",
            "支持多种语言"
        ],
        cons=[
            "复杂文本翻译质量一般",
            "专业术语处理能力有限"
        ]
    ),

    # 平衡质量模型
    "qwen2.5:7b": TranslationModel(
        name="qwen2.5:7b",
        display_name="Qwen2.5 7B (推荐)",
        size=ModelSize.MEDIUM,
        quality=TranslationQuality.BALANCED,
        languages=["zh", "en", "ja", "ko", "fr", "de", "es", "ru", "pt", "it"],
        description="平衡性能和质量的最佳选择，适合大多数翻译场景",
        parameters={
            "temperature": 0.05,
            "top_p": 0.9,
            "max_tokens": 2048,
            "stop": ["</s>", "<|im_end|>"],
            "num_predict": 1024
        },
        recommended_use_cases=[
            "日常文档翻译",
            "网页内容翻译",
            "邮件翻译",
            "新闻文章翻译"
        ],
        pros=[
            "质量和速度平衡",
            "支持语言丰富",
            "上下文理解能力强",
            "适合大多数场景"
        ],
        cons=[
            "对于超长文本可能有限制",
            "专业领域术语需要优化"
        ]
    ),

    # 高质量翻译模型
    "deepseek-r1:8b": TranslationModel(
        name="deepseek-r1:8b",
        display_name="DeepSeek R1 8B (高质量)",
        size=ModelSize.LARGE,
        quality=TranslationQuality.HIGH,
        languages=["zh", "en", "ja", "ko", "fr", "de", "es", "ru"],
        description="DeepSeek最新推理模型，具有强大的逻辑推理和翻译能力",
        parameters={
            "temperature": 0.01,
            "top_p": 0.95,
            "max_tokens": 4096,
            "stop": ["</s>", "<|im_end|>", "---"],
            "num_predict": 2048
        },
        recommended_use_cases=[
            "专业文档翻译",
            "技术文档翻译",
            "学术论文翻译",
            "法律文件翻译"
        ],
        pros=[
            "翻译质量极高",
            "逻辑推理能力强",
            "上下文理解深入",
            "专业术语处理好"
        ],
        cons=[
            "速度相对较慢",
            "资源占用较大",
            "可能过度解释"
        ]
    ),

    # 顶级质量模型
    "llama3.1:70b": TranslationModel(
        name="llama3.1:70b",
        display_name="Llama 3.1 70B (顶级)",
        size=ModelSize.XLARGE,
        quality=TranslationQuality.PREMIUM,
        languages=["zh", "en", "ja", "ko", "fr", "de", "es", "ru", "pt", "it", "ar"],
        description="Meta最强开源模型，提供顶级翻译质量",
        parameters={
            "temperature": 0.01,
            "top_p": 0.95,
            "max_tokens": 8192,
            "stop": ["</s>", "<|im_end|>"],
            "num_predict": 4096
        },
        recommended_use_cases=[
            "重要商务文档",
            "文学作品翻译",
            "高端客户服务",
            "品牌营销内容"
        ],
        pros=[
            "翻译质量最高",
            "文化背景理解深入",
            "创意翻译能力强",
            "支持语言最多"
        ],
        cons=[
            "速度最慢",
            "资源需求极大",
            "成本较高"
        ]
    )
}

# 语言对特殊优化
LANGUAGE_SPECIFIC_MODELS = {
    ("zh", "en"): "qwen2.5:7b",      # 中英互译推荐
    ("en", "zh"): "qwen2.5:7b",      # 英中互译推荐
    ("zh", "ja"): "qwen2.5:7b",      # 中日互译推荐
    ("ja", "zh"): "qwen2.5:7b",      # 日中互译推荐
    ("en", "ja"): "deepseek-r1:8b",  # 英日互译推荐
    ("ja", "en"): "deepseek-r1:8b",  # 日英互译推荐
}

# 领域特定模型推荐
DOMAIN_SPECIFIC_MODELS = {
    "technical": "deepseek-r1:8b",    # 技术文档
    "legal": "llama3.1:70b",          # 法律文件
    "medical": "llama3.1:70b",        # 医学文档
    "business": "qwen2.5:7b",         # 商务文档
    "academic": "deepseek-r1:8b",     # 学术论文
    "creative": "llama3.1:70b",       # 创意内容
    "news": "qwen2.5:7b",             # 新闻文章
    "casual": "qwen2.5:3b",           # 日常对话
}

def get_recommended_model(
    source_lang: str = "auto",
    target_lang: str = "zh",
    domain: str = "general",
    quality: TranslationQuality = TranslationQuality.BALANCED,
    max_response_time: Optional[float] = None
) -> str:
    """
    根据需求推荐最佳翻译模型
    
    Args:
        source_lang: 源语言
        target_lang: 目标语言
        domain: 领域
        quality: 质量要求
        max_response_time: 最大响应时间(秒)
    
    Returns:
        推荐的模型名称
    """
    # 1. 检查领域特定推荐
    if domain in DOMAIN_SPECIFIC_MODELS:
        domain_model = DOMAIN_SPECIFIC_MODELS[domain]
        if quality == TranslationQuality.FAST:
            # 如果要求快速，降级到更小的模型
            return "qwen2.5:3b"
        return domain_model
    
    # 2. 检查语言对特定推荐
    lang_pair = (source_lang, target_lang)
    if lang_pair in LANGUAGE_SPECIFIC_MODELS:
        return LANGUAGE_SPECIFIC_MODELS[lang_pair]
    
    # 3. 根据质量要求推荐
    quality_models = {
        TranslationQuality.FAST: "qwen2.5:3b",
        TranslationQuality.BALANCED: "qwen2.5:7b",
        TranslationQuality.HIGH: "deepseek-r1:8b",
        TranslationQuality.PREMIUM: "llama3.1:70b"
    }
    
    recommended = quality_models.get(quality, "qwen2.5:7b")
    
    # 4. 考虑响应时间限制
    if max_response_time and max_response_time < 5.0:
        return "qwen2.5:3b"  # 快速模型
    elif max_response_time and max_response_time < 15.0:
        return "qwen2.5:7b"  # 平衡模型
    
    return recommended

def get_model_config(model_name: str) -> Optional[TranslationModel]:
    """获取模型配置"""
    return RECOMMENDED_MODELS.get(model_name)

def list_available_models() -> List[TranslationModel]:
    """列出所有可用模型"""
    return list(RECOMMENDED_MODELS.values())

def get_models_by_quality(quality: TranslationQuality) -> List[TranslationModel]:
    """根据质量等级获取模型"""
    return [model for model in RECOMMENDED_MODELS.values() if model.quality == quality]
