#!/usr/bin/env python3
"""
测试记忆设置和知识库功能
"""

import requests
import json
import time
import os

def test_memory_settings():
    """测试记忆设置保存和加载"""
    agent_id = "e55f5e84-6d8b-4265-8e55-728bdb0d2455"
    
    print("🧠 测试记忆设置保存和加载")
    print("=" * 50)
    
    # 创建包含详细记忆设置的测试数据
    timestamp = int(time.time())
    test_data = {
        "name": f"记忆测试智能体-{timestamp}",
        "description": "测试记忆设置的智能体",
        "systemPrompt": "你是一个测试记忆功能的助手",
        
        # 详细的记忆设置
        "memory": {
            "short_term": {
                "enabled": True,
                "duration": "session",
                "max_items": 100
            },
            "long_term": {
                "enabled": True,
                "strategy": "important",
                "retention_days": 30
            },
            "semantic": {
                "enabled": True,
                "learning": "active",
                "similarity_threshold": 0.8
            },
            "episodic": {
                "enabled": False,
                "auto_save": True
            }
        },
        
        # 其他基本设置
        "avatar": "🧠",
        "personality": "analytical",
        "domain": "memory_research"
    }
    
    print(f"📝 测试记忆设置:")
    print(f"  短期记忆: {test_data['memory']['short_term']}")
    print(f"  长期记忆: {test_data['memory']['long_term']}")
    print(f"  语义记忆: {test_data['memory']['semantic']}")
    
    try:
        # 1. 更新智能体（包含记忆设置）
        update_url = f"http://localhost:8000/api/v1/agents/{agent_id}"
        print(f"\n🚀 发送更新请求...")
        
        response = requests.put(update_url, json=test_data, timeout=15)
        
        print(f"📊 更新响应状态: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 更新成功: {result.get('message')}")
        else:
            print(f"❌ 更新失败: {response.text}")
            return False
            
        # 等待数据库更新完成
        time.sleep(2)
        
        # 2. 重新加载智能体数据，检查记忆设置
        get_url = f"http://localhost:8000/api/v1/agents/{agent_id}"
        print(f"\n🔍 重新加载数据...")
        
        response = requests.get(get_url, timeout=15)
        
        print(f"📊 加载响应状态: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                agent = result.get('agent', {})
                memory_config = agent.get('memory', {})
                
                print(f"\n✅ 数据加载成功!")
                print(f"📄 智能体名称: {agent.get('name')}")
                print(f"🧠 记忆设置验证:")
                
                # 验证短期记忆
                short_term = memory_config.get('short_term', {})
                print(f"  短期记忆启用: {short_term.get('enabled', '未设置')} {'✅' if short_term.get('enabled') == True else '❌'}")
                print(f"  短期记忆持续时间: {short_term.get('duration', '未设置')} {'✅' if short_term.get('duration') == 'session' else '❌'}")
                
                # 验证长期记忆
                long_term = memory_config.get('long_term', {})
                print(f"  长期记忆启用: {long_term.get('enabled', '未设置')} {'✅' if long_term.get('enabled') == True else '❌'}")
                print(f"  长期记忆策略: {long_term.get('strategy', '未设置')} {'✅' if long_term.get('strategy') == 'important' else '❌'}")
                
                # 验证语义记忆
                semantic = memory_config.get('semantic', {})
                print(f"  语义记忆启用: {semantic.get('enabled', '未设置')} {'✅' if semantic.get('enabled') == True else '❌'}")
                print(f"  语义记忆学习: {semantic.get('learning', '未设置')} {'✅' if semantic.get('learning') == 'active' else '❌'}")
                
                # 计算成功率
                success_count = 0
                total_count = 6
                
                if short_term.get('enabled') == True: success_count += 1
                if short_term.get('duration') == 'session': success_count += 1
                if long_term.get('enabled') == True: success_count += 1
                if long_term.get('strategy') == 'important': success_count += 1
                if semantic.get('enabled') == True: success_count += 1
                if semantic.get('learning') == 'active': success_count += 1
                
                print(f"\n📊 记忆设置验证结果: {success_count}/{total_count} 成功")
                
                return success_count == total_count
                
            else:
                print(f"❌ 数据加载失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 请求失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        return False

def test_knowledge_documents_api():
    """测试知识库文档API"""
    
    print("\n📚 测试知识库文档API")
    print("=" * 40)
    
    try:
        # 1. 先上传一个测试文件
        timestamp = int(time.time())
        test_content = f"""知识库测试文档
时间戳: {timestamp}
内容: 这是一个测试知识库功能的文档
用途: 验证文档上传和列表显示功能"""
        
        test_filename = f"knowledge_test_{timestamp}.txt"
        
        # 保存测试文件
        with open(test_filename, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        print(f"📁 创建测试文件: {test_filename}")
        
        # 上传文件
        upload_url = "http://localhost:8000/api/v1/ai-agent/upload-file"
        with open(test_filename, 'rb') as f:
            files = {'file': (test_filename, f, 'text/plain')}
            response = requests.post(upload_url, files=files, timeout=30)
        
        print(f"📊 上传响应状态: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 文件上传成功: {result.get('message')}")
            else:
                print(f"❌ 文件上传失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 上传请求失败: {response.text}")
            return False
        
        # 等待处理完成
        time.sleep(3)
        
        # 2. 测试知识库文档列表API
        docs_url = "http://localhost:8000/api/v1/ai-agent/knowledge-documents"
        print(f"\n🔍 获取知识库文档列表: {docs_url}")
        
        response = requests.get(docs_url, timeout=15)
        
        print(f"📊 文档列表响应状态: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                documents = result.get('documents', [])
                total = result.get('total', 0)
                
                print(f"✅ 获取文档列表成功!")
                print(f"📄 文档总数: {total}")
                
                if documents:
                    print(f"📋 最近的文档:")
                    for i, doc in enumerate(documents[:3]):  # 显示前3个
                        print(f"  {i+1}. {doc.get('filename')} ({doc.get('file_type')}) - {doc.get('created_at')}")
                    
                    # 检查是否包含我们刚上传的文件
                    uploaded_found = any(doc.get('filename') == test_filename for doc in documents)
                    print(f"🔍 刚上传的文件是否在列表中: {'✅ 是' if uploaded_found else '❌ 否'}")
                    
                    return uploaded_found
                else:
                    print("📋 暂无文档")
                    return False
                    
            else:
                print(f"❌ 获取失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 请求失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        return False
    finally:
        # 清理测试文件
        if 'test_filename' in locals() and os.path.exists(test_filename):
            os.remove(test_filename)
            print(f"🗑️ 清理测试文件: {test_filename}")

if __name__ == "__main__":
    print("🧪 开始测试记忆设置和知识库功能")
    print("=" * 60)
    
    # 测试记忆设置
    memory_test = test_memory_settings()
    
    # 测试知识库文档API
    knowledge_test = test_knowledge_documents_api()
    
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print(f"  记忆设置保存加载: {'✅ 成功' if memory_test else '❌ 失败'}")
    print(f"  知识库文档API: {'✅ 成功' if knowledge_test else '❌ 失败'}")
    
    if memory_test and knowledge_test:
        print("\n🎉 所有测试通过！记忆设置和知识库功能完全正常！")
    else:
        print("\n⚠️ 部分测试失败，请检查相关功能")
