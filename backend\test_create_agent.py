#!/usr/bin/env python3
"""
测试创建智能体API
"""

import requests
import json

def test_create_agent():
    base_url = "http://localhost:8000/api/v1"
    
    print("🧪 测试创建智能体API")
    print("=" * 50)
    
    # 测试数据
    test_agent = {
        "name": "测试智能体",
        "description": "这是一个用于测试的智能体",
        "agent_type": "general",
        "system_prompt": "你是一个友好的AI助手",
        "avatar": "🤖",
        "personality": "friendly",
        "domain": "general",
        "language": "zh-CN",
        "temperature": 0.7,
        "max_tokens": 4000,
        "tools": [],
        "knowledge_bases": [],
        "memory_types": ["short_term", "long_term"],
        "workflow_enabled": True,
        "memory": {
            "short_term": {
                "enabled": True,
                "max_messages": 20
            },
            "long_term": {
                "enabled": True,
                "max_entries": 1000
            }
        },
        "advanced": {
            "context_window": 4000,
            "response_format": "markdown",
            "streaming": True
        },
        "safety": {
            "content_filter": True,
            "pii_detection": True
        }
    }
    
    print("\n1. 测试创建智能体")
    try:
        response = requests.post(
            f"{base_url}/agents",
            json=test_agent,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"   URL: {base_url}/agents")
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                agent = data.get('agent', {})
                print(f"   ✅ 智能体创建成功")
                print(f"   ✅ 智能体ID: {agent.get('id')}")
                print(f"   ✅ 智能体名称: {agent.get('name')}")
                print(f"   ✅ 智能体类型: {agent.get('agent_type')}")
                
                # 保存创建的智能体ID用于后续测试
                created_agent_id = agent.get('id')
                
                # 测试获取创建的智能体
                print("\n2. 测试获取创建的智能体")
                get_response = requests.get(f"{base_url}/agents/{created_agent_id}")
                print(f"   URL: {base_url}/agents/{created_agent_id}")
                print(f"   状态码: {get_response.status_code}")
                
                if get_response.status_code == 200:
                    get_data = get_response.json()
                    print(f"   ✅ 获取智能体成功")
                    print(f"   ✅ 名称匹配: {get_data.get('name') == test_agent['name']}")
                    print(f"   ✅ 描述匹配: {get_data.get('description') == test_agent['description']}")
                else:
                    print(f"   ❌ 获取智能体失败: {get_response.status_code}")
                
                return created_agent_id
                
            else:
                print(f"   ❌ 创建失败: {data.get('error', 'Unknown error')}")
        else:
            print(f"   ❌ 创建请求失败: {response.status_code}")
            print(f"   响应内容: {response.text[:200]}")
            
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
    
    return None

def test_unified_chat_with_new_agent(agent_id):
    """测试使用新创建的智能体进行统一聊天"""
    if not agent_id:
        print("\n❌ 没有可用的智能体ID，跳过统一聊天测试")
        return
    
    print(f"\n3. 测试统一聊天页面")
    print(f"   智能体ID: {agent_id}")
    print(f"   测试URL: http://*************:3000/utilities/daily/unified-chat?agent_id={agent_id}")
    print("   请手动在浏览器中测试此URL")

if __name__ == "__main__":
    # 测试创建智能体
    created_agent_id = test_create_agent()
    
    # 测试统一聊天
    test_unified_chat_with_new_agent(created_agent_id)
    
    print("\n" + "=" * 50)
    print("📊 测试总结")
    print("1. 创建智能体API - 检查是否返回200状态码")
    print("2. 获取智能体API - 检查创建的智能体是否可以正常获取")
    print("3. 统一聊天页面 - 手动测试页面是否正常加载")
    
    print("\n🔧 如果测试失败，请检查:")
    print("1. 后端服务是否正常运行")
    print("2. 数据库连接是否正常")
    print("3. API路由是否正确配置")
    print("4. 前端页面是否有JavaScript错误")
