<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UnifiedAgentChat 调试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .debug-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .debug-header {
            text-align: center;
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 2px solid #e2e8f0;
        }
        
        .debug-section {
            margin-bottom: 24px;
            padding: 16px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: #f8fafc;
        }
        
        .debug-section h3 {
            margin: 0 0 16px 0;
            color: #1e293b;
        }
        
        .test-button {
            display: inline-block;
            padding: 10px 20px;
            background: #3b82f6;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin: 4px;
            font-weight: 500;
        }
        
        .test-button:hover {
            background: #2563eb;
        }
        
        .status {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 8px;
        }
        
        .status.success { background: #dcfce7; color: #166534; }
        .status.error { background: #fef2f2; color: #dc2626; }
        .status.pending { background: #fef3c7; color: #d97706; }
        
        .code-block {
            background: #1e293b;
            color: #e2e8f0;
            padding: 12px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            overflow-x: auto;
            margin: 8px 0;
        }
        
        .log-area {
            background: #000;
            color: #0f0;
            padding: 16px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
            margin: 16px 0;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }
        
        .test-card {
            border: 1px solid #d1d5db;
            border-radius: 8px;
            padding: 16px;
            background: white;
        }
        
        .test-card h4 {
            margin: 0 0 12px 0;
            color: #374151;
        }
        
        .url-display {
            background: #f3f4f6;
            padding: 8px 12px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
            margin: 8px 0;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <div class="debug-header">
            <h1>🐛 UnifiedAgentChat 调试页面</h1>
            <p>诊断和修复统一聊天页面的问题</p>
        </div>
        
        <div class="debug-section">
            <h3>🎯 问题描述</h3>
            <p>访问 <code>/utilities/daily/unified-chat?agent_id=xxx</code> 时页面显示空白</p>
            <div class="url-display">
                当前测试URL: http://192.168.1.143:3000/utilities/daily/unified-chat?agent_id=e55f5e84-6d8b-4265-8e55-728bdb0d2455
            </div>
        </div>
        
        <div class="debug-section">
            <h3>🔍 测试链接</h3>
            <div class="test-grid">
                <div class="test-card">
                    <h4>正常访问</h4>
                    <a href="http://192.168.1.143:3000/utilities/daily/unified-chat?agent_id=e55f5e84-6d8b-4265-8e55-728bdb0d2455" 
                       class="test-button" target="_blank">
                        打开统一聊天页面
                    </a>
                    <span class="status pending">待测试</span>
                </div>
                
                <div class="test-card">
                    <h4>测试模式</h4>
                    <a href="http://192.168.1.143:3000/utilities/daily/unified-chat?agent_id=test-123&test=true" 
                       class="test-button" target="_blank">
                        测试模式访问
                    </a>
                    <span class="status pending">待测试</span>
                </div>
                
                <div class="test-card">
                    <h4>演示模式</h4>
                    <a href="http://192.168.1.143:3000/utilities/daily/unified-chat?agent_id=e55f5e84-6d8b-4265-8e55-728bdb0d2455&demo=1" 
                       class="test-button" target="_blank">
                        演示模式访问
                    </a>
                    <span class="status pending">待测试</span>
                </div>
                
                <div class="test-card">
                    <h4>直接聊天路由</h4>
                    <a href="http://192.168.1.143:3000/chat/e55f5e84-6d8b-4265-8e55-728bdb0d2455" 
                       class="test-button" target="_blank">
                        全屏聊天页面
                    </a>
                    <span class="status pending">待测试</span>
                </div>
            </div>
        </div>
        
        <div class="debug-section">
            <h3>🔧 已修复的问题</h3>
            <ul>
                <li>✅ agent-service.js API路径修复 (添加 /api/v1 前缀)</li>
                <li>✅ UnifiedAgentChat.vue 组件导入修复</li>
                <li>✅ 默认聊天组件设置修复</li>
                <li>✅ 错误处理和调试信息增强</li>
            </ul>
        </div>
        
        <div class="debug-section">
            <h3>📊 API测试</h3>
            <div class="test-grid">
                <div class="test-card">
                    <h4>智能体详情API</h4>
                    <button onclick="testAgentDetailAPI()" class="test-button">
                        测试 GET /api/v1/agents/{id}
                    </button>
                    <div id="agent-api-result" class="status pending">未测试</div>
                </div>
                
                <div class="test-card">
                    <h4>可用模型API</h4>
                    <button onclick="testModelsAPI()" class="test-button">
                        测试 GET /api/v1/agents/available-models
                    </button>
                    <div id="models-api-result" class="status pending">未测试</div>
                </div>
            </div>
        </div>
        
        <div class="debug-section">
            <h3>📝 控制台日志</h3>
            <div id="console-log" class="log-area">
                等待日志输出...
            </div>
            <button onclick="clearLog()" class="test-button">清空日志</button>
        </div>
        
        <div class="debug-section">
            <h3>🚀 预期修复结果</h3>
            <ul>
                <li>页面正常加载，显示加载状态</li>
                <li>成功获取智能体信息</li>
                <li>正确渲染聊天界面组件</li>
                <li>无JavaScript错误</li>
            </ul>
        </div>
    </div>
    
    <script>
        // 日志记录
        const logArea = document.getElementById('console-log');
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        
        function addLog(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logArea.textContent += logEntry;
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addLog(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            addLog(args.join(' '), 'error');
        };
        
        function clearLog() {
            logArea.textContent = '日志已清空...\n';
        }
        
        // API测试函数
        async function testAgentDetailAPI() {
            const resultDiv = document.getElementById('agent-api-result');
            resultDiv.textContent = '测试中...';
            resultDiv.className = 'status pending';
            
            try {
                const response = await fetch('http://192.168.1.143:8000/api/v1/agents/e55f5e84-6d8b-4265-8e55-728bdb0d2455');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.textContent = '✅ API正常';
                    resultDiv.className = 'status success';
                    console.log('智能体详情API测试成功:', data);
                } else {
                    resultDiv.textContent = `❌ ${response.status}`;
                    resultDiv.className = 'status error';
                    console.error('智能体详情API测试失败:', data);
                }
            } catch (error) {
                resultDiv.textContent = '❌ 网络错误';
                resultDiv.className = 'status error';
                console.error('智能体详情API测试错误:', error);
            }
        }
        
        async function testModelsAPI() {
            const resultDiv = document.getElementById('models-api-result');
            resultDiv.textContent = '测试中...';
            resultDiv.className = 'status pending';
            
            try {
                const response = await fetch('http://192.168.1.143:8000/api/v1/agents/available-models');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.textContent = '✅ API正常';
                    resultDiv.className = 'status success';
                    console.log('可用模型API测试成功:', data);
                } else {
                    resultDiv.textContent = `❌ ${response.status}`;
                    resultDiv.className = 'status error';
                    console.error('可用模型API测试失败:', data);
                }
            } catch (error) {
                resultDiv.textContent = '❌ 网络错误';
                resultDiv.className = 'status error';
                console.error('可用模型API测试错误:', error);
            }
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🐛 UnifiedAgentChat 调试页面已加载');
            console.log('📋 请按照以下步骤进行调试：');
            console.log('1. 点击API测试按钮，确认后端接口正常');
            console.log('2. 点击测试链接，观察页面加载情况');
            console.log('3. 打开浏览器开发者工具，查看控制台错误');
            console.log('4. 检查网络请求是否成功');
        });
    </script>
</body>
</html>
