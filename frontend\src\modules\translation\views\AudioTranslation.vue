<template>
  <div class="container">
    <PageHeaderGradient 
      title="音频翻译"
      description="上传音频文件，自动识别并翻译成多种语言"
    />

    <el-row :gutter="24">
      <!-- 左侧表单区域 -->
      <el-col :span="10">
        <el-card class="translation-card">
          <template #header>翻译设置</template>
          <el-form :model="formState" :rules="rules" label-position="top" ref="formRef">
            <!-- 音频上传 -->
            <el-form-item prop="sourceAudio" label="音频文件">
              <el-upload
                class="upload-dragger"
                drag
                v-model:file-list="audioList"
                :before-upload="beforeAudioUpload"
                @change="handleAudioChange"
                :maxCount="1"
                accept="audio/mp3,audio/wav,audio/mpeg,audio/ogg"
              >
                <p class="ant-upload-drag-icon">
                  <el-icon><Microphone /></el-icon>
                </p>
                <p class="ant-upload-text">点击或拖拽音频文件到此区域上传</p>
                <p class="ant-upload-hint">
                  支持MP3、WAV、OGG格式，最大100MB
                </p>
              </el-upload>
            </el-form-item>

            <!-- 源语言 -->
            <el-form-item prop="sourceLanguage" label="源语言">
              <el-select
                v-model="formState.sourceLanguage"
                placeholder="自动检测"
                clearable
              >
                <el-option
                  v-for="option in sourceLanguageOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>

            <!-- 目标语言 -->
            <el-form-item prop="targetLanguage" label="目标语言">
              <el-select
                v-model="formState.targetLanguage"
                placeholder="请选择目标语言"
              >
                <el-option
                  v-for="option in targetLanguageOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>

            <!-- 高级选项 -->
            <el-collapse>
              <el-collapse-item name="1" title="高级选项">
                <!-- 翻译质量 -->
                <el-form-item prop="quality" label="翻译质量">
                  <el-radio-group v-model="formState.quality">
                    <el-radio value="standard">标准</el-radio>
                    <el-radio value="high">高质量</el-radio>
                    <el-radio value="professional">专业</el-radio>
                  </el-radio-group>
                </el-form-item>

                <!-- 专业领域 -->
                <el-form-item prop="domain" label="专业领域">
                  <el-select
                    v-model="formState.domain"
                    placeholder="通用"
                    clearable
                  >
                    <el-option
                      v-for="option in domainOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>

                <!-- 输出格式 -->
                <el-form-item prop="outputFormat" label="输出格式">
                  <el-select v-model="formState.outputFormat">
                    <el-option
                      v-for="option in outputFormatOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>

                <!-- 保留原音频 -->
                <el-form-item prop="keepOriginal" label="原音频">
                  <el-switch v-model="formState.keepOriginal" />
                  <span class="option-note">在翻译后的音频中保留原始音频</span>
                </el-form-item>
              </el-collapse-item>
            </el-collapse>

            <!-- 提交按钮 -->
            <el-form-item>
              <div class="button-group">
                <el-button type="primary" :loading="translating" @click="startTranslation">
                  开始翻译
                </el-button>
                <el-button @click="resetForm">重置</el-button>
              </div>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 右侧预览区域 -->
      <el-col :span="14">
        <el-card v-if="translationStatus !== 'empty'" class="translation-card">
          <template #header>翻译结果</template>
          <div v-if="translationStatus === 'uploading'" class="status-container">
            <div class="loading-content" v-loading="true" element-loading-text="处理音频中...">
              <p>正在上传和处理音频文件，请稍候</p>
              <el-progress :percentage="uploadProgress" status="active" />
            </div>
          </div>

          <div v-else-if="translationStatus === 'transcribing'" class="status-container">
            <div class="loading-content" v-loading="true" element-loading-text="识别文本中...">
              <p>正在识别音频内容，大约需要 {{ estimatedTime }} 秒</p>
              <el-progress :percentage="transcriptionProgress" status="active" />
              <div class="action-buttons">
                <el-button @click="moveToBackground" type="primary" size="large" class="move-background-btn">
                  <el-icon><ArrowLeft /></el-icon>
                  <span class="btn-text">移至后台处理</span>
                  <el-icon><ArrowRight /></el-icon>
                </el-button>
              </div>
            </div>
          </div>

          <div v-else-if="translationStatus === 'translating'" class="status-container">
            <div class="loading-content" v-loading="true" element-loading-text="翻译中...">
              <p v-if="translationProgress >= 90">正在生成翻译后的音频...</p>
              <p v-else>正在翻译，文本长度 {{ transcriptLength }} 字符</p>
              <el-progress :percentage="translationProgress" status="active" />
              <p v-if="translationProgress > 90 && processingTime > 60" class="warning-text">
                <el-icon><Warning /></el-icon> 处理时间较长，可能遇到了复杂内容
              </p>
              <div class="action-buttons">
                <el-button @click="moveToBackground" type="primary" size="large" class="move-background-btn">
                  <el-icon><ArrowLeft /></el-icon>
                  <span class="btn-text">移至后台处理</span>
                  <el-icon><ArrowRight /></el-icon>
                </el-button>
              </div>
            </div>
          </div>

          <div v-else-if="translationStatus === 'complete'" class="result-container">
            <el-tabs v-model="activeTabKey">
              <el-tab-pane name="text" label="文本">
                <div class="text-result">
                  <div class="transcript-section">
                    <div class="section-header">
                      <h3>原文（{{ getLanguageName(detectedLanguage) || '自动检测' }}）</h3>
                      <el-button type="primary" link size="small" @click="copyText(transcript)">
                        <el-icon><DocumentCopy /></el-icon> 复制
                      </el-button>
                    </div>
                    <div class="text-content">
                      {{ transcript }}
                    </div>
                  </div>

                  <div class="translation-section">
                    <div class="section-header">
                      <h3>译文（{{ getLanguageName(formState.targetLanguage) }}）</h3>
                      <el-button type="primary" link size="small" @click="copyText(translatedText)">
                        <el-icon><DocumentCopy /></el-icon> 复制
                      </el-button>
                    </div>
                    <div class="text-content">
                      {{ translatedText }}
                    </div>
                  </div>
                </div>
              </el-tab-pane>

              <el-tab-pane name="audio" label="音频">
                <div class="audio-result">
                  <div class="audio-player-container">
                    <h3>翻译后的音频</h3>
                    <audio controls class="audio-player" :src="translatedAudioUrl" @error="handleAudioLoadError"></audio>
                    <div class="audio-actions">
                      <el-button type="primary" @click="downloadAudio">
                        <el-icon><Download /></el-icon> 下载音频
                      </el-button>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
              
              <el-tab-pane name="timeline" label="时间轴">
                <div class="timeline-result">
                  <el-timeline>
                    <el-timeline-item v-for="(segment, index) in segments" :key="index" :icon="Clock">
                      <div class="segment-container">
                        <div class="segment-time">{{ formatTime(segment.startTime || segment.start) }} - {{ formatTime(segment.endTime || segment.end) }}</div>
                        <div class="segment-content">
                          <div class="segment-original">{{ segment.text }}</div>
                          <div class="segment-translated">{{ segment.translatedText }}</div>
                        </div>
                      </div>
                    </el-timeline-item>
                  </el-timeline>
                  
                  <!-- 添加反馈区域，用于报告翻译不完整问题 -->
                  <div class="feedback-section" v-if="segments.some(s => s.translatedText.includes('翻译缺失'))">
                    <el-alert type="warning" :closable="false">
                      <template #title>
                        <div>检测到翻译不完整，部分段落可能没有被翻译</div>
                      </template>
                      <template #default>
                        <p>这可能是由后端翻译服务问题导致。您可以：</p>
                        <ol>
                          <li>重新尝试翻译</li>
                          <li>将音频分割成较小的文件分别翻译</li>
                          <li>发送反馈给系统管理员</li>
                        </ol>
                        <div class="feedback-actions">
                          <el-button type="primary" @click="sendTranslationFeedback" :loading="sendingFeedback">
                            发送反馈
                          </el-button>
                        </div>
                      </template>
                    </el-alert>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
            
            <div class="result-actions">
              <div class="action-buttons">
                <el-button @click="saveToHistory">
                  <el-icon><Folder /></el-icon> 保存到历史
                </el-button>
                <el-button @click="shareTranslation">
                  <el-icon><Share /></el-icon> 分享
                </el-button>
                <el-button @click="navigateToTaskCenter" type="default">
                  <el-icon><List /></el-icon> 查看任务中心
                </el-button>
              </div>
            </div>
          </div>

          <div v-else-if="translationStatus === 'error'" class="status-container error-container">
            <el-result
              icon="error"
              title="翻译失败"
              :sub-title="errorMessage || '处理音频时出错，请检查音频文件并重试'"
            >
              <template #extra>
                <el-button type="primary" @click="resetForm">
                  重新上传
                </el-button>
              </template>
            </el-result>
          </div>
        </el-card>

        <el-card v-else class="tips-card">
          <template #header>使用指南</template>
          <el-timeline>
            <el-timeline-item>上传高质量、清晰的音频获得最佳翻译效果</el-timeline-item>
            <el-timeline-item>如果知道源语言，请选择具体语言以提高识别准确率</el-timeline-item>
            <el-timeline-item>专业领域选项可以提高特定行业术语的翻译质量</el-timeline-item>
            <el-timeline-item>较短的音频（5分钟以内）处理速度更快</el-timeline-item>
            <el-timeline-item>翻译后可下载音频文件或复制文本</el-timeline-item>
          </el-timeline>
        </el-card>

        <!-- 添加音频问题排查部分 -->
        <div v-if="translationStatus === 'error' && errorMessage && errorMessage.includes('无法识别音频内容')" class="troubleshooting-guide mt-4">
          <el-alert type="warning" show-icon :closable="false">
            <template #title>音频识别问题排查指南</template>
            <template #default>
              <p>您的音频无法正常识别，可能的原因：</p>
              <ol>
                <li>音频质量问题：背景噪音过大或录音太小声</li>
                <li>音频格式不兼容：尝试转换为标准MP3或WAV格式</li>
                <li>语音不清晰：尝试更清晰地说话</li>
                <li>文件损坏：检查文件是否可以在其他播放器正常播放</li>
              </ol>
              <p>建议操作：</p>
              <ul>
                <li>重新录制质量更好的音频</li>
                <li>确保使用支持的语言（中文、英语、日语等主流语言）</li>
                <li>检查麦克风是否正常工作</li>
                <li>转换为标准音频格式（MP3或WAV）后重试</li>
                <li><a href="#" @click.prevent="showFormatConverterGuide = true">查看格式转换指南</a></li>
              </ul>
            </template>
          </el-alert>
        </div>

        <!-- 格式转换指南弹窗 -->
        <el-dialog v-model="showFormatConverterGuide" title="音频格式转换指南" width="700px">
          <div class="format-converter-guide">
            <h3>使用在线音频转换工具</h3>
            <p>您可以使用以下在线工具转换音频格式：</p>
            <ul>
              <li><a href="https://online-audio-converter.com/" target="_blank">Online Audio Converter</a></li>
              <li><a href="https://convertio.co/audio-converter/" target="_blank">Convertio</a></li>
              <li><a href="https://cloudconvert.com/audio-converter" target="_blank">CloudConvert</a></li>
            </ul>

            <h3>推荐设置</h3>
            <p>为了获得最佳识别效果，请使用以下设置转换音频：</p>
            <ul>
              <li>格式：MP3或WAV</li>
              <li>采样率：16kHz</li>
              <li>比特率：128 kbps或更高</li>
              <li>声道：单声道</li>
            </ul>

            <h3>使用软件转换</h3>
            <p>您也可以使用以下免费软件转换音频格式：</p>
            <ul>
              <li><a href="https://www.audacityteam.org/download/" target="_blank">Audacity</a> - 开源音频编辑器，可以导入大多数格式并转换</li>
              <li><a href="https://www.videolan.org/vlc/" target="_blank">VLC Media Player</a> - 除了播放，还可以转换音频格式</li>
            </ul>

            <h3>转换步骤</h3>
            <ol>
              <li>上传您的音频文件到转换工具</li>
              <li>选择输出格式为MP3或WAV</li>
              <li>调整音频参数（如可能）：采样率16kHz，单声道</li>
              <li>转换并下载文件</li>
              <li>使用下载的文件重试翻译</li>
            </ol>

            <h3>音频增强提示</h3>
            <p>如果您的音频质量不佳，可以尝试以下增强措施：</p>
            <ul>
              <li>使用Audacity进行降噪处理（效果 > 降噪）</li>
              <li>调整音量（效果 > 标准化）</li>
              <li>剪切掉音频前后的静音部分</li>
              <li>使用高品质麦克风重新录制</li>
            </ul>
          </div>
          <template #footer>
            <el-button type="primary" @click="showFormatConverterGuide = false">
              我知道了
            </el-button>
          </template>
        </el-dialog>

        <!-- 添加加强版音频检测工具 -->
        <div v-if="translationStatus === 'empty' || translationStatus === 'error'" class="additional-tools mt-4">
          <el-collapse>
            <el-collapse-item name="1" title="高级音频检测工具">
              <el-alert type="info" show-icon :closable="false" style="margin-bottom: 16px">
                <template #title>为什么音频可能无法识别?</template>
                <template #default>
                  <p>语音识别系统对音频有一定的要求，以下因素会影响识别：</p>
                  <ol>
                    <li><strong>格式兼容性</strong>：某些特殊编码可能不兼容</li>
                    <li><strong>音频质量</strong>：背景噪音、音量过低都会影响识别</li>
                    <li><strong>语言模式</strong>：系统对某些语言/方言的识别能力不同</li>
                    <li><strong>采样率/位深</strong>：非标准音频参数可能影响处理</li>
                    <li><strong>音频长度</strong>：过短或过长的音频都可能难以识别</li>
                  </ol>
                </template>
              </el-alert>
              
              <el-card class="mb-3">
                <template #header>
                  <div class="card-header">
                    <span>音频质量检测</span>
                    <el-button
                      type="primary"
                      size="small"
                      :disabled="!formState.sourceAudio"
                      @click="analyzeAudioQuality(formState.sourceAudio)"
                      :loading="loading"
                    >
                      检测音频质量
                    </el-button>
                  </div>
                </template>
                <div v-if="audioQualityInfo">
                  <div v-if="audioQualityInfo.isProcessing" v-loading="true" element-loading-text="分析中...">
                    <div style="height: 60px;"></div>
                  </div>
                  <div v-else>
                    <div v-if="audioQualityInfo.status === 'success'" class="audio-analysis-result mt-3">
                      <el-collapse>
                        <el-collapse-item name="1" title="音频分析结果">
                          <div class="audio-score">
                            <div class="score-circle" :class="{
                              'high-score': audioQualityInfo.quality_score >= 8,
                              'medium-score': audioQualityInfo.quality_score >= 6 && audioQualityInfo.quality_score < 8,
                              'low-score': audioQualityInfo.quality_score < 6
                            }">
                              {{ audioQualityInfo.quality_score }}/10
                            </div>
                            <div class="score-text">
                              <div>可识别性: {{ audioQualityInfo.is_recognizable ? '良好' : '较差' }}</div>
                              <div>文件: {{ audioQualityInfo.file_info.filename }}</div>
                              <div>时长: {{ audioQualityInfo.audio_properties.duration_seconds.toFixed(1) }}秒</div>
                            </div>
                          </div>
                          
                          <el-divider />

                          <div class="audio-properties">
                            <h4>音频属性</h4>
                            <el-descriptions :column="2" size="small" border>
                              <el-descriptions-item label="采样率">{{ audioQualityInfo.audio_properties.frame_rate }} Hz</el-descriptions-item>
                              <el-descriptions-item label="通道数">{{ audioQualityInfo.audio_properties.channels }} ({{ audioQualityInfo.audio_properties.channels === 1 ? '单声道' : '立体声' }})</el-descriptions-item>
                              <el-descriptions-item label="样本宽度">{{ audioQualityInfo.audio_properties.sample_width }} 字节</el-descriptions-item>
                              <el-descriptions-item label="音量电平">{{ audioQualityInfo.audio_properties.dBFS.toFixed(2) }} dBFS</el-descriptions-item>
                            </el-descriptions>
                          </div>
                          
                          <el-divider />

                          <div class="recommendations">
                            <h4>改进建议</h4>
                            <el-alert
                              v-if="audioQualityInfo.recommendations.length === 0"
                              type="success"
                              title="音频质量良好，无需改进"
                              show-icon
                              :closable="false"
                            />
                            <div
                              v-else
                              class="recommendation-list"
                            >
                              <div
                                v-for="(item, index) in audioQualityInfo.recommendations"
                                :key="index"
                                class="recommendation-item"
                              >
                                <el-icon><InfoFilled /></el-icon> {{ item }}
                              </div>
                            </div>
                          </div>
                        </el-collapse-item>
                      </el-collapse>
                    </div>
                  </div>
                </div>
                <p v-else>上传音频文件后点击"检测音频质量"来分析您的音频是否适合机器识别</p>
              </el-card>
              
              <!-- 添加API连接测试卡片 -->
              <el-card class="mb-3">
                <template #header>
                  <div class="card-header">
                    <span>API连接测试</span>
                    <el-button
                      type="primary"
                      size="small"
                      @click="testAudioAPI"
                      :loading="loading"
                    >
                      测试API连接
                    </el-button>
                  </div>
                </template>
                <p>点击"测试API连接"按钮检查前端是否能够正常连接到后端翻译服务。如果测试成功，说明服务器正在运行且可以接收请求。</p>
                <el-alert
                  v-if="errorMessage && errorMessage.includes('API')"
                  :title="errorMessage"
                  :type="errorMessage.includes('正常') ? 'success' : 'warning'"
                  show-icon
                  :closable="false"
                  style="margin-top: 10px"
                />
              </el-card>
              
              <el-card class="mb-3">
                <template #header>支持的音频格式</template>
                <div class="format-support-info">
                  <el-descriptions border :column="1">
                    <el-descriptions-item label="最佳支持格式">
                      WAV (16kHz, 单声道), MP3 (128+ kbps)
                    </el-descriptions-item>
                    <el-descriptions-item label="支持格式">
                      WAV, MP3, FLAC, OGG, M4A, AAC, WMA
                    </el-descriptions-item>
                    <el-descriptions-item label="最佳时长">
                      5秒 - 5分钟
                    </el-descriptions-item>
                    <el-descriptions-item label="最大文件大小">
                      100MB
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
              </el-card>
            </el-collapse-item>
          </el-collapse>
        </div>
      </el-col>
    </el-row>

    <!-- 添加TaskStatusManager组件，放置在最外层，使其可以覆盖整个页面 -->
    <teleport to="body" v-if="showTaskManager">
      <div class="task-manager-container" role="dialog" aria-modal="true" aria-labelledby="task-manager-title">
        <task-status-manager
          :task-id="currentTranslationId"
          :task-type="'audio_translation'"
          :task-title="'音频翻译'"
          :status-endpoint="'/api/translation/status'"
          :check-interval="3000"
          :default-mode="taskMode"
          :skip-mode-selection="true"
          :result-type="'audio'"
          :prevent-auto-close="true"
          @task-completed="handleTaskCompleted"
          @task-failed="handleTaskFailed"
          @task-canceled="handleTaskCanceled"
          @mode-changed="handleModeChanged"
          @add-to-notifications="handleAddToNotifications"
          @go-to-tasks="navigateToTaskCenter"
          @task-closed="handleTaskClosed"
        />
      </div>
    </teleport>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted, watch, computed, getCurrentInstance } from 'vue';
import { ElMessage } from 'element-plus';
import {
  Microphone,
  DocumentCopy,
  Download,
  Folder,
  Share,
  Clock,
  InfoFilled,
  Warning,
  ArrowLeft,
  ArrowRight,
  List
} from '@element-plus/icons-vue';
import { translateAudio, getAudioFile, saveTranslationToHistory, getTranslationResult } from '@/api/translationService';
import axios from 'axios';
import PageHeaderGradient from '@/components/PageHeaderGradient.vue';
import TaskStatusManager from '@/components/task/TaskStatusManager.vue';
import { getLanguageName as getLangName, normalizeLanguageCode, toRegionalCode, toSimpleCode } from '@/utils/languages';
import { useLanguages } from '@/composables/useLanguages';

export default defineComponent({
  name: 'AudioTranslation',
  components: {
    Microphone,
    DocumentCopy,
    Download,
    Folder,
    Share,
    Clock,
    InfoFilled,
    Warning,
    ArrowLeft,
    ArrowRight,
    List,
    PageHeaderGradient,
    TaskStatusManager
  },
  setup() {
    // 表单状态
    const formRef = ref(null);
    const audioList = ref([]);
    const translating = ref(false);
    const translationStatus = ref('empty'); // empty, uploading, transcribing, translating, complete, error
    const uploadProgress = ref(0);
    const transcriptionProgress = ref(0);
    const translationProgress = ref(0);
    const activeTabKey = ref('text');
    
    // 添加当前翻译ID变量
    const currentTranslationId = ref('');
    
    // 添加任务管理相关状态
    const showTaskManager = ref(false);
    const taskMode = ref('foreground'); // foreground 或 background
    const taskCompleted = ref(false);
    const navigateToTask = ref(false);
    
    // 使用 vue-router
    const router = getCurrentInstance()?.appContext.config.globalProperties.$router;

    // 翻译结果
    const transcript = ref('');
    const transcriptLength = ref(0);
    const translatedText = ref('');
    const translatedAudioUrl = ref('');
    const detectedLanguage = ref('');
    const segments = ref([]);

    // 表单数据
    const formState = reactive({
      sourceAudio: null,
      sourceLanguage: 'auto',
      targetLanguage: 'en-US',
      quality: 'standard',
      domain: 'general',
      outputFormat: 'mp3',
      keepOriginal: false
    });

    // 表单验证规则
    const rules = {
      sourceAudio: [
        { required: true, message: '请上传音频文件', trigger: 'change' }
      ],
      targetLanguage: [
        { required: true, message: '请选择目标语言', trigger: 'change' }
      ]
    };

    // 使用公用的语言配置
    const { languages } = useLanguages();

    // 语言选项 - 使用公用语言列表
    const sourceLanguageOptions = computed(() => {
      return languages.value.map(lang => ({
        value: lang.code,
        label: lang.name
      }));
    });

    const targetLanguageOptions = computed(() => {
      // 目标语言不包含"自动检测"
      return languages.value
        .filter(lang => lang.code !== 'auto')
        .map(lang => ({
          value: lang.code,
          label: lang.name
        }));
    });

    // 专业领域选项
    const domainOptions = [
      { value: '', label: '通用' },
      { value: 'tech', label: '科技' },
      { value: 'medical', label: '医疗' },
      { value: 'legal', label: '法律' },
      { value: 'financial', label: '金融' },
      { value: 'academic', label: '学术' }
    ];

    // 输出格式选项
    const outputFormatOptions = [
      { value: 'mp3', label: 'MP3' },
      { value: 'wav', label: 'WAV' },
      { value: 'ogg', label: 'OGG' }
    ];

    // 估计处理时间（基于音频时长）
    const estimatedTime = computed(() => {
      // 模拟计算，实际应根据文件大小或时长计算
      return Math.round(Math.random() * 20 + 10);
    });

    // 上传前检查
    const beforeAudioUpload = (file) => {
      const isAudio = file.type === 'audio/mp3' || file.type === 'audio/wav' || 
                      file.type === 'audio/mpeg' || file.type === 'audio/ogg';
      if (!isAudio) {
        ElMessage.error('只能上传音频文件!');
        return false;
      }

      // 检查是否为0字节文件
      if (file.size === 0) {
        ElMessage.error('音频文件为0字节，无法处理!');
        errorMessage.value = '音频文件为0字节，无法处理。请检查文件是否有效。';
        translationStatus.value = 'error';
        return false;
      }

      const isLt100M = file.size / 1024 / 1024 < 100;
      if (!isLt100M) {
        ElMessage.error('音频大小不能超过100MB!');
        return false;
      }
      
      // 文件验证通过，设置文件
      if (isAudio && isLt100M) {
        formState.sourceAudio = file;
        console.log('音频文件已选择:', file.name);
      }
      
      return false; // 阻止自动上传
    };

    // 模拟进度 - 仅作为后备机制，用于API未返回进度时提供视觉反馈
    const simulateProgress = (type) => {
      if (type === 'upload') {
        uploadProgress.value = 0;
        const timer = setInterval(() => {
          if (translationStatus.value !== 'uploading' || uploadProgress.value >= 90) {
            clearInterval(timer);
            return;
          }
          uploadProgress.value += Math.floor(Math.random() * 5) + 1;
        }, 200);
      } else if (type === 'transcription') {
        transcriptionProgress.value = 0;
        const timer = setInterval(() => {
          if (translationStatus.value !== 'transcribing' || transcriptionProgress.value >= 90) {
            clearInterval(timer);
            return;
          }
          transcriptionProgress.value += Math.floor(Math.random() * 3) + 1;
        }, 300);
      } else if (type === 'translation') {
        translationProgress.value = 0;
        const timer = setInterval(() => {
          if (translationStatus.value !== 'translating' || translationProgress.value >= 90) {
            clearInterval(timer);
            return;
          }
          translationProgress.value += Math.floor(Math.random() * 3) + 1;
        }, 300);
      }
    };

    // 定期检查翻译状态的函数
    const statusCheckInterval = ref(null);
    const errorMessage = ref('');  // 添加错误信息变量
    const processingTime = ref(0); // 添加处理时间计时器
    const processingStartTime = ref(null); // 处理开始时间
    const stuckDetector = ref(null); // 检测卡住的定时器
    const jobId = ref(''); // 添加job_id变量，用于状态查询
    const errorCount = ref(0); // 添加错误计数变量
    const warningShown = ref(false); // 添加警告显示标志

    const resetErrorState = () => {
      errorMessage.value = '';
      processingTime.value = 0;
      if (stuckDetector.value) {
        clearInterval(stuckDetector.value);
        stuckDetector.value = null;
      }
    };

    const checkForStuckProcess = () => {
      // 如果已经在后台模式，不启动卡住检测
      if (taskMode.value === 'background') {
        console.log('任务已在后台模式，不启动卡住检测');
        return;
      }
      
      // 检测处理是否卡住(不只检测高进度，也检测低进度长时间无变化)
      if (stuckDetector.value) {
        console.log('清除现有的卡住检测器');
        clearInterval(stuckDetector.value);
        stuckDetector.value = null;
      }
      
      // 记录当前进度和时间
      const startProgress = translationProgress.value;
      const startTime = Date.now();
      
      // 设置一个合理的检测间隔
      const checkInterval = 10000; // 10秒检查一次，减少资源消耗
      
      console.log('启动卡住检测器，初始进度:', startProgress);
      
      stuckDetector.value = setInterval(() => {
        // 如果任务模式变为后台，停止卡住检测
        if (taskMode.value === 'background') {
          console.log('检测到模式为后台，停止卡住检测');
          clearInterval(stuckDetector.value);
          stuckDetector.value = null;
          return;
        }
        
        const currentTime = Date.now();
        const elapsedSeconds = (currentTime - startTime) / 1000;
        
        // 更新总处理时间
        if (processingStartTime.value) {
          processingTime.value = Math.floor((currentTime - processingStartTime.value) / 1000);
        }
        
        // 设置最大检测时间为10分钟
        if (elapsedSeconds > 600) {
          console.log('卡住检测已运行10分钟，自动停止');
          clearInterval(stuckDetector.value);
          stuckDetector.value = null;
          return;
        }
        
        console.log('卡住检测中:', {
          progress: translationProgress.value,
          elapsedSeconds,
          status: translationStatus.value
        });
        
        // 检查进度是否停滞 - 同时考虑高进度和低进度情况
        if ((translationProgress.value === startProgress && elapsedSeconds > 60) ||
            (translationProgress.value < 10 && elapsedSeconds > 120) ||
            (translationProgress.value > 90 && elapsedSeconds > 90)) {
          // 进度长时间没有变化
          console.warn('检测到翻译进度可能卡住', {
            progress: translationProgress.value,
            elapsedSeconds,
            status: translationStatus.value
          });
          
          // 尝试更新状态或结束进程
          if (elapsedSeconds > 180) { // 3分钟无变化则检查最终状态
            // 如果超过3分钟没有变化，主动检查一次最终状态
            console.log('超过3分钟无变化，检查最终状态');
            checkFinalStatus();
            
            // 停止卡住检测，避免重复查询
            clearInterval(stuckDetector.value);
            stuckDetector.value = null;
          } else if (elapsedSeconds > 120 && translationProgress.value < 15) {
            // 如果是低进度超过2分钟无变化，显示提示信息
            if (!warningShown.value) {
              ElMessage.warning('处理音频可能需要较长时间，请耐心等待或移至后台处理');
              warningShown.value = true;
            }
          }
        }
      }, checkInterval);
    };

    const checkFinalStatus = async () => {
      if (!currentTranslationId.value) {
        console.warn('没有翻译ID，无法检查最终状态');
        return;
      }
      
      // 如果已经在后台模式，不执行前台状态检查
      if (taskMode.value === 'background') {
        console.log('任务已在后台模式，不执行前台最终状态检查');
        return;
      }
      
      console.log('执行最终状态检查, ID:', currentTranslationId.value);
      
      try {
        // 准备认证配置
        const config = {};
        const token = localStorage.getItem('token') || sessionStorage.getItem('token');
        
        if (token) {
          config.headers = {
            'Authorization': token.startsWith('Bearer ') ? token : `Bearer ${token}`
          };
        } else {
          config.headers = {
            'Authorization': 'Bearer test123'
          };
        }
        
        // 添加一个时间戳防止缓存
        const statusUrl = `/api/translation/status/${currentTranslationId.value}?finalCheck=true&_=${Date.now()}`;
        
        console.log('发送最终状态检查请求:', statusUrl);
        
        // 设置短超时时间，避免长时间等待
        config.timeout = 5000; // 5秒超时
        
        const response = await axios.get(statusUrl, config);
        
        console.log('最终状态检查结果:', response.data);
        
        // 检查是否有错误信息
        if (response.data.error_message) {
          errorMessage.value = response.data.error_message;
          translationStatus.value = 'error';
          
          // 确保停止所有检查
          if (statusCheckInterval.value) {
            clearInterval(statusCheckInterval.value);
            statusCheckInterval.value = null;
          }
          
          translating.value = false;
          return;
        }
        
        // 如果状态是 completed 但前端没有更新，手动更新
        if (response.data.status === 'completed') {
          console.log('检测到任务已完成，获取结果');
          
          try {
            const result = await getTranslationResult(currentTranslationId.value);
            
            if (result) {
              console.log('成功获取任务结果');
              
              // 设置结果数据
              transcript.value = result.source_text || '';
              translatedText.value = result.translated_text || '';
              detectedLanguage.value = result.detected_language || formState.sourceLanguage;
              
              // 处理其他数据...
              
              // 设置成功状态
              translationStatus.value = 'complete';
              translating.value = false;
              taskCompleted.value = true;
              
              // 确保停止所有检查
              if (statusCheckInterval.value) {
                clearInterval(statusCheckInterval.value);
                statusCheckInterval.value = null;
              }
              
              ElMessage.success('翻译完成！');
            }
          } catch (resultError) {
            console.error('获取最终结果失败:', resultError);
            // 设置一个错误消息但不中断流程
            errorMessage.value = '获取完整结果失败，请稍后在任务中心查看';
          }
        } else if (response.data.status === 'error') {
          // 处理错误状态
          translationStatus.value = 'error';
          errorMessage.value = response.data.error_message || '处理过程中发生未知错误';
          translating.value = false;
          
          // 确保停止所有检查
          if (statusCheckInterval.value) {
            clearInterval(statusCheckInterval.value);
            statusCheckInterval.value = null;
          }
          
          ElMessage.error('翻译失败：' + errorMessage.value);
        }
      } catch (error) {
        console.error('检查最终状态失败:', error);
        
        // 不设置错误状态，只记录日志，让常规检查继续
        console.log('将继续通过正常轮询获取状态');
      }
    };

    const startCheckingStatus = () => {
      // 防止重复启动状态检查
      if (statusCheckInterval.value) {
        console.log('已存在状态检查间隔，先清除');
        clearInterval(statusCheckInterval.value);
        statusCheckInterval.value = null;
      }
      
      // 如果任务已经在后台模式，则不启动前台轮询
      if (taskMode.value === 'background' && showTaskManager.value) {
        console.log('任务已在后台模式，不启动前台状态检查');
        return;
      }
      
      const idToCheck = jobId.value || currentTranslationId.value;
      
      if (!idToCheck) {
        console.warn('没有可检查的翻译ID');
        return;
      }
      
      // 记录开始处理时间
      if (!processingStartTime.value) {
        processingStartTime.value = Date.now();
      }
      
      console.log('启动前台状态检查，任务ID:', idToCheck);
      
      // 启动卡住检测
      checkForStuckProcess();
      
      // 每3秒检查一次翻译状态
      statusCheckInterval.value = setInterval(async () => {
        try {
          // 如果任务模式变为后台，停止前台检查
          if (taskMode.value === 'background') {
            console.log('检测到模式为后台，停止前台状态检查');
            clearInterval(statusCheckInterval.value);
            statusCheckInterval.value = null;
            return;
          }
          
          // 准备认证配置
          const config = {};
          const token = localStorage.getItem('token') || sessionStorage.getItem('token');
          
          if (token) {
            config.headers = {
              'Authorization': token.startsWith('Bearer ') ? token : `Bearer ${token}`
            };
          } else {
            // 如果没有令牌，添加测试令牌
            config.headers = {
              'Authorization': 'Bearer test123'
            };
          }
          
          // 在URL中添加auth_token参数作为备用
          let statusUrl = `/api/translation/status/${idToCheck}`;
          if (!token) {
            statusUrl += `?auth_token=test123`;
          }
          
          console.log(`检查翻译状态: ${statusUrl}`);
          
          // 添加请求标识符，以便在网络面板中区分前台和后台请求
          statusUrl += statusUrl.includes('?') ? '&' : '?';
          statusUrl += 'requestMode=foreground&_=' + Date.now();
          
          const response = await axios.get(statusUrl, config);
          errorCount.value = 0; // 重置错误计数
          
          const status = response.data.status;
          
          // 如果有错误信息，记录下来
          if (response.data.error_message) {
            errorMessage.value = response.data.error_message;
          }
          
          // 如果状态是error，立即处理错误
          if (status === 'error') {
            translationStatus.value = 'error';
            errorMessage.value = errorMessage.value || response.data.message || '处理过程中发生未知错误';
            clearInterval(statusCheckInterval.value);
            if (stuckDetector.value) {
              clearInterval(stuckDetector.value);
            }
            translating.value = false;
            ElMessage.error('翻译失败：' + (errorMessage.value || '未知错误'));
            return;
          }
          
          // 更新文本长度
          if (response.data.transcript_length !== undefined) {
            transcriptLength.value = response.data.transcript_length;
            
            // 如果文本长度为0且状态不是transcribing，显示明确提示
            if (transcriptLength.value === 0 && status !== 'transcribing' && status !== 'processing') {
              if (!errorMessage.value) {
                errorMessage.value = '无法识别音频内容，请检查音频质量或尝试重新录制';
              }
            }
          }
          
          // 更新状态和进度
          if (status === 'processing') {
            translationStatus.value = 'transcribing';
            transcriptionProgress.value = response.data.progress || 5;
          } else if (status === 'transcribing') {
            translationStatus.value = 'transcribing';
            transcriptionProgress.value = response.data.progress || 50;
          } else if (status === 'translating') {
            translationStatus.value = 'translating';
            translationProgress.value = response.data.progress || 50;
          } else if (status === 'generating_audio') {
            translationStatus.value = 'translating';
            translationProgress.value = response.data.progress || 90; // 接近完成
          } else if (status === 'completed') {
            // 翻译完成，获取翻译结果
            try {
              const result = await getTranslationResult(currentTranslationId.value);
              
              if (result) {
                // 设置结果数据
                transcript.value = result.source_text || '';
                translatedText.value = result.translated_text || '';
                detectedLanguage.value = result.detected_language || formState.sourceLanguage;
                
                // 处理时间轴段落
                if (result.segments) {
                  try {
                    // 解析segments数据
                    let parsedSegments = typeof result.segments === 'string' 
                      ? JSON.parse(result.segments) 
                      : result.segments;
                    
                    // 确保每个segment有正确的startTime和endTime字段
                    segments.value = parsedSegments.map(segment => {
                      // 标准化字段名称
                      return {
                        startTime: segment.startTime || segment.start || 0,
                        endTime: segment.endTime || segment.end || 0,
                        text: segment.text || '',
                        translatedText: segment.translatedText || ''
                      };
                    });
                    
                    // 如果segments中的translatedText都为空，但有整体翻译文本，尝试分配翻译
                    if (translatedText.value && segments.value.every(segment => !segment.translatedText)) {
                      // 尝试使用简单算法将整体翻译文本分配到各个segments
                      const sentences = translatedText.value.match(/[^.!?。！？]+[.!?。！？]+/g) || [translatedText.value];
                      
                      // 如果翻译句子数量与segments相同或接近，按顺序分配
                      if (Math.abs(sentences.length - segments.value.length) <= 2) {
                        segments.value.forEach((segment, index) => {
                          if (index < sentences.length) {
                            segment.translatedText = sentences[index].trim();
                          }
                        });
                      } 
                      // 否则根据文本长度比例分配
                      else {
                        const totalOriginalLength = segments.value.reduce((sum, seg) => sum + (seg.text?.length || 0), 0);
                        const totalTranslatedLength = translatedText.value.length;
                        
                        // 确保原文有长度
                        if (totalOriginalLength > 0) {
                          let startPos = 0;
                          
                          segments.value.forEach(segment => {
                            const segmentRatio = (segment.text?.length || 0) / totalOriginalLength;
                            const approxTranslatedLength = Math.floor(segmentRatio * totalTranslatedLength);
                            
                            // 确保不超出范围
                            const endPos = Math.min(startPos + approxTranslatedLength, totalTranslatedLength);
                            segment.translatedText = translatedText.value.substring(startPos, endPos).trim();
                            startPos = endPos;
                          });
                        }
                      }
                    }
                  } catch (e) {
                    console.error('解析segments失败:', e);
                    generateSegments();
                  }
                } else {
                  generateSegments();
                }
                
                // 设置音频URL
                translatedAudioUrl.value = getAudioFile(currentTranslationId.value, 'translated');
                
                // 检查识别结果是否为空
                if (transcript.value && (transcript.value.includes('[无法识别的') || transcript.value.includes('[音频识别失败'))) {
                  translationStatus.value = 'error';
                  errorMessage.value = '无法识别音频内容，请检查音频质量或尝试重新录制';
                  ElMessage.warning('音频识别失败：' + errorMessage.value);
                } else if (!transcript.value || transcript.value.trim() === '') {
                  translationStatus.value = 'error';
                  errorMessage.value = '识别结果为空文本，请检查音频质量或尝试重新录制';
                  ElMessage.warning('音频识别结果为空：' + errorMessage.value);
                } else {
                  translationStatus.value = 'complete';
                  ElMessage.success('翻译完成！');
                }
              }
            } catch (resultError) {
              console.error('获取翻译结果失败:', resultError);
              errorMessage.value = '获取翻译结果失败';
              translationStatus.value = 'error';
            }
            
            clearInterval(statusCheckInterval.value);
            if (stuckDetector.value) {
              clearInterval(stuckDetector.value);
            }
            translating.value = false;
          }
        } catch (error) {
          console.error('检查翻译状态失败:', error);
          // 如果检查失败超过5次，显示错误
          errorCount.value++;
          if (errorCount.value > 5) {
            translationStatus.value = 'error';
            errorMessage.value = '无法获取翻译状态，请检查网络连接';
            clearInterval(statusCheckInterval.value);
            if (stuckDetector.value) {
              clearInterval(stuckDetector.value);
            }
            translating.value = false;
          }
        }
      }, 3000);
    };

    // 获取语言名称
    const getLanguageName = (code) => {
      return getLangName(code);
    };

    // 格式化时间
    const formatTime = (seconds) => {
      if (seconds === undefined || seconds === null || isNaN(seconds)) {
        return "0:00";
      }
      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${mins}:${secs.toString().padStart(2, '0')}`;
    };

    // 复制文本
    const copyText = (text) => {
      navigator.clipboard.writeText(text).then(() => {
        ElMessage.success('文本已复制到剪贴板');
      }).catch(() => {
        ElMessage.error('复制失败，请手动复制');
      });
    };

    // 下载音频
    const downloadAudio = () => {
      if (translatedAudioUrl.value) {
        const a = document.createElement('a');
        a.href = translatedAudioUrl.value;
        a.download = `翻译_${new Date().getTime()}.${formState.outputFormat}`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        ElMessage.success('开始下载音频');
      } else {
        ElMessage.warning('音频文件不存在');
      }
    };

    // 修改保存到历史函数
    const saveToHistory = async () => {
      if (currentTranslationId.value) {
        try {
          // 添加本地令牌状态检查
          const token = localStorage.getItem('token') || sessionStorage.getItem('token');
          if (!token) {
            console.warn('保存历史记录时未找到认证令牌，将使用后备测试令牌');
          }
          
          await saveTranslationToHistory(currentTranslationId.value);
          ElMessage.success('已保存到翻译历史记录');
        } catch (error) {
          console.error('保存历史记录详细错误:', error);
          
          // 检查是否是认证错误
          if (error.response && error.response.status === 401) {
            ElMessage.error('保存失败：认证失败，请尝试登录后再保存');
          } else {
            ElMessage.error('保存失败：' + (error.message || '未知错误'));
          }
        }
      } else {
        ElMessage.warning('无法保存，翻译ID不存在');
      }
    };

    // 分享翻译
    const shareTranslation = () => {
      ElMessage.success('已生成分享链接');
    };

    // 重置表单
    const resetForm = () => {
      formState.sourceAudio = null;
      formState.sourceLanguage = 'zh';
      formState.targetLanguage = 'en';
      formState.keepOriginal = false;
      formState.quality = 'standard';
      formState.domain = '';
      formState.outputFormat = 'mp3';
      
      audioList.value = [];
      translationStatus.value = 'empty';
      translating.value = false;
      
      // 重置错误状态
      resetErrorState();
      
      // 重置结果
      transcript.value = '';
      translatedText.value = '';
      detectedLanguage.value = '';
      transcriptLength.value = 0;
      translatedAudioUrl.value = '';
      currentTranslationId.value = '';
      jobId.value = '';
      
      // 清除定时器
      if (statusCheckInterval.value) {
        clearInterval(statusCheckInterval.value);
      }
    };

    // 处理音频变更事件
    const handleAudioChange = (info) => {
      const file = info.file;
      
      // 检查是否有文件
      if (!file) {
        audioList.value = [];
        return;
      }
      
      // 检查文件大小是否为0
      if (file.size === 0) {
        ElMessage.error('检测到0字节音频文件，无法处理');
        errorMessage.value = '音频文件为0字节，无法处理。请检查文件是否有效或已损坏。';
        translationStatus.value = 'error';
        return;
      }
      
      // 更新文件列表
      if (info.file.status !== 'removed') {
        formState.sourceAudio = file;
        console.log('音频文件已选择:', file.name);
        ElMessage.success(`音频文件 "${file.name}" 已选择，可以开始翻译`);
      } else {
        formState.sourceAudio = null;
        translationStatus.value = 'empty';
      }
    };

    // 生成时间轴段落
    const generateSegments = () => {
      const text = transcript.value;
      const translatedTextValue = translatedText.value;
      
      // 如果没有文本，返回空数组
      if (!text) {
        segments.value = [];
        return;
      }
      
      // 使用标点符号分割句子
      const sentences = text.match(/[^.!?。！？]+[.!?。！？]+/g) || [text];
      
      // 如果有翻译文本，尝试按相同方式分割译文
      let translatedSentences = [];
      if (translatedTextValue) {
        translatedSentences = translatedTextValue.match(/[^.!?。！？]+[.!?。！？]+/g) || [translatedTextValue];
      }
      
      let currentTime = 0;
      segments.value = sentences.map((sentence, index) => {
        const duration = sentence.length * 0.1; // 简单估算持续时间
        
        // 获取对应的翻译句子，如果没有对应句子，使用空字符串或整个翻译
        let translated = "";
        if (translatedSentences.length > 0) {
          // 如果翻译的句子数量匹配或更多，使用对应索引的句子
          if (index < translatedSentences.length) {
            translated = translatedSentences[index];
          } else {
            // 否则使用最后一个翻译句子
            translated = translatedSentences[translatedSentences.length - 1];
          }
        } else if (translatedTextValue) {
          // 如果无法分割翻译文本但有整体翻译，使用整体翻译
          translated = translatedTextValue;
        }
        
        const segment = {
          startTime: currentTime,
          endTime: currentTime + duration,
          text: sentence.trim(),
          translatedText: translated.trim()
        };
        currentTime += duration;
        return segment;
      });
    };

    // 开始翻译
    const startTranslation = async () => {
      try {
        await formRef.value.validate();
        
        if (!formState.sourceAudio) {
          ElMessage.error('请先上传音频文件');
          return;
        }
        
        // 重置所有状态变量
        translating.value = true;
        translationStatus.value = 'uploading';
        uploadProgress.value = 0;
        transcriptionProgress.value = 0;
        translationProgress.value = 0;
        errorMessage.value = '';
        transcript.value = '';
        translatedText.value = '';
        segments.value = [];
        processingTime.value = 0;
        processingStartTime.value = null;
        warningShown.value = false;
        taskCompleted.value = false;
        showTaskManager.value = false; // 确保开始时不显示任务管理器
        
        // 如果存在先前的检测器，清除它们
        if (statusCheckInterval.value) {
          clearInterval(statusCheckInterval.value);
          statusCheckInterval.value = null;
        }
        if (stuckDetector.value) {
          clearInterval(stuckDetector.value);
          stuckDetector.value = null;
        }
        
        // 准备表单数据
        const formData = new FormData();
        formData.append('audio_file', audioList.value[0].originFileObj);
        formData.append('target_language', formState.targetLanguage);
        
        if (formState.sourceLanguage) {
          formData.append('source_language', formState.sourceLanguage);
        }
        
        if (formState.quality) {
          formData.append('quality', formState.quality);
        }
        
        if (formState.domain) {
          formData.append('domain', formState.domain);
        }
        
        if (formState.outputFormat) {
          formData.append('output_format', formState.outputFormat);
        }
        
        formData.append('keep_original', formState.keepOriginal ? 'true' : 'false');
        
        // 添加任务类型标记
        formData.append('task_type', 'audio_translation');
        
        console.log('开始上传音频文件');
        
        // 发送请求
        const response = await axios.post('/api/translation/audio', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: (progressEvent) => {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            uploadProgress.value = percentCompleted;
          }
        });
        
        // 获取翻译ID
        currentTranslationId.value = response.data.task_id;
        
        ElMessage.success('音频文件上传成功，开始处理');
        
        // 设置初始状态为前台处理，直接开始检查状态
        taskMode.value = 'foreground';
        startCheckingStatus();

        // 翻译状态更新为转写阶段
        translationStatus.value = 'transcribing';
        
      } catch (error) {
        // 其他错误处理
        translationStatus.value = 'error';
        translating.value = false;
        console.error('Translation error:', error);
        errorMessage.value = error.message || '未知错误';
        ElMessage.error('翻译失败：' + errorMessage.value);
      }
    };

    // 添加数据
    const micTesting = ref(false);
    const micResult = ref('');
    const audioFileInfo = ref(null);
    const showFormatConverterGuide = ref(false);
    const audioQualityInfo = ref(null);
    const loading = ref(false);

    // 测试麦克风
    const testMicrophone = async () => {
      try {
        micTesting.value = true;
        micResult.value = '正在请求麦克风权限...';
        
        // 请求麦克风权限
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        
        // 创建音频上下文
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const analyser = audioContext.createAnalyser();
        const microphone = audioContext.createMediaStreamSource(stream);
        microphone.connect(analyser);
        
        analyser.fftSize = 256;
        const bufferLength = analyser.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);
        
        // 监听音量
        let soundDetected = false;
        let maxVolume = 0;
        
        const checkSound = () => {
          analyser.getByteFrequencyData(dataArray);
          let sum = 0;
          for (let i = 0; i < bufferLength; i++) {
            sum += dataArray[i];
          }
          const average = sum / bufferLength;
          maxVolume = Math.max(maxVolume, average);
          
          if (average > 10) {
            soundDetected = true;
          }
          
          micResult.value = `正在监听...(音量: ${Math.round(average)})`;
          
          if (soundDetected) {
            micResult.value = `检测到声音！音量水平: ${Math.round(maxVolume)}`;
          }
        };
        
        // 监听5秒
        const checkInterval = setInterval(checkSound, 100);
        
        setTimeout(() => {
          clearInterval(checkInterval);
          stream.getTracks().forEach(track => track.stop());
          
          if (soundDetected) {
            if (maxVolume < 20) {
              micResult.value = '检测到声音，但音量非常低。建议调高麦克风音量或靠近麦克风说话。';
            } else if (maxVolume < 50) {
              micResult.value = '检测到声音，音量适中。应该可以正常使用。';
            } else {
              micResult.value = '检测到声音，音量良好。应该可以正常使用。';
            }
          } else {
            micResult.value = '未检测到声音，请检查麦克风是否正常工作或系统设置是否允许麦克风使用。';
          }
          
          micTesting.value = false;
        }, 5000);
        
      } catch (error) {
        micResult.value = `麦克风测试失败: ${error.message}`;
        micTesting.value = false;
        console.error('麦克风测试失败:', error);
      }
    };

    // 分析音频文件
    const analyzeAudioFile = () => {
      const file = formState.sourceAudio;
      if (!file) {
        ElMessage.warning('请先上传音频文件');
        return;
      }
      
      const fileInfo = {
        name: file.name,
        size: (file.size / 1024).toFixed(2) + ' KB',
        type: file.type || '未知类型',
        result: ''
      };
      
      // 检查文件类型
      if (!file.type.startsWith('audio/')) {
        fileInfo.result = '警告：文件类型似乎不是音频文件，这可能导致识别失败。';
      } else if (file.size < 1024) {
        fileInfo.result = '警告：文件太小，可能是空文件或损坏文件。';
      } else if (file.size > 50 * 1024 * 1024) {
        fileInfo.result = '警告：文件较大，超过50MB，可能会导致处理时间较长。';
      } else {
        fileInfo.result = '文件格式和大小正常，应该可以进行识别。';
        
        // 尝试读取音频元数据
        try {
          const url = URL.createObjectURL(file);
          const audio = new Audio();
          audio.src = url;
          
          audio.addEventListener('loadedmetadata', () => {
            if (audio.duration < 0.5) {
              fileInfo.result = '警告：音频时长很短，可能没有有效内容。';
            } else {
              fileInfo.result += ` 音频时长: ${audio.duration.toFixed(2)}秒`;
            }
            audioFileInfo.value = fileInfo;
            URL.revokeObjectURL(url);
          });
          
          audio.addEventListener('error', () => {
            fileInfo.result = '警告：无法读取音频元数据，文件可能已损坏。';
            audioFileInfo.value = fileInfo;
            URL.revokeObjectURL(url);
          });
          
          // 超时处理
          setTimeout(() => {
            if (!audioFileInfo.value || !audioFileInfo.value.result.includes('音频时长')) {
              fileInfo.result += ' (无法读取完整音频信息)';
              audioFileInfo.value = fileInfo;
              URL.revokeObjectURL(url);
            }
          }, 3000);
          
        } catch (e) {
          fileInfo.result = `无法分析音频内容: ${e.message}`;
        }
      }
      
      audioFileInfo.value = fileInfo;
    };

    // 分析音频质量
    const analyzeAudioQuality = (file) => {
      if (!file) {
        audioQualityInfo.value = null
        return
      }
      
      loading.value = true
      errorMessage.value = ''
      
      const formData = new FormData()
      formData.append('audio_file', file)
      
      axios.post('/api/translation/analyze_audio', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      .then(response => {
        audioQualityInfo.value = response.data
        
        // 显示分析结果
        if (audioQualityInfo.value.status === 'success') {
          if (audioQualityInfo.value.is_recognizable) {
            errorMessage.value = `音频质量评分: ${audioQualityInfo.value.quality_score}/10 - 可以进行识别`
          } else {
            errorMessage.value = `音频质量评分: ${audioQualityInfo.value.quality_score}/10 - 识别可能有困难`
          }
          
          // 如果有建议，显示在控制台
          if (audioQualityInfo.value.recommendations && audioQualityInfo.value.recommendations.length > 0) {
            console.log('音频改进建议:', audioQualityInfo.value.recommendations)
          }
        } else {
          errorMessage.value = '音频分析失败: ' + (audioQualityInfo.value.error_details || '未知错误')
        }
      })
      .catch(err => {
        console.error('音频分析错误:', err)
        errorMessage.value = '音频分析服务出错，请稍后重试'
      })
      .finally(() => {
        loading.value = false
      })
    };

    // 测试音频API连接
    const testAudioAPI = async () => {
      try {
        loading.value = true;
        errorMessage.value = '正在测试与后端API的连接...';
        
        // 准备认证配置
        const config = {};
        const token = localStorage.getItem('token') || sessionStorage.getItem('token');
        
        if (token) {
          config.headers = {
            'Authorization': token.startsWith('Bearer ') ? token : `Bearer ${token}`
          };
          console.log('使用存储的认证令牌测试API');
        } else {
          // 如果没有令牌，添加测试令牌
          config.headers = {
            'Authorization': 'Bearer test123'
          };
          console.log('使用测试令牌测试API');
        }
        
        // 在URL中添加auth_token参数作为备用
        let testUrl = '/api/translation/test-services';
        if (!token) {
          testUrl += '?auth_token=test123';
        }
        
        // 测试API连接
        console.log(`测试API连接: ${testUrl}`);
        const response = await axios.get(testUrl, config);
        console.log('API测试结果:', response.data);
        
        if (response.data && response.data.status === 'ok') {
          ElMessage.success('与后端音频翻译服务连接正常!');
          errorMessage.value = '后端服务连接正常，可以使用音频翻译功能';
        } else {
          ElMessage.warning('后端服务响应异常，详情请查看控制台');
          errorMessage.value = '后端服务连接测试完成，但响应数据异常';
        }
      } catch (error) {
        console.error('API连接测试失败:', error);
        ElMessage.error('无法连接到后端音频翻译服务');
        errorMessage.value = '无法连接到后端音频翻译服务，请检查服务器是否运行正常';
        
        // 显示详细错误信息
        if (error.response) {
          console.error('错误状态码:', error.response.status);
          console.error('错误数据:', error.response.data);
          
          // 401错误特殊处理
          if (error.response.status === 401) {
            errorMessage.value = '认证失败：请先登录系统或联系管理员获取访问权限';
          }
        }
      } finally {
        loading.value = false;
      }
    };

    // 处理音频加载失败
    const handleAudioLoadError = (event) => {
      console.error('音频加载失败:', event);
      message.error('翻译后的音频加载失败，可能尚未生成或生成过程中出现错误');
      
      // 尝试再次获取音频文件状态
      checkFinalStatus();
    };

    // 移动到后台处理
    const moveToBackground = () => {
      // 如果没有任务ID，无法进行后台处理
      if (!currentTranslationId.value) {
        message.error('无法移至后台：任务ID不存在');
        return;
      }
      
      console.log('移至后台处理开始执行', {
        taskId: currentTranslationId.value,
        hasStatusInterval: !!statusCheckInterval.value,
        hasStuckDetector: !!stuckDetector.value
      });
      
      // 在清除任何服务前显示转换中状态
      message.loading('正在切换到后台模式...', 1);
      
      // 确保停止所有前台轮询和检测器
      if (statusCheckInterval.value) {
        console.log('清除前台状态检查间隔');
        clearInterval(statusCheckInterval.value);
        statusCheckInterval.value = null;
      }
      
      if (stuckDetector.value) {
        console.log('清除卡住检测器');
        clearInterval(stuckDetector.value);
        stuckDetector.value = null;
      }
      
      // 确保不会有其他的定时器或轮询
      // 尝试清除所有可能的间隔
      try {
        for (let i = 0; i < 1000; i++) {
          clearInterval(i);
        }
      } catch (e) {
        console.log('清除所有间隔时出错', e);
      }
      
      // 设置任务模式为后台
      taskMode.value = 'background';
      
      // 显示任务管理器组件前的准备工作
      const prepareTaskManager = () => {
        // 记录当前状态，以便任务管理器初始化
        const currentStatus = translationStatus.value;
        const currentProgress = 
          currentStatus === 'transcribing' ? transcriptionProgress.value : 
          currentStatus === 'translating' ? translationProgress.value : 0;
        
        // 存储任务状态信息，以便传递给任务管理器
        localStorage.setItem(`task_${currentTranslationId.value}_status`, currentStatus);
        localStorage.setItem(`task_${currentTranslationId.value}_progress`, currentProgress.toString());
        
        // 显示任务管理器组件
        showTaskManager.value = true;
        translating.value = true; // 维持翻译中状态
        
        // 确认显示并通知用户
        setTimeout(() => {
          if (showTaskManager.value) {
            message.success('已切换到后台模式，可以继续其他工作');
            
            // 提示用户如何回到任务
            setTimeout(() => {
              message.info('任务将在后台继续处理，您可以稍后在任务中心查看结果');
            }, 2000);
          }
        }, 500);
      };
      
      // 重置状态，以防重复请求
      processingStartTime.value = null;
      
      // 延时执行，确保UI有足够时间响应
      setTimeout(() => {
        prepareTaskManager();
        
        console.log('任务已移至后台处理', {
          taskId: currentTranslationId.value,
          mode: taskMode.value,
          showManager: showTaskManager.value
        });
      }, 300);
    };

    // 处理任务完成事件
    const handleTaskCompleted = (event) => {
      console.log('任务完成', event);
      taskCompleted.value = true;
      
      // 设置翻译状态为完成
      translationStatus.value = 'complete';
      translating.value = false;
      
      // 尝试获取结果
      if (event && event.result) {
        // 如果结果中包含翻译数据，更新UI
        if (event.result.source_text) {
          transcript.value = event.result.source_text;
        }
        
        if (event.result.translated_text) {
          translatedText.value = event.result.translated_text;
        }
        
        if (event.result.audioUrl) {
          translatedAudioUrl.value = event.result.audioUrl;
        } else if (currentTranslationId.value) {
          // 如果结果中没有音频URL，尝试从API获取
          translatedAudioUrl.value = getAudioFile(currentTranslationId.value, 'translated');
        }
        
        // 检查翻译结果是否完整
        const checkTranslationCompleteness = () => {
          console.log('检查翻译完整性');
          // 如果我们有分段但没有翻译，或翻译明显不完整，尝试重新获取或补充翻译
          if (event.result.segments && event.result.segments.length > 0) {
            const segmentTexts = Array.isArray(event.result.segments) 
              ? event.result.segments.map(s => s.text || '')
              : typeof event.result.segments === 'string'
                ? JSON.parse(event.result.segments).map(s => s.text || '')
                : [];
                
            const totalSegments = segmentTexts.length;
            // 估计翻译应该有的长度
            const estimatedTranslationLength = translatedText.value.length;
            const averageSegmentLength = estimatedTranslationLength / totalSegments;
            
            console.log('翻译完整性分析:', {
              totalSegments,
              estimatedLength: estimatedTranslationLength,
              averageSegmentLength,
              actualTranslation: translatedText.value
            });
            
            // 如果翻译看起来不完整 (估计每段至少30个字符)
            if (estimatedTranslationLength / totalSegments < 30 && totalSegments > 2) {
              console.warn('检测到翻译可能不完整');
              
              // 显示警告，但继续处理
              message.warning('翻译结果可能不完整，部分内容可能缺失');
              
              // 标记UI显示有缺失
              errorMessage.value = '翻译结果可能不完整，部分段落缺少翻译';
            }
          }
        };
        
        // 运行翻译完整性检查
        checkTranslationCompleteness();
        
        // 如果结果包含segments，更新时间轴
        if (event.result.segments) {
          try {
            // 解析segments数据
            let parsedSegments = Array.isArray(event.result.segments) 
              ? event.result.segments 
              : JSON.parse(event.result.segments);
            
            console.log('处理翻译分段', parsedSegments);
            
            // 改进分段翻译处理
            const improvedSegmentHandling = () => {
              // 如果只有一个翻译段落但有多个原文段落，尝试智能拆分
              if (translatedText.value && parsedSegments.length > 1) {
                // 首先尝试通过标点符号分割翻译文本
                const translationSentences = translatedText.value.match(/[^.!?。！？]+[.!?。！？]+/g) || [translatedText.value];
                
                // 如果分割后的句子数接近段落数，按顺序分配
                if (Math.abs(translationSentences.length - parsedSegments.length) <= 1) {
                  console.log('通过句子分割分配翻译', translationSentences);
                  parsedSegments.forEach((segment, index) => {
                    if (index < translationSentences.length) {
                      segment.translatedText = translationSentences[index].trim();
                    } else {
                      // 对于没有对应翻译的段落，添加提示
                      segment.translatedText = '[翻译缺失]';
                    }
                  });
                } else {
                  // 对于翻译分段与原始分段数量差异较大的情况
                  console.log('使用基于位置估计的翻译分配');
                  
                  // 计算更精确的比例分配
                  // 如果前N段有对应翻译但后面的段落没有，说明翻译可能不完整
                  // 假设前两段有翻译
                  if (parsedSegments.length > 2 && translatedText.value) {
                    // 估计前两段原文在整个文本中的占比
                    const firstTwoSegmentsText = parsedSegments.slice(0, 2).map(s => s.text || '').join(' ');
                    const firstTwoSegmentsRatio = firstTwoSegmentsText.length / 
                      parsedSegments.map(s => s.text || '').join(' ').length;
                    
                    // 如果翻译文本长度与前两段原文比例接近，可能只翻译了前两段
                    if (firstTwoSegmentsRatio > 0.3) {
                      console.warn('检测到可能只翻译了前两段');
                      
                      // 为前两段分配翻译，其余标记为缺失
                      const avgTranslatedLength = translatedText.value.length / 2;
                      
                      parsedSegments.forEach((segment, index) => {
                        if (index < 2) {
                          // 前两段分别分配翻译的前半部分和后半部分
                          segment.translatedText = index === 0
                            ? translatedText.value.substring(0, Math.floor(avgTranslatedLength)).trim()
                            : translatedText.value.substring(Math.floor(avgTranslatedLength)).trim();
                        } else {
                          // 后面的段落标记为缺失翻译
                          segment.translatedText = '[翻译缺失 - 请联系技术支持]';
                        }
                      });
                    } else {
                      // 否则采用均匀分配策略
                      const avgLength = translatedText.value.length / parsedSegments.length;
                      
                      parsedSegments.forEach((segment, index) => {
                        const start = Math.round(index * avgLength);
                        const end = Math.round((index + 1) * avgLength);
                        segment.translatedText = translatedText.value.substring(start, end).trim() || '[翻译缺失]';
                      });
                    }
                  }
                }
              }
              
              return parsedSegments;
            };
            
            // 应用改进的分段处理
            const updatedSegments = improvedSegmentHandling();
            
            // 标准化字段名称
            segments.value = updatedSegments.map(segment => {
              return {
                startTime: segment.startTime || segment.start || 0,
                endTime: segment.endTime || segment.end || 0,
                text: segment.text || '',
                translatedText: segment.translatedText || ''
              };
            });
            
          } catch (e) {
            console.error('解析时间轴数据失败', e);
            // 如果解析失败，尝试重新生成
            generateSegmentsWithTranslation();
          }
        } else {
          // 如果没有segments数据，生成
          generateSegmentsWithTranslation();
        }
      } else {
        // 如果没有结果对象，尝试手动获取结果
        getTranslationResult(currentTranslationId.value).then(result => {
          if (result) {
            transcript.value = result.source_text || '';
            translatedText.value = result.translated_text || '';
            
            // 如果有segments数据，处理
            if (result.segments) {
              try {
                let parsedSegments = typeof result.segments === 'string' 
                  ? JSON.parse(result.segments) 
                  : result.segments;
                
                segments.value = parsedSegments.map(segment => ({
                  startTime: segment.startTime || segment.start || 0,
                  endTime: segment.endTime || segment.end || 0,
                  text: segment.text || '',
                  translatedText: segment.translatedText || ''
                }));
                
                // 如果segments中没有翻译文本，尝试分配
                if (translatedText.value && segments.value.every(segment => !segment.translatedText)) {
                  assignTranslationsToAllSegments();
                }
              } catch (e) {
                console.error('解析segments失败:', e);
                generateSegmentsWithTranslation();
              }
            } else {
              generateSegmentsWithTranslation();
            }
          }
        }).catch(err => {
          console.error('获取结果失败', err);
        });
      }
      
      message.success('翻译任务已完成！');
    };
    
    // 处理任务失败事件
    const handleTaskFailed = (event) => {
      console.log('任务失败', event);
      
      translationStatus.value = 'error';
      translating.value = false;
      errorMessage.value = event.error || '处理过程中发生未知错误';
      
      message.error('翻译失败：' + errorMessage.value);
    };
    
    // 处理任务取消事件
    const handleTaskCanceled = () => {
      console.log('任务已取消');
      
      translationStatus.value = 'error';
      translating.value = false;
      errorMessage.value = '任务已被取消';
      
      message.info('翻译任务已取消');
    };
    
    // 处理模式变化事件
    const handleModeChanged = (mode) => {
      console.log('任务模式已变更为', mode);
      
      // 如果当前已经是此模式，无需处理
      if (taskMode.value === mode) {
        console.log('任务已经是该模式，忽略变更');
        return;
      }
      
      // 更新模式状态
      taskMode.value = mode;
      
      if (mode === 'foreground') {
        console.log('切换回前台模式');
        
        // 确保没有进行中的状态检查
        if (statusCheckInterval.value) {
          console.log('清除现有的状态检查间隔');
          clearInterval(statusCheckInterval.value);
          statusCheckInterval.value = null;
        }
        
        // 切换回前台模式，重新开始状态检查
        setTimeout(() => {
          console.log('延迟启动前台状态检查');
          startCheckingStatus();
        }, 500);
        
      } else if (mode === 'background') {
        console.log('切换至后台模式');
        
        // 停止所有前台检查
        if (statusCheckInterval.value) {
          console.log('停止所有前台状态检查');
          clearInterval(statusCheckInterval.value);
          statusCheckInterval.value = null;
        }
        
        if (stuckDetector.value) {
          console.log('停止所有卡住检测');
          clearInterval(stuckDetector.value);
          stuckDetector.value = null;
        }
      }
    };
    
    // 处理添加到通知事件
    const handleAddToNotifications = () => {
      console.log('添加到通知');
      // 可以调用通知服务
    };
    
    // 处理导航到任务中心事件
    const navigateToTaskCenter = () => {
      console.log('导航到任务中心');
      // 使用setup中已经获取的router实例
      if (router) {
        router.push('/tasks');
      } else {
        // 如果router不可用，使用window.location导航
        window.location.href = '/tasks';
      }
    };
    
    // 处理任务关闭事件
    const handleTaskClosed = () => {
      console.log('任务管理器已关闭，当前状态:', {
        taskCompleted: taskCompleted.value,
        taskMode: taskMode.value,
        translationStatus: translationStatus.value
      });
      
      // 隐藏任务管理器
      showTaskManager.value = false;
      
      // 确保没有遗留的定时器
      if (statusCheckInterval.value) {
        console.log('清除遗留的状态检查间隔');
        clearInterval(statusCheckInterval.value);
        statusCheckInterval.value = null;
      }
      
      if (stuckDetector.value) {
        console.log('清除遗留的卡住检测器');
        clearInterval(stuckDetector.value);
        stuckDetector.value = null;
      }
      
      // 如果任务已完成，显示结果
      if (taskCompleted.value) {
        translationStatus.value = 'complete';
        translating.value = false;
        console.log('显示已完成的任务结果');
      } else {
        console.log('任务尚未完成，但用户关闭了任务管理器');
        // 如果用户关闭了后台管理器但任务尚未完成，提示用户
        message.info('任务已移至后台，您可以稍后在任务中心查看结果');
        
        // 重置页面状态
        translationStatus.value = 'empty';
        translating.value = false;
        
        // 不重新开始状态检查，而是完全转入后台
        console.log('完全转入后台，不再进行前台轮询');
      }
    };

    // 分配翻译给所有段落
    const assignTranslationsToAllSegments = () => {
      if (!translatedText.value || !segments.value || segments.value.length === 0) {
        return;
      }

      // 尝试使用不同方法进行分配
      // 方法1：如果原文句子数和译文句子数大致相同，按顺序匹配
      const sourceSegments = segments.value.map(segment => segment.text.trim());
      const translatedSegments = assignTranslationToSegments(sourceSegments, translatedText.value);
      
      // 更新每个段落的翻译
      segments.value.forEach((segment, index) => {
        if (index < translatedSegments.length) {
          segment.translatedText = translatedSegments[index];
        }
      });
    };

    // 智能分配翻译文本到源文本段落
    const assignTranslationToSegments = (sourceSegments, translatedFullText) => {
      if (!sourceSegments || !translatedFullText) {
        return [];
      }

      // 尝试基于标点符号分割译文
      const translatedSentences = translatedFullText.match(/[^.!?。！？]+[.!?。！？]+/g) || [translatedFullText];
      
      // 如果译文段落数量与源文段落数量接近，直接按顺序匹配
      if (Math.abs(translatedSentences.length - sourceSegments.length) <= 2) {
        return translatedSentences.map(sentence => sentence.trim());
      }
      
      // 方法2：基于文本长度比例分配
      const totalSourceLength = sourceSegments.reduce((sum, text) => sum + text.length, 0);
      const totalTranslatedLength = translatedFullText.length;
      
      if (totalSourceLength === 0) {
        return sourceSegments.map(() => '');
      }
      
      const result = [];
      let startPos = 0;
      
      for (const segment of sourceSegments) {
        const segmentRatio = segment.length / totalSourceLength;
        const approxTranslatedLength = Math.floor(segmentRatio * totalTranslatedLength);
        
        // 确保不超出范围
        const endPos = Math.min(startPos + approxTranslatedLength, totalTranslatedLength);
        const translatedSegment = translatedFullText.substring(startPos, endPos).trim();
        
        // 尝试调整到最近的句子结束点
        const adjustedTranslation = adjustToSentenceEnd(translatedSegment, translatedFullText, startPos, endPos);
        
        result.push(adjustedTranslation);
        startPos = endPos;
      }
      
      return result;
    };
    
    // 将文本调整到最近的句子结束位置
    const adjustToSentenceEnd = (text, fullText, startPos, endPos) => {
      // 如果分段位置恰好是句子结尾，无需调整
      if (endPos >= fullText.length || /[.!?。！？]$/.test(text)) {
        return text;
      }
      
      // 向前寻找最近的句子结束点
      let nextEndPos = endPos;
      for (let i = endPos; i < Math.min(endPos + 30, fullText.length); i++) {
        if (/[.!?。！？]/.test(fullText[i])) {
          nextEndPos = i + 1;
          break;
        }
      }
      
      // 如果找到了更好的结束点，使用它
      if (nextEndPos > endPos) {
        return fullText.substring(startPos, nextEndPos).trim();
      }
      
      return text;
    };
    
    // 使用翻译生成时间轴段落
    const generateSegmentsWithTranslation = () => {
      // 如果没有文本，返回空数组
      if (!transcript.value) {
        segments.value = [];
        return;
      }
      
      // 使用标点符号分割句子
      const sentences = transcript.value.match(/[^.!?。！？]+[.!?。！？]+/g) || [transcript.value];
      
      // 为这些句子分配翻译
      const translatedSentences = assignTranslationToSegments(sentences, translatedText.value || '');
      
      // 生成时间轴段落
      let currentTime = 0;
      segments.value = sentences.map((sentence, index) => {
        // 估算持续时间，中文语音每字约0.3秒
        const duration = sentence.length * 0.3;
        
        const segment = {
          startTime: currentTime,
          endTime: currentTime + duration,
          text: sentence.trim(),
          translatedText: index < translatedSentences.length ? translatedSentences[index] : ''
        };
        
        currentTime += duration;
        return segment;
      });
    };

    // 添加反馈相关状态
    const sendingFeedback = ref(false);
    
    // 发送翻译缺失问题反馈
    const sendTranslationFeedback = async () => {
      if (!currentTranslationId.value) {
        message.error('无法发送反馈：任务ID不存在');
        return;
      }
      
      sendingFeedback.value = true;
      
      try {
        const feedbackData = {
          task_id: currentTranslationId.value,
          issue_type: 'incomplete_translation',
          details: {
            total_segments: segments.value.length,
            missing_segments: segments.value.filter(s => s.translatedText.includes('翻译缺失')).length,
            translation_text: translatedText.value,
            source_text: transcript.value
          },
          user_message: '翻译不完整，部分段落缺失翻译',
          timestamp: new Date().toISOString()
        };
        
        // 发送反馈
        await axios.post('/api/feedback/translation', feedbackData);
        
        message.success('反馈已发送，感谢您的反馈！我们将尽快解决此问题');
        
        // 建议用户尝试分割音频
        setTimeout(() => {
          message.info('为获得完整翻译，建议将长音频分割成较小文件（每段不超过1分钟）后重新翻译');
        }, 2000);
      } catch (error) {
        console.error('发送反馈失败:', error);
        message.error('发送反馈失败：' + (error.message || '网络错误'));
      } finally {
        sendingFeedback.value = false;
      }
    };

    // 初始化表单状态
    onMounted(async () => {
      // 标准化语言代码
      formState.sourceLanguage = normalizeLanguageCode(formState.sourceLanguage);
      formState.targetLanguage = normalizeLanguageCode(formState.targetLanguage);
      
      // 其他初始化代码...
    });

    // 处理API请求前语言代码转换
    const prepareAPIRequest = () => {
      // 转换为后端接受的语言代码格式
      const apiData = {
        ...formState,
        sourceLanguage: toSimpleCode(formState.sourceLanguage),
        targetLanguage: toSimpleCode(formState.targetLanguage)
      };
      
      // 使用apiData发送请求...
    };

    // 处理API响应的语言代码转换
    const processAPIResponse = (response) => {
      if (response.detectedLanguage) {
        detectedLanguage.value = toRegionalCode(response.detectedLanguage);
      }
      
      // 处理其他响应数据...
    };

    return {
      formRef,
      formState,
      rules,
      audioList,
      translating,
      translationStatus,
      uploadProgress,
      transcriptionProgress,
      translationProgress,
      transcript,
      transcriptLength,
      translatedText,
      translatedAudioUrl,
      detectedLanguage,
      segments,
      activeTabKey,
      estimatedTime,
      // 语言相关
      languages,
      sourceLanguageOptions,
      targetLanguageOptions,
      domainOptions,
      outputFormatOptions,
      beforeAudioUpload,
      handleAudioChange,
      handleAudioLoadError,
      getLanguageName,
      formatTime,
      copyText,
      downloadAudio,
      saveToHistory,
      shareTranslation,
      resetForm,
      startTranslation,
      currentTranslationId,
      simulateProgress,
      errorMessage,
      processingTime,
      micTesting,
      micResult,
      audioFileInfo,
      testMicrophone,
      analyzeAudioFile,
      showFormatConverterGuide,
      audioQualityInfo,
      analyzeAudioQuality,
      loading,
      testAudioAPI,
      moveToBackground,
      generateSegmentsWithTranslation,
      assignTranslationToSegments,
      assignTranslationsToAllSegments,
      
      // 添加任务管理相关变量和方法
      showTaskManager,
      taskMode,
      taskCompleted,
      handleTaskCompleted,
      handleTaskFailed,
      handleTaskCanceled,
      handleModeChanged,
      handleAddToNotifications,
      navigateToTaskCenter,
      handleTaskClosed,
      sendTranslationFeedback,
      sendingFeedback
    };
  }
});
</script>

<style scoped>
.option-note {
  margin-left: 8px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
}

.status-container,
.result-container,
.error-container {
  min-height: 300px;
  display: flex;
  flex-direction: column;
}

.loading-content {
  padding: 32px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.text-result {
  margin-bottom: 24px;
}

.transcript-section,
.translation-section {
  margin-bottom: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.section-header h3 {
  margin: 0;
}

.audio-player-container {
  margin-bottom: 24px;
  text-align: center;
}

.audio-player {
  width: 100%;
  margin-bottom: 16px;
}

.timeline-result {
  margin-top: 16px;
}

.segment-container {
  margin-bottom: 10px;
}

.segment-time {
  font-weight: bold;
  margin-bottom: 5px;
  color: #1890ff;
}

.segment-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-left: 5px;
  border-left: 2px solid #f0f0f0;
}

.segment-original {
  font-weight: normal;
  color: #333;
}

.segment-translated {
  font-style: italic;
  color: #096dd9;
}

.result-actions {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

.tips-card {
  margin-top: 0;
}

.warning-text {
  color: #fa8c16;
  margin-top: 10px;
  font-size: 14px;
}

.error-container {
  padding: 20px;
  min-height: 200px;
}

.troubleshooting-guide {
  margin-top: 20px;
}

.audio-test {
  margin-top: 20px;
}

.format-converter-guide {
  padding: 20px;
}

.format-converter-guide h3 {
  margin-bottom: 16px;
}

.format-converter-guide p {
  margin-bottom: 12px;
}

.format-converter-guide ul {
  margin-bottom: 16px;
}

.format-converter-guide li {
  margin-bottom: 8px;
}

.format-converter-guide a {
  color: #1890ff;
  text-decoration: none;
}

.format-converter-guide a:hover {
  text-decoration: underline;
}

.format-support-info {
  margin-top: 16px;
}

.additional-tools {
  margin-top: 20px;
}

.audio-analysis-result {
  margin-bottom: 16px;
}

.audio-score {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.score-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 16px;
  margin-right: 20px;
}

.high-score {
  background-color: #52c41a;
  color: white;
}

.medium-score {
  background-color: #faad14;
  color: white;
}

.low-score {
  background-color: #f5222d;
  color: white;
}

.score-text {
  font-size: 14px;
  line-height: 1.5;
}

.recommendations .ant-list-item {
  padding: 8px 16px;
}

.analyze-audio-container {
  display: flex;
  justify-content: flex-end;
}

.action-buttons {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

.action-buttons .ant-btn {
  display: flex;
  align-items: center;
}

.move-background-btn {
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  padding: 0 20px;
  transition: all 0.3s;
}

.move-background-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 添加任务管理相关样式 */
.task-manager-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  background-color: rgba(0, 0, 0, 0.5);
}

.background-transition-enter-active,
.background-transition-leave-active {
  transition: opacity 0.3s ease;
}

.background-transition-enter-from,
.background-transition-leave-to {
  opacity: 0;
}

/* 添加新的按钮样式 */
.move-background-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 24px;
  border-radius: 20px;
  background: linear-gradient(135deg, #1890ff, #096dd9);
  box-shadow: 0 4px 10px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
  border: none;
  margin-top: 12px;
  height: 46px;
}

.move-background-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(24, 144, 255, 0.4);
  background: linear-gradient(135deg, #40a9ff, #1890ff);
}

.move-background-btn .anticon {
  font-size: 16px;
}

.move-background-btn .btn-text {
  margin: 0 8px;
  font-weight: 500;
  font-size: 15px;
}

/* 添加反馈相关样式 */
.feedback-section {
  margin-top: 24px;
  border-radius: 6px;
  overflow: hidden;
}

.feedback-section .ant-alert {
  margin-bottom: 0;
}

.feedback-section ol {
  margin-bottom: 16px;
}

.feedback-section li {
  margin-bottom: 8px;
}

.feedback-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
}
</style> 