#!/usr/bin/env python3
"""
测试文件上传功能
"""

import requests
import json
import os

def test_file_upload():
    """测试文件上传功能"""
    
    # 创建一个测试Excel文件
    test_data = """姓名,年龄,城市
张三,25,北京
李四,30,上海
王五,28,广州"""
    
    # 保存为CSV文件（更容易创建）
    test_file_path = "test_upload.csv"
    with open(test_file_path, 'w', encoding='utf-8') as f:
        f.write(test_data)
    
    print(f"📁 创建测试文件: {test_file_path}")
    
    # 测试文件上传
    url = "http://localhost:8000/api/v1/ai-agent/upload-file"
    
    try:
        with open(test_file_path, 'rb') as f:
            files = {'file': (test_file_path, f, 'text/csv')}
            
            print(f"🚀 开始上传文件到: {url}")
            response = requests.post(url, files=files, timeout=30)
            
            print(f"📊 响应状态码: {response.status_code}")
            print(f"📋 响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 上传成功!")
                print(f"📄 响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
            else:
                print(f"❌ 上传失败!")
                print(f"📄 响应内容: {response.text}")
                
    except requests.exceptions.ConnectionError as e:
        print(f"❌ 连接错误: {e}")
    except requests.exceptions.Timeout as e:
        print(f"❌ 请求超时: {e}")
    except Exception as e:
        print(f"❌ 其他错误: {e}")
    finally:
        # 清理测试文件
        if os.path.exists(test_file_path):
            os.remove(test_file_path)
            print(f"🗑️ 清理测试文件: {test_file_path}")

def test_agent_update():
    """测试智能体更新功能"""
    
    url = "http://localhost:8000/api/v1/agents/e55f5e84-6d8b-4265-8e55-728bdb0d2455"
    
    data = {
        "name": "测试更新的智能体",
        "description": "这是一个测试更新的描述",
        "systemPrompt": "你是一个测试助手"
    }
    
    try:
        print(f"🚀 开始更新智能体: {url}")
        response = requests.put(url, json=data, timeout=10)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 更新成功!")
            print(f"📄 响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ 更新失败!")
            print(f"📄 响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    print("🧪 开始测试AI智能体功能")
    print("=" * 50)
    
    print("\n1️⃣ 测试智能体更新功能")
    test_agent_update()
    
    print("\n2️⃣ 测试文件上传功能")
    test_file_upload()
    
    print("\n✅ 测试完成!")
