"""
文档翻译异步任务
"""

import os
import time
import logging
import asyncio
from pathlib import Path
from typing import Dict, Any, Optional

from ..core.celery_unified import celery_app
from ..core.database import get_db_manager
from ..services.translation_service import TranslationService

logger = logging.getLogger(__name__)


def smart_split_text(text: str, max_chunk_size: int = 800) -> list:
    """
    智能分段文本，确保每段不超过指定长度
    对于文档翻译，使用较小的分段以便更好的翻译质量和进度跟踪
    """
    if not text.strip():
        return []

    # 对于较短的文本，也要尝试分段以便更好的翻译质量
    # 不再直接返回整个文本

    # 1. 首先尝试按双换行符分段（标准段落）
    paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]

    # 2. 检查是否有段落过长
    result_chunks = []
    for paragraph in paragraphs:
        if len(paragraph) <= max_chunk_size:
            result_chunks.append(paragraph)
        else:
            # 段落太长，进一步分割
            sub_chunks = split_long_paragraph(paragraph, max_chunk_size)
            result_chunks.extend(sub_chunks)

    # 3. 如果第一步没有分出多个段落，尝试按单换行符分段
    if len(result_chunks) <= 1:
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        result_chunks = []
        current_chunk = ""

        for line in lines:
            if len(current_chunk + "\n" + line) <= max_chunk_size:
                current_chunk = current_chunk + "\n" + line if current_chunk else line
            else:
                if current_chunk:
                    result_chunks.append(current_chunk)
                current_chunk = line

        if current_chunk:
            result_chunks.append(current_chunk)

    # 4. 最后检查是否还有过长的块
    final_chunks = []
    for chunk in result_chunks:
        if len(chunk) <= max_chunk_size:
            final_chunks.append(chunk)
        else:
            # 强制按字符数分割
            sub_chunks = split_by_chars(chunk, max_chunk_size)
            final_chunks.extend(sub_chunks)

    return final_chunks


def split_long_paragraph(paragraph: str, max_size: int) -> list:
    """分割过长的段落"""
    # 尝试按句子分割
    sentences = []
    current_sentence = ""

    for char in paragraph:
        current_sentence += char
        if char in '.!?。！？':
            sentences.append(current_sentence.strip())
            current_sentence = ""

    if current_sentence.strip():
        sentences.append(current_sentence.strip())

    # 组合句子到合适的块大小
    chunks = []
    current_chunk = ""

    for sentence in sentences:
        if len(current_chunk + " " + sentence) <= max_size:
            current_chunk = current_chunk + " " + sentence if current_chunk else sentence
        else:
            if current_chunk:
                chunks.append(current_chunk)
            current_chunk = sentence

    if current_chunk:
        chunks.append(current_chunk)

    return chunks


def split_by_chars(text: str, max_size: int) -> list:
    """按字符数强制分割文本"""
    chunks = []
    for i in range(0, len(text), max_size):
        chunk = text[i:i + max_size]
        if chunk.strip():
            chunks.append(chunk.strip())
    return chunks


def update_task_status(task_id: str, status: str, progress: int, message: str, result_path: Optional[str] = None, word_count: Optional[int] = None, detected_source_lang: Optional[str] = None):
    """更新任务状态"""
    try:
        db_manager = get_db_manager()

        if hasattr(db_manager, 'use_postgres') and db_manager.use_postgres:
            if result_path and word_count is not None and detected_source_lang:
                update_sql = """
                UPDATE document_translation_tasks
                SET status = %s, progress = %s, message = %s, result_path = %s, word_count = %s, source_language = %s, updated_at = NOW()
                WHERE task_id = %s
                """
                params = (status, progress, message, result_path, word_count, detected_source_lang, task_id)
            elif result_path and word_count is not None:
                update_sql = """
                UPDATE document_translation_tasks
                SET status = %s, progress = %s, message = %s, result_path = %s, word_count = %s, updated_at = NOW()
                WHERE task_id = %s
                """
                params = (status, progress, message, result_path, word_count, task_id)
            elif result_path:
                update_sql = """
                UPDATE document_translation_tasks
                SET status = %s, progress = %s, message = %s, result_path = %s, updated_at = NOW()
                WHERE task_id = %s
                """
                params = (status, progress, message, result_path, task_id)
            else:
                update_sql = """
                UPDATE document_translation_tasks
                SET status = %s, progress = %s, message = %s, updated_at = NOW()
                WHERE task_id = %s
                """
                params = (status, progress, message, task_id)
        else:
            if result_path and word_count is not None and detected_source_lang:
                update_sql = """
                UPDATE document_translation_tasks
                SET status = ?, progress = ?, message = ?, result_path = ?, word_count = ?, source_language = ?, updated_at = datetime('now')
                WHERE task_id = ?
                """
                params = (status, progress, message, result_path, word_count, detected_source_lang, task_id)
            elif result_path and word_count is not None:
                update_sql = """
                UPDATE document_translation_tasks
                SET status = ?, progress = ?, message = ?, result_path = ?, word_count = ?, updated_at = datetime('now')
                WHERE task_id = ?
                """
                params = (status, progress, message, result_path, word_count, task_id)
            elif result_path:
                update_sql = """
                UPDATE document_translation_tasks
                SET status = ?, progress = ?, message = ?, result_path = ?, updated_at = datetime('now')
                WHERE task_id = ?
                """
                params = (status, progress, message, result_path, task_id)
            else:
                update_sql = """
                UPDATE document_translation_tasks
                SET status = ?, progress = ?, message = ?, updated_at = datetime('now')
                WHERE task_id = ?
                """
                params = (status, progress, message, task_id)

        db_manager.execute_query(update_sql, params)
        logger.info(f"任务状态更新: {task_id} -> {status} ({progress}%): {message}")

    except Exception as e:
        logger.error(f"更新任务状态失败: {e}")


def extract_excel_content(file_path: str) -> str:
    """
    专门处理Excel文件，保持表格结构并合理分段
    """
    try:
        import pandas as pd
        import json

        # 读取Excel文件
        df = pd.read_excel(file_path)

        # 保存Excel结构信息到全局变量（用于后续格式还原）
        excel_structure = {
            'columns': df.columns.tolist(),
            'shape': df.shape,
            'file_path': file_path
        }

        # 将结构信息保存到临时文件
        structure_file = file_path.replace('.xlsx', '_structure.json').replace('.xls', '_structure.json')
        with open(structure_file, 'w', encoding='utf-8') as f:
            json.dump(excel_structure, f, ensure_ascii=False, indent=2)

        # 按行提取内容，每行作为一个翻译单元
        text_segments = []
        for index, row in df.iterrows():
            # 过滤空值并组合单元格内容
            row_cells = []
            for cell in row.values:
                if pd.notna(cell) and str(cell).strip():
                    row_cells.append(str(cell).strip())

            if row_cells:  # 只有非空行才添加
                # 每行作为一个段落，用特殊分隔符标记
                row_text = ' | '.join(row_cells)
                text_segments.append(f"[ROW_{index}] {row_text}")

        # 用双换行符连接，便于后续分段
        return '\n\n'.join(text_segments)

    except ImportError:
        logger.warning("pandas 未安装，无法处理 Excel 文件")
        return "无法处理 Excel 文件：缺少 pandas 库"
    except Exception as e:
        logger.error(f"处理Excel文件失败: {e}")
        return f"处理Excel文件失败: {str(e)}"


def extract_text_from_file(file_path: str) -> str:
    """从文件中提取文本内容"""
    try:
        file_ext = Path(file_path).suffix.lower()
        
        if file_ext == '.txt':
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        
        elif file_ext in ['.docx']:
            try:
                import docx
                doc = docx.Document(file_path)
                text_content = []
                for paragraph in doc.paragraphs:
                    if paragraph.text.strip():
                        text_content.append(paragraph.text.strip())
                return '\n\n'.join(text_content)
            except ImportError:
                logger.warning("python-docx 未安装，无法处理 .docx 文件")
                return "无法处理 .docx 文件：缺少 python-docx 库"
        
        elif file_ext in ['.xlsx', '.xls']:
            return extract_excel_content(file_path)
        
        elif file_ext == '.pdf':
            try:
                import PyPDF2
                with open(file_path, 'rb') as f:
                    reader = PyPDF2.PdfReader(f)
                    text_content = []
                    for page in reader.pages:
                        text = page.extract_text()
                        if text.strip():
                            text_content.append(text.strip())
                    return '\n\n'.join(text_content)
            except ImportError:
                logger.warning("PyPDF2 未安装，无法处理 PDF 文件")
                return "无法处理 PDF 文件：缺少 PyPDF2 库"
        
        else:
            return f"不支持的文件格式: {file_ext}"
    
    except Exception as e:
        logger.error(f"提取文件内容失败: {e}")
        return f"提取文件内容失败: {str(e)}"


def save_translated_content(content: str, original_path: str, task_id: str) -> str:
    """保存翻译后的内容，支持Excel格式还原"""
    try:
        original_file = Path(original_path)
        result_dir = Path("storage/documents/results")
        result_dir.mkdir(parents=True, exist_ok=True)

        # 检查是否是Excel文件
        if original_file.suffix.lower() in ['.xlsx', '.xls']:
            return save_excel_translation(content, original_path, task_id, result_dir)
        else:
            # 其他格式保存为TXT
            result_filename = f"{task_id}_translated_{original_file.stem}.txt"
            result_path = result_dir / result_filename

            with open(result_path, 'w', encoding='utf-8') as f:
                f.write(content)

            absolute_path = os.path.abspath(str(result_path))
            logger.info(f"翻译结果已保存到: {absolute_path}")
            return absolute_path

    except Exception as e:
        logger.error(f"保存翻译结果失败: {e}")
        raise


def save_excel_translation(content: str, original_path: str, task_id: str, result_dir: Path) -> str:
    """保存Excel翻译结果，保持原格式"""
    try:
        import pandas as pd
        import json

        original_file = Path(original_path)

        # 读取结构信息
        structure_file = original_path.replace('.xlsx', '_structure.json').replace('.xls', '_structure.json')
        if os.path.exists(structure_file):
            with open(structure_file, 'r', encoding='utf-8') as f:
                excel_structure = json.load(f)
        else:
            logger.warning("未找到Excel结构信息，使用默认处理")
            excel_structure = None

        # 解析翻译内容
        translated_rows = []
        for line in content.split('\n\n'):
            line = line.strip()
            if line.startswith('[ROW_') and '] ' in line:
                # 提取行内容
                row_content = line.split('] ', 1)[1]
                # 分割单元格内容
                cells = [cell.strip() for cell in row_content.split(' | ')]
                translated_rows.append(cells)

        if not translated_rows:
            # 如果解析失败，按行分割
            translated_rows = [line.strip().split(' | ') for line in content.split('\n') if line.strip()]

        # 创建DataFrame
        if excel_structure and 'columns' in excel_structure:
            # 使用原始列名
            max_cols = len(excel_structure['columns'])
            # 确保每行都有足够的列
            for row in translated_rows:
                while len(row) < max_cols:
                    row.append('')

            df = pd.DataFrame(translated_rows, columns=excel_structure['columns'])
        else:
            # 自动生成列名
            max_cols = max(len(row) for row in translated_rows) if translated_rows else 1
            columns = [f'Column_{i+1}' for i in range(max_cols)]

            # 确保每行都有足够的列
            for row in translated_rows:
                while len(row) < max_cols:
                    row.append('')

            df = pd.DataFrame(translated_rows, columns=columns)

        # 保存为Excel文件
        result_filename = f"{task_id}_translated_{original_file.stem}.xlsx"
        result_path = result_dir / result_filename

        df.to_excel(result_path, index=False, engine='openpyxl')

        absolute_path = os.path.abspath(str(result_path))
        logger.info(f"Excel翻译结果已保存到: {absolute_path}")

        # 清理临时结构文件
        if os.path.exists(structure_file):
            os.remove(structure_file)

        return absolute_path

    except ImportError:
        logger.warning("pandas或openpyxl未安装，保存为TXT格式")
        # 降级为TXT保存
        result_filename = f"{task_id}_translated_{Path(original_path).stem}.txt"
        result_path = result_dir / result_filename

        with open(result_path, 'w', encoding='utf-8') as f:
            f.write(content)

        return os.path.abspath(str(result_path))

    except Exception as e:
        logger.error(f"保存Excel翻译结果失败: {e}")
        raise


@celery_app.task(bind=True, name="translate_document_v2", max_retries=3, default_retry_delay=60)
def translate_document_v2(self, task_id: str, file_path: str, source_lang: str, target_lang: str,
                         domain: str = "general", style: str = "standard"):
    """
    异步文档翻译任务
    """
    start_time = time.time()
    
    try:
        logger.info(f"开始文档翻译任务: {task_id}")
        update_task_status(task_id, "processing", 10, "开始处理文档...")
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            raise Exception(f"文件不存在: {file_path}")
        
        # 提取文档内容
        update_task_status(task_id, "processing", 20, "正在提取文档内容...")
        text_content = extract_text_from_file(file_path)
        
        if not text_content or text_content.startswith("无法处理") or text_content.startswith("不支持"):
            raise Exception(f"文档内容提取失败: {text_content}")
        
        logger.info(f"提取的文档内容长度: {len(text_content)} 字符")
        
        # 分段处理长文档
        update_task_status(task_id, "processing", 30, "正在准备翻译...")

        # 智能分段：优先按段落分，如果段落太长则按句子分，最后按字符数分
        paragraphs = smart_split_text(text_content)
        total_paragraphs = len(paragraphs)

        if total_paragraphs == 0:
            raise Exception("文档内容为空")

        logger.info(f"文档智能分为 {total_paragraphs} 个段落，平均长度: {len(text_content) // total_paragraphs} 字符")
        
        # 创建翻译服务实例
        translation_service = TranslationService()
        translated_paragraphs = []
        detected_source_lang = source_lang  # 保存检测到的源语言

        # 逐段翻译
        for i, paragraph in enumerate(paragraphs):
            if not paragraph.strip():
                translated_paragraphs.append("")
                continue
            
            # 更新进度
            progress = 30 + int(60 * (i / total_paragraphs))
            update_task_status(task_id, "processing", progress, f"正在翻译第 {i+1}/{total_paragraphs} 段...")
            
            # 创建事件循环进行异步翻译
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                result = loop.run_until_complete(
                    translation_service.translate_text(
                        text=paragraph,
                        source_lang=source_lang,
                        target_lang=target_lang,
                        domain=domain,
                        style=style
                    )
                )
                
                if result.get("success"):
                    translated_text = result.get("translated_text", paragraph)
                    translated_paragraphs.append(translated_text)

                    # 获取检测到的源语言（只在第一段时更新）
                    if i == 0 and result.get("source_language") and result.get("source_language") != source_lang:
                        detected_source_lang = result.get("source_language")
                        logger.info(f"检测到源语言: {detected_source_lang}")

                    logger.info(f"段落 {i+1} 翻译完成")
                else:
                    error_msg = result.get('error', '未知错误')
                    logger.error(f"段落 {i+1} 翻译失败: {error_msg}")
                    # 翻译失败时，抛出异常而不是使用原文
                    raise Exception(f"翻译失败: {error_msg}")

            except Exception as e:
                logger.error(f"段落 {i+1} 翻译异常: {e}")
                # 翻译异常时，抛出异常而不是使用原文
                raise Exception(f"翻译异常: {str(e)}")
            finally:
                loop.close()
            
            # 添加小延迟，避免请求过快
            time.sleep(0.5)
        
        # 合并翻译结果
        update_task_status(task_id, "processing", 90, "正在保存翻译结果...")
        translated_content = '\n\n'.join(translated_paragraphs)
        
        # 保存翻译结果和原文对照
        result_path = save_translated_content(translated_content, file_path, task_id)

        # 保存原文和译文的对照数据
        bilingual_data = []
        for i, (source_para, translated_para) in enumerate(zip(paragraphs, translated_paragraphs)):
            bilingual_data.append({
                "id": i + 1,
                "source": source_para.strip(),
                "translated": translated_para.strip()
            })

        # 保存对照数据为JSON文件
        import json
        bilingual_path = result_path.replace('.txt', '_bilingual.json')
        with open(bilingual_path, 'w', encoding='utf-8') as f:
            json.dump(bilingual_data, f, ensure_ascii=False, indent=2)

        logger.info(f"双语对照数据已保存到: {bilingual_path}")
        
        # 计算翻译时间和字数统计
        translation_time = time.time() - start_time
        word_count = len(text_content.split())  # 简单的字数统计

        # 更新任务完成状态，包含字数统计和检测到的源语言
        update_task_status(
            task_id,
            "completed",
            100,
            f"翻译完成！用时 {translation_time:.1f} 秒，共翻译 {total_paragraphs} 段，约 {word_count} 词",
            result_path,
            word_count,
            detected_source_lang if detected_source_lang != source_lang else None
        )
        
        logger.info(f"文档翻译任务完成: {task_id}, 用时: {translation_time:.2f}秒")
        
        return {
            "success": True,
            "task_id": task_id,
            "result_path": result_path,
            "paragraphs_count": total_paragraphs,
            "translation_time": translation_time
        }
        
    except Exception as e:
        error_message = f"翻译失败: {str(e)}"
        logger.error(f"文档翻译任务失败: {task_id}, 错误: {e}")
        
        # 更新失败状态
        update_task_status(task_id, "failed", 0, error_message)
        
        # 如果还有重试次数，进行重试
        if self.request.retries < self.max_retries:
            logger.info(f"任务 {task_id} 将在 {self.default_retry_delay} 秒后重试 (第 {self.request.retries + 1} 次)")
            raise self.retry(countdown=self.default_retry_delay, exc=e)
        
        return {
            "success": False,
            "task_id": task_id,
            "error": error_message
        }
